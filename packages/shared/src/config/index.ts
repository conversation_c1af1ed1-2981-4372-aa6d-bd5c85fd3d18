/**
 * 配置常量和默认值
 */

// API相关配置
export const API_CONFIG = {
  BASE_URL: 'http://localhost:3001',
  TIMEOUT: 30_000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// 文件上传配置
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
  ],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
  CHUNK_SIZE: 1024 * 1024, // 1MB
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 60 * 60 * 1000, // 1小时
  MAX_TTL: 24 * 60 * 60 * 1000, // 24小时
  MEMORY_LIMIT: 100 * 1024 * 1024, // 100MB
} as const;

// WebSocket配置
export const WEBSOCKET_CONFIG = {
  RECONNECT_INTERVAL: 5000,
  MAX_RECONNECT_ATTEMPTS: 10,
  HEARTBEAT_INTERVAL: 30_000,
} as const;

// 主题配置
export const THEME_CONFIG = {
  STORAGE_KEY: 'flexihub-theme',
  CSS_VARIABLE_PREFIX: '--fh',
  RESPONSIVE_BREAKPOINTS: {
    mobile: 640,
    tablet: 768,
    desktop: 1024,
    wide: 1280,
  },
} as const;

// 组件配置
export const COMPONENT_CONFIG = {
  MAX_NESTING_DEPTH: 10,
  AUTO_SAVE_INTERVAL: 30_000, // 30秒
  UNDO_HISTORY_LIMIT: 50,
} as const;

// 验证配置
export const VALIDATION_CONFIG = {
  PASSWORD_MIN_LENGTH: 8,
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 32,
  SLUG_MAX_LENGTH: 255,
  TITLE_MAX_LENGTH: 255,
  DESCRIPTION_MAX_LENGTH: 1000,
} as const;

// 响应式预览配置
export const RESPONSIVE_PREVIEW_CONFIG = {
  DEVICES: {
    mobile: [
      { name: 'iPhone SE', width: 375, height: 667 },
      { name: 'iPhone 12', width: 414, height: 896 },
      { name: 'Samsung Galaxy S21', width: 360, height: 800 },
    ],
    tablet: [
      { name: 'iPad Mini', width: 768, height: 1024 },
      { name: 'iPad Air', width: 834, height: 1194 },
      { name: 'iPad Pro', width: 1024, height: 1366 },
    ],
    desktop: [
      { name: 'MacBook Air', width: 1280, height: 832 },
      { name: 'MacBook Pro', width: 1440, height: 900 },
      { name: 'Desktop', width: 1920, height: 1080 },
    ],
  },
  DEFAULT_DEVICE: {
    mobile: 'iPhone 12',
    tablet: 'iPad Air',
    desktop: 'MacBook Pro',
  },
} as const;

// 权限配置
export const PERMISSION_CONFIG = {
  RESOURCES: {
    WEBSITE: 'website',
    PAGE: 'page',
    COMPONENT: 'component',
    USER: 'user',
    TENANT: 'tenant',
    MEDIA: 'media',
  },
  ACTIONS: {
    CREATE: 'create',
    READ: 'read',
    UPDATE: 'update',
    DELETE: 'delete',
    PUBLISH: 'publish',
    MANAGE: 'manage',
  },
  SCOPES: {
    ALL: 'all',
    OWN: 'own',
    ASSIGNED: 'assigned',
    NONE: 'none',
  },
} as const;

// 错误代码配置
export const ERROR_CODES = {
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',

  // 验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  REQUIRED_FIELD: 'REQUIRED_FIELD',

  // 资源错误
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',

  // 服务器错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  NETWORK_ERROR: 'NETWORK_ERROR',

  // 业务错误
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
} as const;

// 状态配置
export const STATUS_CONFIG = {
  CONTENT_STATUS: {
    DRAFT: 'draft',
    PUBLISHED: 'published',
    ARCHIVED: 'archived',
  },
  USER_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    SUSPENDED: 'suspended',
  },
  TENANT_STATUS: {
    ACTIVE: 'active',
    SUSPENDED: 'suspended',
    INACTIVE: 'inactive',
  },
} as const;
