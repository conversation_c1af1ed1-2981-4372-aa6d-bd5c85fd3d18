/**
 * API响应和通用类型定义
 */

export interface APIError {
  code: string;
  details?: any;
  field?: string;
  message: string;
  stack?: string;
}

export interface PaginationMeta {
  hasNext: boolean;
  hasPrev: boolean;
  limit: number;
  page: number;
  total: number;
  totalPages: number;
}

export interface SortMeta {
  field: string;
  order: 'asc' | 'desc';
}

export interface APIMeta {
  cached?: boolean;
  cacheExpiry?: string;
  filters?: Record<string, any>;
  pagination?: PaginationMeta;
  sort?: SortMeta;
  total?: number;
  version?: string;
}

export interface APIResponse<T = any> {
  data?: T;
  error?: APIError;
  meta?: APIMeta;
  requestId: string;
  success: boolean;
  timestamp: string;
}

// 通用列表查询参数
export interface ListQueryParams {
  filters?: Record<string, any>;
  limit?: number;
  page?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 批量操作相关类型
export interface BulkOperationError<T = any> {
  error: APIError;
  index: number;
  item: T;
}

export interface BulkOperation<T = any> {
  action: 'create' | 'delete' | 'update';
  items: T[];
}

export interface BulkOperationResult<T = any> {
  failed: BulkOperationError<T>[];
  failureCount: number;
  successCount: number;
  successful: T[];
  totalCount: number;
}

// 文件上传相关类型
export interface FileUploadRequest {
  allowedTypes?: string[];
  category?: 'audio' | 'document' | 'image' | 'other' | 'video';
  file: File;
  maxSize?: number;
}

export interface FileUploadResponse {
  fileName: string;
  id: string;
  metadata?: Record<string, any>;
  mimeType: string;
  originalName: string;
  size: number;
  thumbnailUrl?: string;
  uploadedAt: string;
  url: string;
}

export interface FileDeleteRequest {
  fileId: string;
  permanent?: boolean;
}

// 搜索相关类型
export interface SearchFilter {
  field: string;
  operator:
    | 'contains'
    | 'endsWith'
    | 'eq'
    | 'gt'
    | 'gte'
    | 'in'
    | 'lt'
    | 'lte'
    | 'ne'
    | 'nin'
    | 'startsWith';
  value: any;
}

export interface SearchResultItem<T = any> {
  highlights?: Record<string, string[]>;
  item: T;
  score: number;
}

export interface SearchFacetValue {
  count: number;
  selected?: boolean;
  value: any;
}

export interface SearchFacet {
  field: string;
  values: SearchFacetValue[];
}

export interface SearchQuery {
  facets?: string[];
  filters?: SearchFilter[];
  highlight?: boolean;
  pagination?: {
    limit: number;
    page: number;
  };
  q: string;
  sort?: SortMeta;
}

export interface SearchResult<T = any> {
  executionTime: number;
  facets?: SearchFacet[];
  items: SearchResultItem<T>[];
  query: string;
  suggestions?: string[];
  total: number;
}

// 导出导入相关类型
export interface ExportRequest {
  fields?: string[];
  fileName?: string;
  filters?: Record<string, any>;
  format: 'csv' | 'json' | 'xlsx' | 'xml';
}

export interface ExportResponse {
  downloadUrl: string;
  expiresAt: string;
  fileName: string;
  format: string;
  recordCount: number;
  size: number;
}

export interface ImportOptions {
  delimiter?: string;
  dryRun?: boolean;
  encoding?: string;
  onConflict?: 'replace' | 'skip' | 'update';
  skipHeader?: boolean;
}

export interface ImportError {
  error: string;
  field?: string;
  row: number;
  value?: any;
}

export interface ImportRequest {
  fileId: string;
  format: 'csv' | 'json' | 'xlsx' | 'xml';
  mapping?: Record<string, string>;
  options?: ImportOptions;
}

export interface ImportResponse {
  completedAt?: string;
  errors?: ImportError[];
  failedRecords?: number;
  jobId: string;
  processedRecords?: number;
  progress?: number;
  result?: any;
  startedAt?: string;
  status: 'completed' | 'failed' | 'pending' | 'processing';
  successfulRecords?: number;
  totalRecords?: number;
}

// WebSocket相关类型
export interface WebSocketMessage<T = any> {
  id: string;
  payload: T;
  timestamp: string;
  type: string;
}

export interface WebSocketResponse<T = any> {
  data?: T;
  error?: string;
  messageId?: string;
  success: boolean;
}

// 健康检查和状态相关
export interface ServiceStatus {
  lastCheck: string;
  message?: string;
  name: string;
  responseTime?: number;
  status: 'degraded' | 'healthy' | 'unhealthy';
}

export interface SystemInfo {
  cpu: {
    usage: number;
  };
  disk: {
    percentage: number;
    total: number;
    used: number;
  };
  memory: {
    percentage: number;
    total: number;
    used: number;
  };
}

export interface HealthCheckResponse {
  services: ServiceStatus[];
  status: 'degraded' | 'healthy' | 'unhealthy';
  system: SystemInfo;
  timestamp: string;
  uptime: number;
  version: string;
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = unknown> {
  code: number;
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T = unknown> {
  items: T[];
  limit: number;
  page: number;
  total: number;
  totalPages: number;
}

/**
 * API错误信息
 */
export interface ApiError {
  code: number;
  details?: Record<string, unknown>;
  message: string;
}

/**
 * 查询参数基础接口
 */
export interface BaseQueryParams {
  limit?: number;
  page?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
