/**
 * 用户认证和权限相关类型定义
 */

export interface Permission {
  action: string;
  conditions?: Record<string, any>;
  description?: string;
  id: string;
  isSystem: boolean;
  name: string;
  resource: string;
  scope: 'all' | 'assigned' | 'none' | 'own';
}

export interface Role {
  createdAt: string;
  description?: string;
  id: string;
  isSystem: boolean;
  level: 'editor' | 'member' | 'super_admin' | 'tenant_admin' | 'viewer';
  name: string;
  permissions: Permission[];
  tenantId?: string;
  updatedAt: string;
}

export interface TenantSettings {
  allowCustomDomains: boolean;
  branding: {
    customColors: boolean;
    customLogo: boolean;
    hidePoweredBy: boolean;
  };
  features: string[];
  maxStorage: number; // in bytes
  maxUsers: number;
  maxWebsites: number;
}

export interface TenantSubscription {
  cancelAtPeriodEnd: boolean;
  currentPeriodEnd: string;
  currentPeriodStart: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  trialEnd?: string;
}

export interface Tenant {
  createdAt: string;
  domain?: string;
  id: string;
  logo?: string;
  name: string;
  ownerId: string;
  settings: TenantSettings;
  slug: string;
  status: 'active' | 'inactive' | 'suspended';
  subscription: TenantSubscription;
  updatedAt: string;
}

export interface User {
  avatar?: string;
  createdAt: string;
  email: string;
  emailVerified: boolean;
  firstName?: string;
  id: string;
  lastLoginAt?: string;
  lastName?: string;
  permissions: Permission[];
  roles: Role[];
  status: 'active' | 'inactive' | 'suspended';
  tenantId: string;
  updatedAt: string;
  username?: string;
}

// JWT Token相关类型
export interface JWTPayload {
  aud: string;
  email: string;
  exp: number;
  iat: number;
  iss: string;
  permissions: string[];
  roles: string[];
  sub: string; // 用户ID
  tenantId: string;
}

export interface AuthTokens {
  accessToken: string;
  expiresIn: number;
  refreshToken: string;
  tokenType: 'Bearer';
}

// 认证请求和响应类型
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
  tenantSlug?: string;
}

export interface LoginResponse {
  tenant: Tenant;
  tokens: AuthTokens;
  user: User;
}

export interface RegisterRequest {
  email: string;
  firstName?: string;
  lastName?: string;
  password: string;
  tenantName?: string;
  tenantSlug?: string;
}

export interface RegisterResponse {
  emailVerificationRequired: boolean;
  tenant?: Tenant;
  tokens: AuthTokens;
  user: User;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ResetPasswordRequest {
  email: string;
  tenantSlug?: string;
}

export interface ConfirmResetPasswordRequest {
  password: string;
  token: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface VerifyEmailRequest {
  token: string;
}

// 权限检查相关类型
export interface PermissionCheck {
  action: string;
  context?: Record<string, any>;
  resource: string;
  resourceId?: string;
}

export interface PermissionResult {
  allowed: boolean;
  conditions?: Record<string, any>;
  reason?: string;
}

// 用户管理相关类型
export interface CreateUserRequest {
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  sendInvitation?: boolean;
}

export interface UpdateUserRequest {
  avatar?: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
  status?: User['status'];
}

export interface InviteUserRequest {
  email: string;
  message?: string;
  roles: string[];
}

export interface UserInvitation {
  createdAt: string;
  email: string;
  expiresAt: string;
  id: string;
  invitedBy: string;
  roles: string[];
  status: 'accepted' | 'expired' | 'pending';
  tenantId: string;
  token: string;
}

export interface AcceptInvitationRequest {
  firstName?: string;
  lastName?: string;
  password: string;
  token: string;
}
