/**
 * 用户信息
 */
export interface User {
  avatar?: string;
  createdAt: string;
  email: string;
  id: string;
  permissions: string[];
  role: 'admin' | 'editor' | 'user';
  updatedAt: string;
  username: string;
}

/**
 * 用户认证信息
 */
export interface AuthInfo {
  expiresAt: string;
  refreshToken?: string;
  token: string;
  user: User;
}

/**
 * 登录参数
 */
export interface LoginParams {
  password: string;
  remember?: boolean;
  username: string;
}

/**
 * 注册参数
 */
export interface RegisterParams {
  confirmPassword: string;
  email: string;
  password: string;
  username: string;
}
