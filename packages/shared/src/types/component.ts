/**
 * 组件相关类型定义
 */

export type PropType =
  | 'array'
  | 'boolean'
  | 'color'
  | 'date'
  | 'datetime'
  | 'email'
  | 'image'
  | 'json'
  | 'number'
  | 'object'
  | 'select'
  | 'string'
  | 'tel'
  | 'textarea'
  | 'url';

export interface PropValidation {
  custom?: (value: any) => boolean | string;
  email?: boolean;
  max?: number;
  maxLength?: number;
  min?: number;
  minLength?: number;
  pattern?: string;
  required?: boolean;
  url?: boolean;
}

export interface SelectOption {
  disabled?: boolean;
  group?: string;
  label: string;
  value: any;
}

export interface PropSchema {
  default: any;
  description?: string;
  disabled?: boolean;
  group?: string;
  help?: string;
  label: string;
  options?: SelectOption[];
  order?: number;
  placeholder?: string;
  type: PropType;
  validation?: PropValidation;
  visible?: boolean;
}

export interface ComponentPreview {
  demoProps?: Record<string, any>;
  description?: string;
  screenshots?: string[];
  thumbnail?: string;
}

export interface CraftRules {
  canDelete?: (node: any) => boolean;
  canDrag?: (node: any) => boolean;
  canDrop?: (node: any, target: any) => boolean;
  canEdit?: (node: any) => boolean;
  canMoveIn?: (node: any, target: any) => boolean;
  canMoveOut?: (node: any, target: any) => boolean;
}

export interface CraftRelated {
  settings?: any;
  toolbar?: any;
}

export interface CraftConfig {
  custom?: Record<string, any>;
  displayName?: string;
  props?: Record<string, any>;
  related?: CraftRelated;
  rules?: CraftRules;
}

export interface ComponentSchema {
  category:
    | 'complex'
    | 'content'
    | 'interactive'
    | 'layout'
    | 'media'
    | 'navigation';
  craft?: CraftConfig;
  defaultProps: Record<string, any>;
  deprecated?: boolean;
  description: string;
  experimental?: boolean;
  icon?: string;
  name: string;
  preview?: ComponentPreview;
  props: Record<string, PropSchema>;
  responsive?: boolean;
  tags?: string[];
  type: string;
}

// 组件实例相关类型
export interface ComponentInstance {
  children?: ComponentInstance[];
  classes?: string[];
  id: string;
  parent?: string;
  position: {
    index: number;
    zone?: string;
  };
  props: Record<string, any>;
  responsive?: {
    desktop?: Partial<ComponentInstance>;
    mobile?: Partial<ComponentInstance>;
    tablet?: Partial<ComponentInstance>;
  };
  style?: Record<string, any>;
  type: string;
}

// 组件注册和管理
export interface ComponentRegistry {
  [componentType: string]: ComponentSchema;
}

export interface ComponentCategory {
  components: string[];
  description?: string;
  icon?: string;
  id: string;
  name: string;
  order?: number;
}

// 组件查询和响应
export interface GetComponentsParams {
  category?: string;
  deprecated?: boolean;
  experimental?: boolean;
  search?: string;
  tags?: string[];
}

export interface ComponentListResponse {
  categories: ComponentCategory[];
  components: ComponentSchema[];
  total: number;
}

// 组件模板
export interface ComponentTemplate {
  category: string;
  createdAt: string;
  description?: string;
  id: string;
  name: string;
  preview?: ComponentPreview;
  structure: ComponentInstance;
  tags?: string[];
  updatedAt: string;
}

export interface CreateComponentTemplateRequest {
  category: string;
  description?: string;
  name: string;
  structure: ComponentInstance;
  tags?: string[];
}
