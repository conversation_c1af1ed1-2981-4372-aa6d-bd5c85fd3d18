/**
 * 页面相关类型定义
 */

export interface OpenGraphData {
  description?: string;
  image?: string;
  siteName?: string;
  title?: string;
  type?: string;
  url?: string;
}

export interface RobotsSettings {
  follow: boolean;
  index: boolean;
  noarchive?: boolean;
  noimageindex?: boolean;
  nosnippet?: boolean;
}

export interface PageMetadata {
  canonicalUrl?: string;
  description: string;
  keywords: string[];
  openGraph?: OpenGraphData;
  robots?: RobotsSettings;
  structuredData?: Record<string, any>;
  title: string;
}

export interface Page {
  content: any; // Craft.js序列化数据
  createdAt: string;
  createdBy: string;
  id: string;
  isHomePage: boolean;
  metadata: PageMetadata;
  order: number; // 页面排序
  parentId?: string; // 用于页面层级结构
  publishedAt?: string;
  slug: string;
  status: 'archived' | 'draft' | 'published';
  template?: string; // 页面模板
  title: string;
  updatedAt: string;
  version: number;
  websiteId: string;
}

export interface PageVersion {
  comment?: string;
  content: any;
  createdAt: string;
  createdBy: string;
  id: string;
  metadata: PageMetadata;
  pageId: string;
  version: number;
}

// 页面CRUD请求类型
export interface CreatePageRequest {
  content?: any;
  isHomePage?: boolean;
  metadata?: Partial<PageMetadata>;
  parentId?: string;
  slug: string;
  template?: string;
  title: string;
  websiteId: string;
}

export interface UpdatePageRequest {
  content?: any;
  isHomePage?: boolean;
  metadata?: Partial<PageMetadata>;
  order?: number;
  parentId?: string;
  slug?: string;
  status?: Page['status'];
  template?: string;
  title?: string;
}

export interface PublishPageRequest {
  comment?: string;
}

// 页面查询和响应类型
export interface GetPagesParams {
  limit?: number;
  page?: number;
  parentId?: string;
  search?: string;
  sortBy?: 'createdAt' | 'order' | 'title' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  status?: Page['status'];
  websiteId: string;
}

export interface PageListResponse {
  limit: number;
  page: number;
  pages: Page[];
  total: number;
}

// 页面树形结构
export interface PageTreeItem extends Page {
  children?: PageTreeItem[];
  depth: number;
}

/**
 * 页面数据结构
 */
export interface PageData {
  content: Record<string, unknown> | string;
  createdAt: string;
  id: string;
  publishedAt?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  seoTitle?: string;
  slug: string;
  status: 'archived' | 'draft' | 'published';
  title: string;
  updatedAt: string;
}

/**
 * Craft.js节点结构
 */
export interface CraftNode {
  linkedNodes?: Record<string, CraftNode>;
  nodes?: string[];
  parent?: string;
  props?: Record<string, unknown>;
  type: string;
}

/**
 * Craft.js内容结构
 */
export interface CraftContent {
  [nodeId: string]: CraftNode;
  ROOT: {
    nodes: string[];
    props?: Record<string, unknown>;
    type: string;
  };
}

/**
 * 页面查询参数
 */
export interface PageQueryParams {
  limit?: number;
  page?: number;
  search?: string;
  status?: PageData['status'];
  websiteId: string;
}

/**
 * 页面创建参数
 */
export interface CreatePageParams {
  content?: Record<string, unknown> | string;
  seoDescription?: string;
  seoKeywords?: string[];
  seoTitle?: string;
  slug: string;
  title: string;
  websiteId: string;
}

/**
 * 页面更新参数
 */
export interface UpdatePageParams extends Partial<CreatePageParams> {
  id: string;
}
