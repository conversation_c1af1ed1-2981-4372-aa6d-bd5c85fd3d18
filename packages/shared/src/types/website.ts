/**
 * 网站相关类型定义
 */

export interface ThemeSettings {
  accentColor?: string;
  borderRadius: 'lg' | 'md' | 'none' | 'sm' | 'xl';
  darkMode: boolean;
  fontFamily: string;
  headingFont?: string;
  primaryColor: string;
  secondaryColor: string;
  shadows: boolean;
}

export interface SEOSettings {
  description: string;
  favicon?: string;
  keywords: string[];
  ogImage?: string;
  robotsFollow: boolean;
  robotsIndex: boolean;
  title: string;
  twitterCard?: 'summary' | 'summary_large_image';
}

export interface AnalyticsSettings {
  customScripts?: string[];
  facebookPixelId?: string;
  googleAnalyticsId?: string;
  hotjarId?: string;
}

export interface CustomCodeSettings {
  bodyCode?: string;
  cssCode?: string;
  headCode?: string;
}

export interface WebsiteSettings {
  analytics?: AnalyticsSettings;
  customCode?: CustomCodeSettings;
  seo: SEOSettings;
  theme: ThemeSettings;
}

export interface Website {
  createdAt: string;
  createdBy: string;
  domain?: string;
  id: string;
  name: string;
  settings: WebsiteSettings;
  status: 'archived' | 'draft' | 'published';
  tenantId: string;
  updatedAt: string;
}

// 创建和更新网站的请求类型
export interface CreateWebsiteRequest {
  domain?: string;
  name: string;
  settings?: Partial<WebsiteSettings>;
}

export interface UpdateWebsiteRequest {
  domain?: string;
  name?: string;
  settings?: Partial<WebsiteSettings>;
  status?: Website['status'];
}
