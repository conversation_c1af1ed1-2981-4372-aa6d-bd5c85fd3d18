/**
 * 主题相关工具函数
 */

import type { ThemeSettings } from '../types/website';

// 默认主题配置
export const DEFAULT_THEME: ThemeSettings = {
  primaryColor: '#3B82F6',
  secondaryColor: '#64748B',
  accentColor: '#F59E0B',
  fontFamily: 'Inter, system-ui, sans-serif',
  headingFont: 'Inter, system-ui, sans-serif',
  borderRadius: 'md',
  shadows: true,
  darkMode: false,
};

// 预设主题
export const PRESET_THEMES: Record<string, ThemeSettings> = {
  modern: {
    ...DEFAULT_THEME,
    primaryColor: '#3B82F6',
    secondaryColor: '#64748B',
    accentColor: '#F59E0B',
    borderRadius: 'lg',
    shadows: true,
  },
  minimal: {
    ...DEFAULT_THEME,
    primaryColor: '#000000',
    secondaryColor: '#666666',
    accentColor: '#333333',
    borderRadius: 'none',
    shadows: false,
  },
  vibrant: {
    ...DEFAULT_THEME,
    primaryColor: '#EC4899',
    secondaryColor: '#8B5CF6',
    accentColor: '#F59E0B',
    borderRadius: 'xl',
    shadows: true,
  },
  nature: {
    ...DEFAULT_THEME,
    primaryColor: '#059669',
    secondaryColor: '#0D9488',
    accentColor: '#F59E0B',
    borderRadius: 'md',
    shadows: true,
  },
  corporate: {
    ...DEFAULT_THEME,
    primaryColor: '#1E40AF',
    secondaryColor: '#475569',
    accentColor: '#DC2626',
    borderRadius: 'sm',
    shadows: false,
  },
};

/**
 * 将十六进制颜色转换为RGB值
 */
export function hexToRgb(
  hex: string,
): null | { b: number; g: number; r: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result && result[1] && result[2] && result[3]
    ? {
        r: Number.parseInt(result[1], 16),
        g: Number.parseInt(result[2], 16),
        b: Number.parseInt(result[3], 16),
      }
    : null;
}

/**
 * 将RGB值转换为十六进制颜色
 */
export function rgbToHex(r: number, g: number, b: number): string {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
}

/**
 * 生成颜色的亮/暗变体
 */
export function generateColorVariants(color: string): {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
} {
  const rgb = hexToRgb(color);
  if (!rgb) {
    throw new Error('Invalid color format');
  }

  const { r, g, b } = rgb;

  const variants = {
    50: lightenColor(r, g, b, 0.95),
    100: lightenColor(r, g, b, 0.9),
    200: lightenColor(r, g, b, 0.8),
    300: lightenColor(r, g, b, 0.6),
    400: lightenColor(r, g, b, 0.3),
    500: color,
    600: darkenColor(r, g, b, 0.1),
    700: darkenColor(r, g, b, 0.2),
    800: darkenColor(r, g, b, 0.3),
    900: darkenColor(r, g, b, 0.4),
    950: darkenColor(r, g, b, 0.5),
  };

  return variants;
}

/**
 * 变亮颜色
 */
function lightenColor(r: number, g: number, b: number, factor: number): string {
  const newR = Math.round(r + (255 - r) * factor);
  const newG = Math.round(g + (255 - g) * factor);
  const newB = Math.round(b + (255 - b) * factor);
  return rgbToHex(
    Math.min(255, newR),
    Math.min(255, newG),
    Math.min(255, newB),
  );
}

/**
 * 变暗颜色
 */
function darkenColor(r: number, g: number, b: number, factor: number): string {
  const newR = Math.round(r * (1 - factor));
  const newG = Math.round(g * (1 - factor));
  const newB = Math.round(b * (1 - factor));
  return rgbToHex(Math.max(0, newR), Math.max(0, newG), Math.max(0, newB));
}

/**
 * 生成CSS变量对象
 */
export function generateCSSVariables(
  theme: ThemeSettings,
): Record<string, string> {
  const primaryVariants = generateColorVariants(theme.primaryColor);
  const secondaryVariants = generateColorVariants(theme.secondaryColor);
  const accentVariants = theme.accentColor
    ? generateColorVariants(theme.accentColor)
    : null;

  const variables: Record<string, string> = {
    // 主色调
    '--color-primary-50': primaryVariants[50],
    '--color-primary-100': primaryVariants[100],
    '--color-primary-200': primaryVariants[200],
    '--color-primary-300': primaryVariants[300],
    '--color-primary-400': primaryVariants[400],
    '--color-primary-500': primaryVariants[500],
    '--color-primary-600': primaryVariants[600],
    '--color-primary-700': primaryVariants[700],
    '--color-primary-800': primaryVariants[800],
    '--color-primary-900': primaryVariants[900],
    '--color-primary-950': primaryVariants[950],

    // 次要色调
    '--color-secondary-50': secondaryVariants[50],
    '--color-secondary-100': secondaryVariants[100],
    '--color-secondary-200': secondaryVariants[200],
    '--color-secondary-300': secondaryVariants[300],
    '--color-secondary-400': secondaryVariants[400],
    '--color-secondary-500': secondaryVariants[500],
    '--color-secondary-600': secondaryVariants[600],
    '--color-secondary-700': secondaryVariants[700],
    '--color-secondary-800': secondaryVariants[800],
    '--color-secondary-900': secondaryVariants[900],
    '--color-secondary-950': secondaryVariants[950],

    // 字体
    '--font-family': theme.fontFamily,
    '--font-heading': theme.headingFont || theme.fontFamily,

    // 圆角
    '--border-radius': getBorderRadiusValue(theme.borderRadius),

    // 阴影
    '--shadow-sm': theme.shadows ? '0 1px 2px 0 rgb(0 0 0 / 0.05)' : 'none',
    '--shadow-md': theme.shadows
      ? '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
      : 'none',
    '--shadow-lg': theme.shadows
      ? '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
      : 'none',
    '--shadow-xl': theme.shadows
      ? '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
      : 'none',
  };

  // 添加强调色变量
  if (accentVariants) {
    Object.assign(variables, {
      '--color-accent-50': accentVariants[50],
      '--color-accent-100': accentVariants[100],
      '--color-accent-200': accentVariants[200],
      '--color-accent-300': accentVariants[300],
      '--color-accent-400': accentVariants[400],
      '--color-accent-500': accentVariants[500],
      '--color-accent-600': accentVariants[600],
      '--color-accent-700': accentVariants[700],
      '--color-accent-800': accentVariants[800],
      '--color-accent-900': accentVariants[900],
      '--color-accent-950': accentVariants[950],
    });
  }

  return variables;
}

/**
 * 获取边框圆角值
 */
function getBorderRadiusValue(
  borderRadius: ThemeSettings['borderRadius'],
): string {
  const values = {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
  };
  return values[borderRadius];
}

/**
 * 应用主题到DOM
 */
export function applyThemeToDOM(
  theme: ThemeSettings,
  element?: HTMLElement,
): void {
  const target = element || document.documentElement;
  const variables = generateCSSVariables(theme);

  Object.entries(variables).forEach(([property, value]) => {
    target.style.setProperty(property, value);
  });

  // 添加暗色模式类
  if (theme.darkMode) {
    target.classList.add('dark');
  } else {
    target.classList.remove('dark');
  }
}

/**
 * 从DOM移除主题变量
 */
export function removeThemeFromDOM(element?: HTMLElement): void {
  const target = element || document.documentElement;

  // 移除所有主题相关的CSS变量
  const style = target.style;
  for (let i = style.length - 1; i >= 0; i--) {
    const property = style[i];
    if (
      property &&
      (property.startsWith('--color-') ||
        property.startsWith('--font-') ||
        property.startsWith('--border-') ||
        property.startsWith('--shadow-'))
    ) {
      style.removeProperty(property);
    }
  }

  // 移除暗色模式类
  target.classList.remove('dark');
}

/**
 * 验证主题配置
 */
export function validateTheme(theme: Partial<ThemeSettings>): string[] {
  const errors: string[] = [];

  if (theme.primaryColor && !isValidHexColor(theme.primaryColor)) {
    errors.push('Primary color must be a valid hex color');
  }

  if (theme.secondaryColor && !isValidHexColor(theme.secondaryColor)) {
    errors.push('Secondary color must be a valid hex color');
  }

  if (theme.accentColor && !isValidHexColor(theme.accentColor)) {
    errors.push('Accent color must be a valid hex color');
  }

  if (theme.fontFamily && theme.fontFamily.trim().length === 0) {
    errors.push('Font family cannot be empty');
  }

  return errors;
}

/**
 * 验证十六进制颜色
 */
function isValidHexColor(color: string): boolean {
  return /^#(?:[A-F0-9]{6}|[A-F0-9]{3})$/i.test(color);
}

/**
 * 导出主题配置为JSON
 */
export function exportTheme(theme: ThemeSettings): string {
  return JSON.stringify(theme, null, 2);
}

/**
 * 从JSON导入主题配置
 */
export function importTheme(json: string): ThemeSettings {
  try {
    const theme = JSON.parse(json);
    const errors = validateTheme(theme);
    if (errors.length > 0) {
      throw new Error(`Invalid theme: ${errors.join(', ')}`);
    }
    return { ...DEFAULT_THEME, ...theme };
  } catch (error) {
    throw new Error(
      `Failed to import theme: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
