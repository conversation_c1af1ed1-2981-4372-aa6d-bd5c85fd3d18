/**
 * 通用验证工具函数
 */

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    // eslint-disable-next-line no-new
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证slug格式（URL友好的字符串）
 */
export function isValidSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

/**
 * 验证域名格式
 */
export function isValidDomain(domain: string): boolean {
  const domainRegex =
    /^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i;
  return domainRegex.test(domain);
}

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): {
  errors: string[];
  isValid: boolean;
  score: number;
} {
  const errors: string[] = [];
  let score = 0;

  // 最小长度检查
  if (password.length < 8) {
    errors.push('密码至少需要8个字符');
  } else {
    score += 1;
  }

  // 大写字母检查
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    errors.push('密码需要包含至少一个大写字母');
  }

  // 小写字母检查
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    errors.push('密码需要包含至少一个小写字母');
  }

  // 数字检查
  if (/\d/.test(password)) {
    score += 1;
  } else {
    errors.push('密码需要包含至少一个数字');
  }

  // 特殊字符检查
  if (/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    score += 1;
  } else {
    errors.push('密码需要包含至少一个特殊字符');
  }

  // 长度加分
  if (password.length >= 12) {
    score += 1;
  }

  return {
    isValid: errors.length === 0,
    errors,
    score,
  };
}

/**
 * 验证手机号格式（中国）
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证身份证号格式（中国）
 */
export function isValidIdCard(idCard: string): boolean {
  const idCardRegex = /^(?:\d{15}|\d{18}|\d{17}X)$/i;
  return idCardRegex.test(idCard);
}

/**
 * 验证文件扩展名
 */
export function isValidFileExtension(
  fileName: string,
  allowedExtensions: string[],
): boolean {
  const extension = fileName.toLowerCase().split('.').pop();
  return extension ? allowedExtensions.includes(extension) : false;
}

/**
 * 验证文件大小
 */
export function isValidFileSize(
  fileSize: number,
  maxSizeInMB: number,
): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return fileSize <= maxSizeInBytes;
}

/**
 * 验证图片文件
 */
export function isValidImageFile(file: File): {
  errors: string[];
  isValid: boolean;
} {
  const errors: string[] = [];
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
  const maxSizeInMB = 10;

  if (!allowedTypes.includes(file.type)) {
    errors.push('不支持的图片格式，请上传 JPEG、PNG、GIF 或 WebP 格式的图片');
  }

  if (!isValidFileSize(file.size, maxSizeInMB)) {
    errors.push(`图片大小不能超过 ${maxSizeInMB}MB`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 验证JSON格式
 */
export function isValidJSON(jsonString: string): boolean {
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证颜色值（十六进制或RGB）
 */
export function isValidColor(color: string): boolean {
  // 十六进制颜色
  const hexRegex = /^#(?:[A-F0-9]{6}|[A-F0-9]{3})$/i;
  if (hexRegex.test(color)) {
    return true;
  }

  // RGB颜色
  const rgbRegex = /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/;
  const rgbMatch = color.match(rgbRegex);
  if (rgbMatch && rgbMatch[1] && rgbMatch[2] && rgbMatch[3]) {
    const [, r, g, b] = rgbMatch;
    return [r, g, b].every((val) => {
      const num = Number.parseInt(val, 10);
      return num >= 0 && num <= 255;
    });
  }

  // RGBA颜色
  const rgbaRegex =
    /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/;
  const rgbaMatch = color.match(rgbaRegex);
  if (
    rgbaMatch &&
    rgbaMatch[1] &&
    rgbaMatch[2] &&
    rgbaMatch[3] &&
    rgbaMatch[4]
  ) {
    const [, r, g, b, a] = rgbaMatch;
    const rgbValid = [r, g, b].every((val) => {
      const num = Number.parseInt(val, 10);
      return num >= 0 && num <= 255;
    });
    const alphaValid = Number.parseFloat(a) >= 0 && Number.parseFloat(a) <= 1;
    return rgbValid && alphaValid;
  }

  return false;
}

/**
 * 验证CSS类名格式
 */
export function isValidCSSClassName(className: string): boolean {
  const classNameRegex = /^[a-z_-][\w-]*$/i;
  return classNameRegex.test(className);
}

/**
 * 清理和验证HTML内容
 */
export function sanitizeHTML(html: string): string {
  // 简单的HTML清理，实际项目中建议使用专门的库如DOMPurify
  return html
    .replaceAll(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replaceAll(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replaceAll(/javascript:/gi, '')
    .replaceAll(/on\w+\s*=/gi, '');
}

/**
 * 验证字符串长度
 */
export function isValidLength(str: string, min: number, max: number): boolean {
  return str.length >= min && str.length <= max;
}

/**
 * 验证数字范围
 */
export function isValidNumberRange(
  num: number,
  min: number,
  max: number,
): boolean {
  return num >= min && num <= max;
}

/**
 * 验证数组长度
 */
export function isValidArrayLength<T>(
  arr: T[],
  min: number,
  max: number,
): boolean {
  return arr.length >= min && arr.length <= max;
}

/**
 * 验证对象是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj === null || obj === undefined) return true;
  if (typeof obj === 'string' || Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
}

/**
 * 深度验证对象结构
 */
export function validateObjectStructure(
  obj: any,
  schema: any,
): {
  errors: string[];
  isValid: boolean;
} {
  const errors: string[] = [];

  function validate(current: any, currentSchema: any, path = ''): void {
    if (
      currentSchema.required &&
      (current === null || current === undefined || current === '')
    ) {
      errors.push(`${path || 'root'} is required`);
      return;
    }

    if (current === null || current === undefined) return;

    if (currentSchema.type) {
      const actualType = Array.isArray(current) ? 'array' : typeof current;
      if (actualType !== currentSchema.type) {
        errors.push(
          `${path || 'root'} should be ${currentSchema.type}, got ${actualType}`,
        );
        return;
      }
    }

    if (
      currentSchema.properties &&
      typeof current === 'object' &&
      !Array.isArray(current)
    ) {
      Object.keys(currentSchema.properties).forEach((key) => {
        const newPath = path ? `${path}.${key}` : key;
        validate(current[key], currentSchema.properties[key], newPath);
      });
    }

    if (currentSchema.items && Array.isArray(current)) {
      current.forEach((item, index) => {
        const newPath = path ? `${path}[${index}]` : `[${index}]`;
        validate(item, currentSchema.items, newPath);
      });
    }

    if (
      currentSchema.minLength &&
      typeof current === 'string' &&
      current.length < currentSchema.minLength
    ) {
      errors.push(
        `${path || 'root'} should be at least ${currentSchema.minLength} characters long`,
      );
    }

    if (
      currentSchema.maxLength &&
      typeof current === 'string' &&
      current.length > currentSchema.maxLength
    ) {
      errors.push(
        `${path || 'root'} should be at most ${currentSchema.maxLength} characters long`,
      );
    }

    if (currentSchema.pattern && typeof current === 'string') {
      const regex = new RegExp(currentSchema.pattern);
      if (!regex.test(current)) {
        errors.push(`${path || 'root'} does not match the required pattern`);
      }
    }
  }

  validate(obj, schema);

  return {
    isValid: errors.length === 0,
    errors,
  };
}
