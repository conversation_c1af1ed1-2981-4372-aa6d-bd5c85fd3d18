{"extends": "../../internal/tsconfig/base.json", "compilerOptions": {"rootDir": "src", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "declaration": true, "declarationMap": true, "noEmit": false, "outDir": "dist", "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}