{"name": "@flexihub/shared", "version": "0.1.0", "description": "FlexiHub共享逻辑、类型定义和工具函数", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "files": ["dist", "src"], "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.mjs", "require": "./dist/types/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.mjs", "require": "./dist/utils/index.js"}, "./api": {"types": "./dist/api/index.d.ts", "import": "./dist/api/index.mjs", "require": "./dist/api/index.js"}}, "publishConfig": {"access": "restricted"}, "peerDependencies": {"react": "^18.0.0"}, "dependencies": {"zod": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}}