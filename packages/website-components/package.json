{"name": "@flexihub/website-components", "version": "0.1.0", "description": "FlexiHub 可拖拽网站组件库", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "files": ["dist", "src"], "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./content": {"types": "./dist/content/index.d.ts", "import": "./dist/content/index.mjs", "require": "./dist/content/index.js"}, "./layout": {"types": "./dist/layout/index.d.ts", "import": "./dist/layout/index.mjs", "require": "./dist/layout/index.js"}, "./interactive": {"types": "./dist/interactive/index.d.ts", "import": "./dist/interactive/index.mjs", "require": "./dist/interactive/index.js"}, "./media": {"types": "./dist/media/index.d.ts", "import": "./dist/media/index.mjs", "require": "./dist/media/index.js"}}, "publishConfig": {"access": "restricted"}, "peerDependencies": {"@craftjs/core": "^0.2.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"@flexihub/shared": "workspace:*", "class-variance-authority": "catalog:", "clsx": "catalog:", "lucide-react": "^0.460.0", "tailwind-merge": "catalog:"}, "devDependencies": {"@craftjs/core": "^0.2.7", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@types/node": "catalog:", "@types/react": "^18.3.14", "@types/react-dom": "^18.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "storybook": "^8.4.7", "tsup": "catalog:", "typescript": "catalog:"}}