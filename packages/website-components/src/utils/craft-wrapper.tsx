/**
 * Craft.js 组件包装器工具
 */

import { useNode } from '@craftjs/core';
import { ComponentSchema } from '@flexihub/shared';
import React from 'react';

import { cn } from './cn';

export interface CraftWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 为组件添加Craft.js拖拽和编辑功能的HOC
 */
export const withCraftWrapper = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  schema: ComponentSchema,
) => {
  const WrappedComponent = React.forwardRef<HTMLElement, CraftWrapperProps & T>(
    (props, ref) => {
      const {
        connectors: { connect, drag },
        selected,
        hovered,
        actions: { setProp: _setProp },
        id,
      } = useNode((state) => ({
        selected: state.events.selected,
        hovered: state.events.hovered,
      }));

      const { className, style, ...componentProps } = props;

      return (
        <div
          className={cn(
            'relative transition-all duration-200',
            {
              'ring-2 ring-blue-500 ring-opacity-75': selected,
              'ring-1 ring-blue-300 ring-opacity-50': hovered && !selected,
            },
            className,
          )}
          data-craft-node-id={id}
          ref={(el) => {
            connect(drag(el));
            if (typeof ref === 'function') {
              ref(el);
            } else if (ref) {
              ref.current = el;
            }
          }}
          style={style}
        >
          <Component {...(componentProps as T)} />

          {/* 选中状态的标签 */}
          {selected && (
            <div className="absolute -top-6 left-0 z-10 rounded-t-md bg-blue-500 px-2 py-1 text-xs font-medium text-white">
              {schema.name}
            </div>
          )}

          {/* 悬停状态的边框效果 */}
          {hovered && !selected && (
            <div className="pointer-events-none absolute inset-0 rounded bg-blue-100 bg-opacity-10" />
          )}
        </div>
      );
    },
  );

  WrappedComponent.displayName = `Craft(${schema.name})`;

  // 为Craft.js添加组件配置
  (WrappedComponent as any).craft = {
    displayName: schema.name,
    props: schema.defaultProps,
    rules: schema.craft?.rules || {
      canDrag: () => true,
      canDrop: () => true,
      canMoveIn: () => true,
      canMoveOut: () => true,
    },
    related: schema.craft?.related || {},
  };

  return WrappedComponent;
};

/**
 * 创建拖拽占位符组件
 */
export const DragPlaceholder: React.FC<{ text?: string }> = ({
  text = '拖拽组件到这里',
}) => {
  return (
    <div className="rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center text-gray-500 transition-colors hover:border-gray-400 hover:bg-gray-100">
      <div className="text-sm font-medium">{text}</div>
    </div>
  );
};

/**
 * 组件预览包装器（用于组件库展示）
 */
export const PreviewWrapper: React.FC<{
  children: React.ReactNode;
  className?: string;
  description?: string;
  title: string;
}> = ({ children, title, description, className }) => {
  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {description && (
          <p className="mt-1 text-sm text-gray-600">{description}</p>
        )}
      </div>
      <div className="rounded-lg border border-gray-200 bg-white p-6">
        {children}
      </div>
    </div>
  );
};

/**
 * 响应式预览包装器
 */
export const ResponsivePreview: React.FC<{
  breakpoint?: 'desktop' | 'mobile' | 'tablet';
  children: React.ReactNode;
}> = ({ children, breakpoint = 'desktop' }) => {
  const widthMap = {
    mobile: 'max-w-sm',
    tablet: 'max-w-2xl',
    desktop: 'max-w-full',
  };

  return (
    <div
      className={cn(
        'mx-auto transition-all duration-300',
        widthMap[breakpoint],
      )}
    >
      {children}
    </div>
  );
};
