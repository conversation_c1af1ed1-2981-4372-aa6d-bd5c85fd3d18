import React from 'react';

import { cn } from '../utils/cn';

interface ContainerProps {
  [key: string]: unknown;
  background?: string;
  children?: React.ReactNode;
  className?: string;
  maxWidth?: '2xl' | 'full' | 'lg' | 'md' | 'sm' | 'xl';
  padding?: 'lg' | 'md' | 'none' | 'sm' | 'xl';
  textAlign?: 'center' | 'left' | 'right';
}

// 扩展FC类型以支持craft属性
interface CraftComponent extends React.FC<ContainerProps> {
  craft?: {
    displayName: string;
    props: Record<string, unknown>;
    rules: Record<string, () => boolean>;
  };
}

export const Container: CraftComponent = ({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  background,
  textAlign = 'left',
  ...props
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <div
      className={cn(
        'mx-auto',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        textAlignClasses[textAlign],
        className,
      )}
      style={{
        backgroundColor: background,
        ...(props.style as React.CSSProperties),
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Craft.js配置（仅在编辑器中使用）
Container.craft = {
  displayName: 'Container',
  props: {
    maxWidth: 'xl',
    padding: 'md',
    textAlign: 'left',
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};

export default Container;
