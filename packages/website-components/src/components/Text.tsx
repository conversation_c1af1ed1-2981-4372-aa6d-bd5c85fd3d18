import React from 'react';

import { cn } from '../utils/cn';

interface TextProps {
  [key: string]: unknown;
  className?: string;
  color?: string;
  content?: string;
  fontSize?:
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | 'base'
    | 'lg'
    | 'sm'
    | 'xl'
    | 'xs';
  fontWeight?:
    | 'black'
    | 'bold'
    | 'extrabold'
    | 'light'
    | 'medium'
    | 'normal'
    | 'semibold'
    | 'thin';
  lineHeight?: 'loose' | 'none' | 'normal' | 'relaxed' | 'snug' | 'tight';
  tag?: 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  textAlign?: 'center' | 'justify' | 'left' | 'right';
}

// 扩展FC类型以支持craft属性
interface CraftComponent extends React.FC<TextProps> {
  craft?: {
    displayName: string;
    props: Record<string, unknown>;
    rules: Record<string, () => boolean>;
  };
}

export const Text: CraftComponent = ({
  content = '',
  tag = 'p',
  fontSize = 'base',
  fontWeight = 'normal',
  textAlign = 'left',
  color,
  lineHeight = 'normal',
  className,
  ...props
}) => {
  const Component = tag;

  const fontSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
    '5xl': 'text-5xl',
    '6xl': 'text-6xl',
  };

  const fontWeightClasses = {
    thin: 'font-thin',
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
    extrabold: 'font-extrabold',
    black: 'font-black',
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  const lineHeightClasses = {
    none: 'leading-none',
    tight: 'leading-tight',
    snug: 'leading-snug',
    normal: 'leading-normal',
    relaxed: 'leading-relaxed',
    loose: 'leading-loose',
  };

  return (
    <Component
      className={cn(
        fontSizeClasses[fontSize],
        fontWeightClasses[fontWeight],
        textAlignClasses[textAlign],
        lineHeightClasses[lineHeight],
        className,
      )}
      style={{
        color,
        ...(props.style as React.CSSProperties),
      }}
      {...props}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

// Craft.js配置（仅在编辑器中使用）
Text.craft = {
  displayName: 'Text',
  props: {
    content: 'Text',
    tag: 'p',
    fontSize: 'base',
    fontWeight: 'normal',
    textAlign: 'left',
    lineHeight: 'normal',
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true,
  },
};

export default Text;
