/**
 * Container 布局组件
 */

import { ComponentSchema } from '@flexihub/shared';
import React from 'react';

import { cn } from '../utils/cn';
import { withCraftWrapper } from '../utils/craft-wrapper';

export interface ContainerProps {
  background?: 'gray' | 'primary' | 'transparent' | 'white';
  children?: React.ReactNode;
  className?: string;
  maxWidth?: '2xl' | 'full' | 'lg' | 'md' | 'sm' | 'xl';
  padding?: 'lg' | 'md' | 'none' | 'sm' | 'xl';
  style?: React.CSSProperties;
}

const ContainerComponent: React.FC<ContainerProps> = ({
  children,
  maxWidth = 'lg',
  padding = 'md',
  background = 'transparent',
  className,
  style,
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6',
    xl: 'px-12 py-8',
  };

  const backgroundClasses = {
    transparent: '',
    white: 'bg-white',
    gray: 'bg-gray-50',
    primary: 'bg-primary-50',
  };

  return (
    <div
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        backgroundClasses[background],
        className,
      )}
      style={style}
    >
      {children}
    </div>
  );
};

// 组件Schema定义
const containerSchema: ComponentSchema = {
  name: 'Container',
  type: 'Container',
  category: 'layout',
  description: '页面容器组件，提供响应式布局和间距控制',
  defaultProps: {
    maxWidth: 'lg',
    padding: 'md',
    background: 'transparent',
  },
  props: {
    maxWidth: {
      type: 'select',
      label: '最大宽度',
      default: 'lg',
      options: [
        { label: '小 (24rem)', value: 'sm' },
        { label: '中 (28rem)', value: 'md' },
        { label: '大 (56rem)', value: 'lg' },
        { label: '超大 (72rem)', value: 'xl' },
        { label: '特大 (80rem)', value: '2xl' },
        { label: '全宽', value: 'full' },
      ],
    },
    padding: {
      type: 'select',
      label: '内边距',
      default: 'md',
      options: [
        { label: '无', value: 'none' },
        { label: '小', value: 'sm' },
        { label: '中', value: 'md' },
        { label: '大', value: 'lg' },
        { label: '超大', value: 'xl' },
      ],
    },
    background: {
      type: 'select',
      label: '背景色',
      default: 'transparent',
      options: [
        { label: '透明', value: 'transparent' },
        { label: '白色', value: 'white' },
        { label: '灰色', value: 'gray' },
        { label: '主色调', value: 'primary' },
      ],
    },
  },
  craft: {
    rules: {
      canDrag: () => true,
      canDrop: () => true,
      canMoveIn: () => true,
      canMoveOut: () => true,
    },
  },
};

export const Container = withCraftWrapper(ContainerComponent, containerSchema);
