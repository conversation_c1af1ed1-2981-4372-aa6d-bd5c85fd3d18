/**
 * Text 内容组件
 */

import { ComponentSchema } from '@flexihub/shared';
import React from 'react';

import { cn } from '../utils/cn';
import { withCraftWrapper } from '../utils/craft-wrapper';

export interface TextProps {
  className?: string;
  color?: 'default' | 'muted' | 'primary' | 'secondary';
  content?: string;
  fontSize?: '2xl' | '3xl' | '4xl' | 'base' | 'lg' | 'sm' | 'xl' | 'xs';
  fontWeight?: 'bold' | 'medium' | 'normal' | 'semibold';
  style?: React.CSSProperties;
  tag?: 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  textAlign?: 'center' | 'justify' | 'left' | 'right';
}

const TextComponent: React.FC<TextProps> = ({
  content = '点击编辑文本内容',
  tag = 'p',
  fontSize = 'base',
  fontWeight = 'normal',
  textAlign = 'left',
  color = 'default',
  className,
  style,
}) => {
  const Component = tag;

  const fontSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  };

  const fontWeightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  const colorClasses = {
    default: 'text-gray-900',
    primary: 'text-primary-600',
    secondary: 'text-secondary-600',
    muted: 'text-gray-500',
  };

  return (
    <Component
      className={cn(
        fontSizeClasses[fontSize],
        fontWeightClasses[fontWeight],
        textAlignClasses[textAlign],
        colorClasses[color],
        className,
      )}
      dangerouslySetInnerHTML={{ __html: content }}
      style={style}
    />
  );
};

// 组件Schema定义
const textSchema: ComponentSchema = {
  name: 'Text',
  type: 'Text',
  category: 'content',
  description: '文本组件，支持多种标签和样式设置',
  defaultProps: {
    content: '点击编辑文本内容',
    tag: 'p',
    fontSize: 'base',
    fontWeight: 'normal',
    textAlign: 'left',
    color: 'default',
  },
  props: {
    content: {
      type: 'textarea',
      label: '文本内容',
      default: '点击编辑文本内容',
    },
    tag: {
      type: 'select',
      label: 'HTML标签',
      default: 'p',
      options: [
        { label: '段落 (p)', value: 'p' },
        { label: '标题1 (h1)', value: 'h1' },
        { label: '标题2 (h2)', value: 'h2' },
        { label: '标题3 (h3)', value: 'h3' },
        { label: '标题4 (h4)', value: 'h4' },
        { label: '标题5 (h5)', value: 'h5' },
        { label: '标题6 (h6)', value: 'h6' },
        { label: '行内元素 (span)', value: 'span' },
        { label: '块级元素 (div)', value: 'div' },
      ],
    },
    fontSize: {
      type: 'select',
      label: '字体大小',
      default: 'base',
      options: [
        { label: '超小', value: 'xs' },
        { label: '小', value: 'sm' },
        { label: '正常', value: 'base' },
        { label: '大', value: 'lg' },
        { label: '超大', value: 'xl' },
        { label: '2倍大', value: '2xl' },
        { label: '3倍大', value: '3xl' },
        { label: '4倍大', value: '4xl' },
      ],
    },
    fontWeight: {
      type: 'select',
      label: '字体粗细',
      default: 'normal',
      options: [
        { label: '正常', value: 'normal' },
        { label: '中等', value: 'medium' },
        { label: '半粗', value: 'semibold' },
        { label: '加粗', value: 'bold' },
      ],
    },
    textAlign: {
      type: 'select',
      label: '文本对齐',
      default: 'left',
      options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' },
        { label: '两端对齐', value: 'justify' },
      ],
    },
    color: {
      type: 'select',
      label: '文本颜色',
      default: 'default',
      options: [
        { label: '默认色', value: 'default' },
        { label: '主色调', value: 'primary' },
        { label: '次色调', value: 'secondary' },
        { label: '淡色', value: 'muted' },
      ],
    },
  },
  craft: {
    rules: {
      canDrag: () => true,
      canDrop: () => false,
      canMoveIn: () => false,
      canMoveOut: () => true,
    },
  },
};

export const Text = withCraftWrapper(TextComponent, textSchema);
