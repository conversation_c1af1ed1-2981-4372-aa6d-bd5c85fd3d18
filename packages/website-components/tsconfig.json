{"extends": "../../internal/tsconfig/base.json", "compilerOptions": {"jsx": "react-jsx", "lib": ["dom", "dom.iterable", "esnext"], "rootDir": "src", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "declaration": true, "declarationMap": true, "noEmit": false, "outDir": "dist", "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.stories.tsx"]}