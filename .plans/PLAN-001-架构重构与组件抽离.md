# FlexiHub 统一架构重构计划 🚀

## 项目概述

将FlexiHub从分离的editor+web架构重构为**统一的Next.js应用架构**，实现编辑器和用户网站渲染的完全统一，简化开发和部署流程。

## 🎯 核心目标

- [x] ✅ 统一技术栈，减少维护成本
- [x] ✅ 实现真正的所见即所得编辑体验  
- [x] ✅ 建立可复用的组件库体系
- [ ] **🔥 统一应用架构，简化部署**
- [ ] 确保编辑和渲染的完全一致性
- [ ] 支持多租户和权限管理
- [ ] 实现全局主题颜色切换和自定义功能
- [ ] 支持PC、平板、移动端响应式编辑预览
- [ ] 🌐 实现完整的国际化(i18n)支持

## 🏗️ 新统一架构设计

### 目录结构
```
apps/
├── admin/              # Vue3 管理后台（保持不变）
└── website/            # 🆕 统一的 Next.js 应用
    ├── src/
    │   ├── app/
    │   │   ├── [locale]/
    │   │   │   ├── editor/[siteId]/     # 编辑器路由
    │   │   │   ├── [domain]/            # 用户网站渲染
    │   │   │   └── layout.tsx           # 条件布局
    │   │   └── api/                     # API路由
    │   ├── components/
    │   │   ├── editor/                  # 编辑器专用组件
    │   │   ├── layouts/                 # 布局组件
    │   │   └── ui/                      # UI组件
    │   ├── hooks/                       # 共享钩子
    │   └── middleware.ts                # 权限中间件
    └── package.json

packages/                               # 共享包（保持不变）
├── @flexihub/shared/
├── @flexihub/website-components/
└── @flexihub/ui/
```

### 🎯 架构优势

1. **🚀 简化部署**：单一Next.js应用，一次部署
2. **🔄 代码共享**：编辑器和渲染器共用组件
3. **⚡ 开发效率**：统一开发环境和工具链
4. **🌐 SEO友好**：原生Next.js SSR/SSG支持
5. **💰 成本降低**：减少服务器资源和维护成本

## 📅 开发时间线（16周）

### 🏗️ 第一阶段：统一架构搭建（第1-4周）

#### **第1周：架构重构**
- [ ] 合并 `apps/editor` 和 `apps/web` 为 `apps/website`
- [ ] 配置统一的Next.js路由系统
- [ ] 实现模式检测逻辑（编辑器模式 vs 渲染模式）
- [ ] 建立条件布局系统

#### **第2周：路由和权限系统**
- [ ] 实现编辑器路由：`/[locale]/editor/[siteId]`
- [ ] 实现用户网站路由：`/[locale]/[domain]`
- [ ] 搭建权限中间件
- [ ] 集成JWT身份验证

#### **第3周：基础UI框架**
- [ ] 统一Tailwind CSS配置
- [ ] 建立设计系统基础
- [ ] 实现响应式布局框架
- [ ] 创建基础UI组件库

#### **第4周：国际化系统**
- [ ] 集成next-intl
- [ ] 实现多语言路由
- [ ] 创建语言切换组件
- [ ] 建立翻译资源管理

### 🎨 第二阶段：编辑器核心功能（第5-8周）

#### **第5周：Craft.js集成**
- [ ] 安装和配置Craft.js
- [ ] 实现基础拖拽功能
- [ ] 创建组件面板
- [ ] 实现撤销/重做功能

#### **第6周：编辑器界面**
- [ ] 设计编辑器布局
- [ ] 实现工具栏组件
- [ ] 创建属性面板
- [ ] 实现图层管理

#### **第7周：组件系统**
- [ ] 扩展website-components库
- [ ] 实现Text、Container等基础组件
- [ ] 添加图片、按钮、表单组件
- [ ] 实现组件配置面板

#### **第8周：主题系统**
- [ ] 实现全局主题管理
- [ ] 创建颜色选择器
- [ ] 实现实时主题切换
- [ ] 支持自定义CSS变量

### 🌐 第三阶段：渲染引擎和高级功能（第9-12周）

#### **第9周：渲染引擎优化**
- [ ] 完善PageRenderer组件
- [ ] 实现SSR/SSG支持
- [ ] 优化SEO元数据
- [ ] 实现动态组件加载

#### **第10周：响应式系统**
- [ ] 实现多设备预览
- [ ] 添加断点管理
- [ ] 创建响应式编辑工具
- [ ] 优化移动端体验

#### **第11周：数据管理**
- [ ] 实现页面保存/加载
- [ ] 添加版本管理
- [ ] 实现模板系统
- [ ] 创建内容管理接口

#### **第12周：高级组件**
- [ ] 实现导航菜单组件
- [ ] 添加幻灯片组件
- [ ] 创建表格组件
- [ ] 实现表单构建器

### ⚡ 第四阶段：集成和优化（第13-16周）

#### **第13周：管理后台集成**
- [ ] 更新Vue3管理后台
- [ ] 实现"编辑网站"跳转
- [ ] 同步权限系统
- [ ] 添加网站管理功能

#### **第14周：性能优化**
- [ ] 代码分割优化
- [ ] 图片懒加载
- [ ] 缓存策略优化
- [ ] 构建性能优化

#### **第15周：测试和调试**
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] 端到端测试
- [ ] 性能测试

#### **第16周：部署和文档**
- [ ] 生产环境配置
- [ ] Docker配置优化
- [ ] 用户文档编写
- [ ] 开发文档完善

## 🔧 核心技术实现

### 统一Next.js配置
```typescript
// apps/website/next.config.ts
const nextConfig: NextConfig = {
  async rewrites() {
    return [
      // 编辑器模式
      {
        source: '/:locale/editor/:siteId*',
        destination: '/:locale/editor/:siteId*'
      },
      // 用户网站模式
      {
        source: '/:locale/:domain*',
        destination: '/:locale/site/:domain*'
      }
    ];
  },
  
  env: {
    APP_MODE: process.env.APP_MODE || 'unified',
  }
};
```

### 模式检测Hook
```typescript
// src/hooks/useAppMode.ts
export function useAppMode() {
  const pathname = usePathname();
  
  return {
    isEditor: pathname.includes('/editor/'),
    isUserSite: !pathname.includes('/editor/'),
    mode: pathname.includes('/editor/') ? 'editor' : 'site',
    siteId: pathname.match(/\/editor\/([^/]+)/)?.[1],
    domain: pathname.match(/\/site\/([^/]+)/)?.[1]
  };
}
```

### 条件布局系统
```typescript
// src/app/[locale]/layout.tsx
export default function RootLayout({ children }: { children: ReactNode }) {
  const { isEditor } = useAppMode();
  
  return (
    <html>
      <body>
        {isEditor ? (
          <EditorLayout>{children}</EditorLayout>
        ) : (
          <UserSiteLayout>{children}</UserSiteLayout>
        )}
      </body>
    </html>
  );
}
```

### 权限中间件
```typescript
// src/middleware.ts
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 编辑器权限检查
  if (pathname.includes('/editor/')) {
    return checkEditorPermission(request);
  }
  
  // 用户网站访问（无需权限）
  return NextResponse.next();
}
```

## 🚀 开始执行

**当前状态**：✅ 计划已更新  
**下一步**：🏗️ 开始第1周任务 - 架构重构  

---

*更新时间：2024年12月* 🕒
