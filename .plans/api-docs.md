# FlexiHub API 文档

多租户SaaS建站系统API接口文档

**版本**: 1.0.0

## 统一返回格式

所有API接口都会返回以下统一格式的响应：

```json
{
  "code": 0, // 状态码：0-成功，其他值-失败
  "data": {}, // 响应数据，类型根据具体接口而定
  "message": "操作成功" // 响应消息
}
```

### 状态码说明

| 状态码 | 说明           |
| ------ | -------------- |
| 0      | 操作成功       |
| -1     | 操作失败       |
| 400    | 参数错误       |
| 401    | 未授权         |
| 403    | 禁止访问       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 认证方式

大部分API需要Bearer Token认证，请在请求头中添加：

```
Authorization: Bearer <your_token>
```

## 认证

### 用户登录

**请求方式**: `POST`

**请求路径**: `/api/auth/login`

**请求体**:

```json
{
  "username": "\"admin\"",
  "password": "\"password123\"",
  "tenantCode": "\"tenant1\"",
  "tenantId": "1"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "accessToken": "\"string\""
  }
}
```

---

### 获取用户信息

**请求方式**: `GET`

**请求路径**: `/api/user/info`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "userId": "\"string\"",
    "username": "\"string\"",
    "realName": "\"string\"",
    "avatar": "\"string\"",
    "desc": "\"string\"",
    "homePath": "\"string\"",
    "roles": "[\"string\"]"
  }
}
```

---

### 获取角色编码

**请求方式**: `GET`

**请求路径**: `/api/auth/codes`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": ["string"]
}
```

---

### 刷新令牌

**请求方式**: `POST`

**请求路径**: `/api/auth/refresh`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "accessToken": "\"string\""
  }
}
```

---

### 退出登录

**请求方式**: `POST`

**请求路径**: `/api/auth/logout`

**响应示例**:

---

## 健康检查

### 系统健康检查

**请求方式**: `GET`

**请求路径**: `/api/health`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "status": "\"ok\"",
    "info": "{}",
    "error": "{}",
    "details": "{}"
  }
}
```

---

### Ping检查

**请求方式**: `GET`

**请求路径**: `/api/health/ping`

**响应示例**:

---

## 系统监控

### 获取系统指标

**请求方式**: `GET`

**请求路径**: `/api/monitoring/metrics`

**响应示例**:

---

### 获取监控数据

**请求方式**: `GET`

**请求路径**: `/api/monitoring/dashboard`

**响应示例**:

---

## 缓存管理

### 获取缓存指标

**请求方式**: `GET`

**请求路径**: `/api/system/cache/metrics`

**响应示例**:

---

### 清空所有缓存

**请求方式**: `DELETE`

**请求路径**: `/api/system/cache/clear`

**响应示例**:

---

### 删除指定键的缓存

**请求方式**: `DELETE`

**请求路径**: `/api/system/cache/key/{key}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| key    | string | path | 是   | 缓存键 |

**响应示例**:

---

### 使用模式删除多个缓存

**请求方式**: `DELETE`

**请求路径**: `/api/system/cache/pattern`

**请求参数**:

| 参数名  | 类型   | 位置  | 必填 | 说明                   |
| ------- | ------ | ----- | ---- | ---------------------- |
| pattern | string | query | 是   | 缓存键模式，如 menu:\* |

**响应示例**:

---

### 清除所有菜单缓存

**请求方式**: `DELETE`

**请求路径**: `/api/system/cache/menu`

**响应示例**:

---

### 缓存监控面板

**请求方式**: `GET`

**请求路径**: `/api/system/cache/dashboard`

**响应示例**:

---

## users

### POST /api/users

**请求方式**: `POST`

**请求路径**: `/api/users`

**请求体**:

```json
{
  "username": "\"string\"",
  "password": "\"string\"",
  "realName": "\"string\"",
  "email": "\"string\"",
  "status": "0",
  "roleIds": "[\"string\"]",
  "phoneNumber": "\"string\"",
  "idCardNumber": "\"string\"",
  "openid": "\"string\"",
  "unionid": "\"string\"",
  "adminRemark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "{\n  \"id\": \"{}\",\n  \"username\": \"{}\",\n  \"emailAddress\": \"{}\",\n  \"firstName\": \"{}\",\n  \"lastName\": \"{}\",\n  \"realName\": \"{}\",\n  \"avatar\": \"{}\",\n  \"status\": \"{}\",\n  \"lastLoginAt\": \"{}\",\n  \"homePath\": \"{}\",\n  \"metadata\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"phoneNumber\": \"{}\",\n  \"idCardNumber\": \"{}\",\n  \"openid\": \"{}\",\n  \"unionid\": \"{}\",\n  \"adminRemark\": \"{}\"\n}",
    "metadata": "{\n  \"count\": \"{}\",\n  \"totalCount\": \"{}\",\n  \"take\": \"{}\",\n  \"skip\": \"{}\",\n  \"pageCount\": \"{}\",\n  \"currentPage\": \"{}\"\n}"
  }
}
```

---

### GET /api/users/list

**请求方式**: `GET`

**请求路径**: `/api/users/list`

**请求参数**:

| 参数名       | 类型   | 位置  | 必填 | 说明                       |
| ------------ | ------ | ----- | ---- | -------------------------- |
| page         | number | query | 否   | Default = 1                |
| pageSize     | number | query | 否   | Default = 10               |
| username     | string | query | 否   | 用户名，模糊查询           |
| realName     | string | query | 否   | 真实姓名，模糊查询         |
| email        | string | query | 否   | 邮箱，模糊查询             |
| status       | number | query | 否   | 状态：0-禁用，1-启用       |
| phoneNumber  | string | query | 否   | 手机号，模糊查询           |
| idCardNumber | string | query | 否   | 身份证号，模糊查询         |
| openid       | string | query | 否   | 微信openid，模糊查询       |
| unionid      | string | query | 否   | 微信unionid，模糊查询      |
| startTime    | string | query | 否   | 开始时间，格式：YYYY-MM-DD |
| endTime      | string | query | 否   | 结束时间，格式：YYYY-MM-DD |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{}]",
    "metadata": "{\n  \"count\": \"{}\",\n  \"totalCount\": \"{}\",\n  \"take\": \"{}\",\n  \"skip\": \"{}\",\n  \"pageCount\": \"{}\",\n  \"currentPage\": \"{}\"\n}"
  }
}
```

---

### GET /api/users/{id}

**请求方式**: `GET`

**请求路径**: `/api/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "{\n  \"id\": \"{}\",\n  \"username\": \"{}\",\n  \"emailAddress\": \"{}\",\n  \"firstName\": \"{}\",\n  \"lastName\": \"{}\",\n  \"realName\": \"{}\",\n  \"avatar\": \"{}\",\n  \"status\": \"{}\",\n  \"lastLoginAt\": \"{}\",\n  \"homePath\": \"{}\",\n  \"metadata\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"phoneNumber\": \"{}\",\n  \"idCardNumber\": \"{}\",\n  \"openid\": \"{}\",\n  \"unionid\": \"{}\",\n  \"adminRemark\": \"{}\"\n}",
    "metadata": "{\n  \"count\": \"{}\",\n  \"totalCount\": \"{}\",\n  \"take\": \"{}\",\n  \"skip\": \"{}\",\n  \"pageCount\": \"{}\",\n  \"currentPage\": \"{}\"\n}"
  }
}
```

---

### PUT /api/users/{id}

**请求方式**: `PUT`

**请求路径**: `/api/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**请求体**:

```json
{
  "username": "\"string\"",
  "realName": "\"string\"",
  "email": "\"string\"",
  "status": "0",
  "roleIds": "[\"string\"]",
  "phoneNumber": "\"string\"",
  "idCardNumber": "\"string\"",
  "openid": "\"string\"",
  "unionid": "\"string\"",
  "adminRemark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "{\n  \"id\": \"{}\",\n  \"username\": \"{}\",\n  \"emailAddress\": \"{}\",\n  \"firstName\": \"{}\",\n  \"lastName\": \"{}\",\n  \"realName\": \"{}\",\n  \"avatar\": \"{}\",\n  \"status\": \"{}\",\n  \"lastLoginAt\": \"{}\",\n  \"homePath\": \"{}\",\n  \"metadata\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"phoneNumber\": \"{}\",\n  \"idCardNumber\": \"{}\",\n  \"openid\": \"{}\",\n  \"unionid\": \"{}\",\n  \"adminRemark\": \"{}\"\n}",
    "metadata": "{\n  \"count\": \"{}\",\n  \"totalCount\": \"{}\",\n  \"take\": \"{}\",\n  \"skip\": \"{}\",\n  \"pageCount\": \"{}\",\n  \"currentPage\": \"{}\"\n}"
  }
}
```

---

### DELETE /api/users/{id}

**请求方式**: `DELETE`

**请求路径**: `/api/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**响应示例**:

---

### 修改密码

**请求方式**: `POST`

**请求路径**: `/api/users/{id}/change-password`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**请求体**:

```json
{
  "oldPassword": "\"string\"",
  "newPassword": "\"string\"",
  "confirmPassword": "\"string\""
}
```

**响应示例**:

---

### 重置密码（管理员操作）

**请求方式**: `POST`

**请求路径**: `/api/users/{id}/reset-password`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**请求体**:

```json
{
  "newPassword": "\"string\"",
  "confirmPassword": "\"string\""
}
```

**响应示例**:

---

## 菜单

### 获取所有菜单（前端导航用）

**请求方式**: `GET`

**请求路径**: `/api/menu/all`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "\"1\"",
      "name": "\"系统管理\"",
      "path": "\"/system\"",
      "component": "\"LAYOUT\"",
      "redirect": "\"/system/user\"",
      "meta": "null",
      "children": "[{}]"
    }
  ]
}
```

---

### 获取菜单列表（管理用）

**请求方式**: `GET`

**请求路径**: `/api/system/menu/list`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "name": "\"系统管理\"",
      "path": "\"/system\"",
      "pid": "0",
      "redirect": "\"/system/user\"",
      "type": "\"menu\"",
      "icon": "\"setting\"",
      "component": "\"LAYOUT\"",
      "permission": "\"system:menu:list\"",
      "orderNo": "1",
      "status": "1",
      "meta": "null",
      "id": "1",
      "createdAt": "\"string\"",
      "updatedAt": "\"string\"",
      "children": "[{}]"
    }
  ]
}
```

---

### 创建菜单

**请求方式**: `POST`

**请求路径**: `/api/system/menu`

**请求体**:

```json
{
  "name": "\"系统管理\"",
  "path": "\"/system\"",
  "pid": "0",
  "redirect": "\"/system/user\"",
  "type": "\"menu\"",
  "icon": "\"setting\"",
  "component": "\"LAYOUT\"",
  "permission": "\"system:menu:list\"",
  "orderNo": "1",
  "status": "1",
  "meta": "null"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "name": "\"系统管理\"",
    "path": "\"/system\"",
    "pid": "0",
    "redirect": "\"/system/user\"",
    "type": "\"menu\"",
    "icon": "\"setting\"",
    "component": "\"LAYOUT\"",
    "permission": "\"system:menu:list\"",
    "orderNo": "1",
    "status": "1",
    "meta": "null",
    "id": "1",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "children": "[{}]"
  }
}
```

---

### 更新菜单

**请求方式**: `PUT`

**请求路径**: `/api/system/menu/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 菜单ID |

**请求体**:

```json
{
  "name": "\"系统管理\"",
  "path": "\"/system\"",
  "pid": "0",
  "redirect": "\"/system/user\"",
  "type": "\"menu\"",
  "icon": "\"setting\"",
  "component": "\"LAYOUT\"",
  "permission": "\"system:menu:list\"",
  "orderNo": "1",
  "status": "1",
  "meta": "null"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "name": "\"系统管理\"",
    "path": "\"/system\"",
    "pid": "0",
    "redirect": "\"/system/user\"",
    "type": "\"menu\"",
    "icon": "\"setting\"",
    "component": "\"LAYOUT\"",
    "permission": "\"system:menu:list\"",
    "orderNo": "1",
    "status": "1",
    "meta": "null",
    "id": "1",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "children": "[{}]"
  }
}
```

---

### 删除菜单

**请求方式**: `DELETE`

**请求路径**: `/api/system/menu/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 菜单ID |

**响应示例**:

---

### 检查菜单名称是否存在

**请求方式**: `GET`

**请求路径**: `/api/system/menu/name-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明         |
| ------ | ------ | ----- | ---- | ------------ |
| name   | string | query | 是   | 菜单名称     |
| id     | string | query | 否   | 排除的菜单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": true
}
```

---

### 检查菜单路径是否存在

**请求方式**: `GET`

**请求路径**: `/api/system/menu/path-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明         |
| ------ | ------ | ----- | ---- | ------------ |
| path   | string | query | 是   | 菜单路径     |
| id     | string | query | 否   | 排除的菜单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": true
}
```

---

## 角色

### 获取角色列表

**请求方式**: `GET`

**请求路径**: `/api/system/role/list`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明                       |
| --------- | ------ | ----- | ---- | -------------------------- |
| page      | number | query | 否   | 页码，默认1                |
| pageSize  | number | query | 否   | 每页数量，默认10           |
| id        | string | query | 否   | 角色ID，精确查询           |
| name      | string | query | 否   | 角色名称，模糊查询         |
| remark    | string | query | 否   | 角色描述，模糊查询         |
| status    | number | query | 否   | 角色状态：0-禁用，1-启用   |
| startTime | string | query | 否   | 开始时间，格式：YYYY-MM-DD |
| endTime   | string | query | 否   | 结束时间，格式：YYYY-MM-DD |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "items": "[{}]",
    "total": "0",
    "page": "0",
    "pageSize": "0"
  }
}
```

---

### 创建角色

**请求方式**: `POST`

**请求路径**: `/api/system/role`

**请求体**:

```json
{
  "name": "\"管理员\"",
  "code": "\"admin\"",
  "remark": "\"系统管理员角色\"",
  "status": "1",
  "permissionIds": "[0]",
  "menuIds": "[0]"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "\"string\"",
    "name": "\"string\"",
    "code": "\"string\"",
    "remark": "\"string\"",
    "status": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "permissions": "[{}]",
    "menus": "[{}]"
  }
}
```

---

### 获取角色详情

**请求方式**: `GET`

**请求路径**: `/api/system/role/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 角色ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "\"string\"",
    "name": "\"string\"",
    "code": "\"string\"",
    "remark": "\"string\"",
    "status": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "permissions": "[{}]",
    "menus": "[{}]"
  }
}
```

---

### 更新角色

**请求方式**: `PUT`

**请求路径**: `/api/system/role/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 角色ID |

**请求体**:

```json
{
  "name": "\"管理员\"",
  "code": "\"admin\"",
  "remark": "\"系统管理员角色\"",
  "status": "1",
  "permissionIds": "[0]",
  "menuIds": "[0]"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "\"string\"",
    "name": "\"string\"",
    "code": "\"string\"",
    "remark": "\"string\"",
    "status": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "permissions": "[{}]",
    "menus": "[{}]"
  }
}
```

---

### 删除角色

**请求方式**: `DELETE`

**请求路径**: `/api/system/role/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 角色ID |

**响应示例**:

---

### 分配角色给用户

**请求方式**: `POST`

**请求路径**: `/api/system/role/assign`

**请求体**:

```json
{
  "userId": "0",
  "roleIds": "[\"string\"]"
}
```

**响应示例**:

---

### 移除用户角色

**请求方式**: `DELETE`

**请求路径**: `/api/system/role/users/{userId}/roles/{roleId}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| userId | number | path | 是   | 用户ID |
| roleId | string | path | 是   | 角色ID |

**响应示例**:

---

## 权限管理

### 获取角色的权限列表

**请求方式**: `GET`

**请求路径**: `/api/system/permission/roles/{roleId}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| roleId | string | path | 是   | 角色ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "code": "\"string\"",
      "description": "\"string\"",
      "status": "0",
      "createdAt": "\"string\"",
      "updatedAt": "\"string\""
    }
  ]
}
```

---

### 获取权限列表

**请求方式**: `GET`

**请求路径**: `/api/system/permission/list`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                 |
| ------ | ------ | ----- | ---- | -------------------- |
| name   | string | query | 否   | 权限名称             |
| code   | string | query | 否   | 权限码               |
| status | number | query | 否   | 状态：0-禁用，1-启用 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "code": "\"string\"",
      "description": "\"string\"",
      "status": "0",
      "createdAt": "\"string\"",
      "updatedAt": "\"string\""
    }
  ]
}
```

---

### 根据ID获取权限

**请求方式**: `GET`

**请求路径**: `/api/system/permission/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 权限ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "code": "\"string\"",
    "description": "\"string\"",
    "status": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\""
  }
}
```

---

### 更新权限

**请求方式**: `PUT`

**请求路径**: `/api/system/permission/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 权限ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "code": "\"string\"",
  "description": "\"string\"",
  "status": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "code": "\"string\"",
    "description": "\"string\"",
    "status": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\""
  }
}
```

---

### 删除权限

**请求方式**: `DELETE`

**请求路径**: `/api/system/permission/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 权限ID |

**响应示例**:

---

### 创建权限

**请求方式**: `POST`

**请求路径**: `/api/system/permission`

**请求体**:

```json
{
  "name": "\"string\"",
  "code": "\"string\"",
  "description": "\"string\"",
  "status": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "code": "\"string\"",
    "description": "\"string\"",
    "status": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\""
  }
}
```

---

### 检查权限码是否存在

**请求方式**: `GET`

**请求路径**: `/api/system/permission/code-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                           |
| ------ | ------ | ----- | ---- | ------------------------------ |
| code   | string | query | 是   | 权限码                         |
| id     | string | query | 否   | 排除的权限ID（用于更新时检查） |

**响应示例**:

---

### 分配权限给角色

**请求方式**: `POST`

**请求路径**: `/api/system/permission/assign`

**请求体**:

```json
{
  "roleId": "\"string\"",
  "permissionIds": "[0]"
}
```

**响应示例**:

---

### 移除角色的权限

**请求方式**: `DELETE`

**请求路径**: `/api/system/permission/roles/{roleId}/permissions/{permissionId}`

**请求参数**:

| 参数名       | 类型   | 位置 | 必填 | 说明   |
| ------------ | ------ | ---- | ---- | ------ |
| roleId       | string | path | 是   | 角色ID |
| permissionId | number | path | 是   | 权限ID |

**响应示例**:

---

## 租户管理

### 获取租户列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/list`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明                 |
| --------- | ------ | ----- | ---- | -------------------- |
| endTime   | string | query | 否   | 结束时间             |
| startTime | string | query | 否   | 开始时间             |
| status    | string | query | 否   | 状态：0-禁用，1-启用 |
| domain    | string | query | 否   | 租户域名             |
| code      | string | query | 否   | 租户代码             |
| name      | string | query | 否   | 租户名称             |
| pageSize  | string | query | 否   | 每页数量             |
| page      | string | query | 否   | 页码                 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "items": "[{}]",
    "total": "0",
    "page": "0",
    "pageSize": "0"
  }
}
```

---

### 获取租户详情

**请求方式**: `GET`

**请求路径**: `/api/tenant/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 租户ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "website": "\"string\"",
    "domain": "\"string\"",
    "status": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "datasource": "null"
  }
}
```

---

### 更新租户

**请求方式**: `PUT`

**请求路径**: `/api/tenant/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 租户ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "website": "\"string\"",
  "domain": "\"string\"",
  "status": "0",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "website": "\"string\"",
    "domain": "\"string\"",
    "status": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "datasource": "null"
  }
}
```

---

### 删除租户

**请求方式**: `DELETE`

**请求路径**: `/api/tenant/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 租户ID |

**响应示例**:

---

### 创建租户

**请求方式**: `POST`

**请求路径**: `/api/tenant`

**请求体**:

```json
{
  "code": "\"string\"",
  "name": "\"string\"",
  "website": "\"string\"",
  "domain": "\"string\"",
  "status": "0",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "website": "\"string\"",
    "domain": "\"string\"",
    "status": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "datasource": "null"
  }
}
```

---

### 更新租户状态

**请求方式**: `PATCH`

**请求路径**: `/api/tenant/{id}/status`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 租户ID |

**请求体**:

```json
{
  "status": "0"
}
```

**响应示例**:

---

### 检查租户代码是否存在

**请求方式**: `GET`

**请求路径**: `/api/tenant/check-code/{code}`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明         |
| ------ | ------ | ----- | ---- | ------------ |
| code   | string | path  | 是   | 租户代码     |
| id     | string | query | 否   | 排除的租户ID |

**响应示例**:

---

### 检查租户域名是否存在

**请求方式**: `GET`

**请求路径**: `/api/tenant/check-domain/{domain}`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明         |
| ------ | ------ | ----- | ---- | ------------ |
| domain | string | path  | 是   | 租户域名     |
| id     | string | query | 否   | 排除的租户ID |

**响应示例**:

---

## 公开接口 - 租户注册

### 租户自助注册

**请求方式**: `POST`

**请求路径**: `/api/public/tenant/register`

**请求体**:

```json
{
  "tenantCode": "\"string\"",
  "tenantName": "\"string\"",
  "customDomain": "\"string\"",
  "companyInfo": "null",
  "adminUser": "null",
  "subscriptionPlan": "\"string\"",
  "metadata": "{}",
  "agreeToTerms": "true"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success": "true",
    "tenantId": "0",
    "tenantCode": "\"string\"",
    "accessUrl": "\"string\"",
    "adminToken": "\"string\"",
    "nextSteps": "[\"string\"]"
  }
}
```

---

### 验证租户邮箱

**请求方式**: `POST`

**请求路径**: `/api/public/tenant/verify`

**请求体**:

```json
{
  "tenantCode": "\"string\"",
  "verificationCode": "\"string\""
}
```

**响应示例**:

---

### 检查租户代码是否可用

**请求方式**: `GET`

**请求路径**: `/api/public/tenant/check-availability`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明 |
| ------ | ------ | ----- | ---- | ---- |
| code   | string | query | 是   |      |
| domain | string | query | 是   |      |

**响应示例**:

---

### 获取可用的订阅计划

**请求方式**: `GET`

**请求路径**: `/api/public/tenant/subscription-plans`

**响应示例**:

---

### 获取注册进度

**请求方式**: `GET`

**请求路径**: `/api/public/tenant/registration-status`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明 |
| ------ | ------ | ----- | ---- | ---- |
| code   | string | query | 是   |      |

**响应示例**:

---

## 数据源

### 测试数据源连接

**请求方式**: `POST`

**请求路径**: `/api/datasource/test-connection`

**请求体**:

```json
{
  "url": "\"postgresql://user:password@localhost:5432/test_db\""
}
```

**响应示例**:

---

## 部门

### 获取部门树形列表

**请求方式**: `GET`

**请求路径**: `/api/departments/tree`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                     |
| ------ | ------ | ----- | ---- | ------------------------ |
| name   | string | query | 否   | 部门名称，模糊查询       |
| status | number | query | 否   | 部门状态，0-禁用，1-启用 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "pid": "0",
      "status": "0",
      "orderNo": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\"",
      "remark": "\"string\"",
      "children": "[{}]"
    }
  ]
}
```

---

### 获取部门树形列表

**请求方式**: `GET`

**请求路径**: `/api/system/dept/tree`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                     |
| ------ | ------ | ----- | ---- | ------------------------ |
| name   | string | query | 否   | 部门名称，模糊查询       |
| status | number | query | 否   | 部门状态，0-禁用，1-启用 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "pid": "0",
      "status": "0",
      "orderNo": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\"",
      "remark": "\"string\"",
      "children": "[{}]"
    }
  ]
}
```

---

### 获取部门列表（扁平结构）

**请求方式**: `GET`

**请求路径**: `/api/departments/list`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                     |
| ------ | ------ | ----- | ---- | ------------------------ |
| status | number | query | 否   | 部门状态，0-禁用，1-启用 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "pid": "0",
      "status": "0",
      "orderNo": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\"",
      "remark": "\"string\"",
      "children": "[{}]"
    }
  ]
}
```

---

### 获取部门列表（扁平结构）

**请求方式**: `GET`

**请求路径**: `/api/system/dept/list`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                     |
| ------ | ------ | ----- | ---- | ------------------------ |
| status | number | query | 否   | 部门状态，0-禁用，1-启用 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "pid": "0",
      "status": "0",
      "orderNo": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\"",
      "remark": "\"string\"",
      "children": "[{}]"
    }
  ]
}
```

---

### 获取部门详情

**请求方式**: `GET`

**请求路径**: `/api/departments/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 更新部门

**请求方式**: `PUT`

**请求路径**: `/api/departments/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "pid": "0",
  "status": "0",
  "orderNo": "0",
  "remark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 删除部门

**请求方式**: `DELETE`

**请求路径**: `/api/departments/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**响应示例**:

---

### 获取部门详情

**请求方式**: `GET`

**请求路径**: `/api/system/dept/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 更新部门

**请求方式**: `PUT`

**请求路径**: `/api/system/dept/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "pid": "0",
  "status": "0",
  "orderNo": "0",
  "remark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 删除部门

**请求方式**: `DELETE`

**请求路径**: `/api/system/dept/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**响应示例**:

---

### 创建部门

**请求方式**: `POST`

**请求路径**: `/api/departments`

**请求体**:

```json
{
  "name": "\"string\"",
  "pid": "0",
  "status": "0",
  "orderNo": "0",
  "remark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 创建部门

**请求方式**: `POST`

**请求路径**: `/api/system/dept`

**请求体**:

```json
{
  "name": "\"string\"",
  "pid": "0",
  "status": "0",
  "orderNo": "0",
  "remark": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "pid": "0",
    "status": "0",
    "orderNo": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "remark": "\"string\""
  }
}
```

---

### 更新部门状态

**请求方式**: `PATCH`

**请求路径**: `/api/departments/{id}/status`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**请求体**:

```json
{
  "status": "0"
}
```

**响应示例**:

---

### 更新部门状态

**请求方式**: `PATCH`

**请求路径**: `/api/system/dept/{id}/status`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | string | path | 是   | 部门ID |

**请求体**:

```json
{
  "status": "0"
}
```

**响应示例**:

---

### 检查部门名称是否存在

**请求方式**: `GET`

**请求路径**: `/api/departments/name-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                                 |
| ------ | ------ | ----- | ---- | ------------------------------------ |
| name   | string | query | 是   | 部门名称                             |
| pid    | number | query | 是   | 父部门ID                             |
| id     | number | query | 否   | 排除的部门ID（可选，用于编辑时检查） |

**响应示例**:

---

### 检查部门名称是否存在

**请求方式**: `GET`

**请求路径**: `/api/system/dept/name-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明                                 |
| ------ | ------ | ----- | ---- | ------------------------------------ |
| name   | string | query | 是   | 部门名称                             |
| pid    | number | query | 是   | 父部门ID                             |
| id     | number | query | 否   | 排除的部门ID（可选，用于编辑时检查） |

**响应示例**:

---

## 功能模板管理

### 获取功能模板列表

**请求方式**: `GET`

**请求路径**: `/api/system/feature-templates`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明     |
| -------- | ------ | ----- | ---- | -------- |
| page     | number | query | 否   | 页码     |
| pageSize | number | query | 否   | 每页数量 |

**响应示例**:

---

### 创建或更新功能模板

**请求方式**: `POST`

**请求路径**: `/api/system/feature-templates`

**响应示例**:

---

### 获取功能模板详情

**请求方式**: `GET`

**请求路径**: `/api/system/feature-templates/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 模板代码 |

**响应示例**:

---

### 删除功能模板

**请求方式**: `DELETE`

**请求路径**: `/api/system/feature-templates/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 模板代码 |

**响应示例**:

---

## 租户功能管理

### 获取租户功能列表

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-features/list`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明 |
| -------- | ------ | ----- | ---- | ---- |
| tenantId | number | query | 是   |      |

**响应示例**:

---

### 启用租户功能

**请求方式**: `POST`

**请求路径**: `/api/{tenantId}/features/{featureCode}/enable`

**请求参数**:

| 参数名      | 类型   | 位置 | 必填 | 说明     |
| ----------- | ------ | ---- | ---- | -------- |
| tenantId    | number | path | 是   | 租户ID   |
| featureCode | string | path | 是   | 功能代码 |

**响应示例**:

---

### 禁用租户功能

**请求方式**: `POST`

**请求路径**: `/api/{tenantId}/features/{featureCode}/disable`

**请求参数**:

| 参数名      | 类型   | 位置 | 必填 | 说明     |
| ----------- | ------ | ---- | ---- | -------- |
| tenantId    | number | path | 是   | 租户ID   |
| featureCode | string | path | 是   | 功能代码 |

**响应示例**:

---

### 应用功能模板

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-features/apply-template`

**请求体**:

```json
{
  "tenantId": "0",
  "templateCode": "\"string\""
}
```

**响应示例**:

---

### 获取当前租户功能

**请求方式**: `GET`

**请求路径**: `/api/tenant/features/my-features`

**响应示例**:

---

### 获取功能配置

**请求方式**: `GET`

**请求路径**: `/api/tenant/features/config/{featureCode}`

**请求参数**:

| 参数名      | 类型   | 位置 | 必填 | 说明     |
| ----------- | ------ | ---- | ---- | -------- |
| featureCode | string | path | 是   | 功能代码 |

**响应示例**:

---

## 租户配置管理

### 获取所有配置类别

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-configs/categories`

**响应示例**:

---

### 创建测试数据

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-configs/create-test-data`

**响应示例**:

---

### 获取租户配置

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-configs/{category}`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明     |
| -------- | ------ | ----- | ---- | -------- |
| category | string | path  | 是   | 配置类别 |
| tenantId | number | query | 否   |          |

**响应示例**:

---

### 更新租户配置

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-configs/{category}`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明     |
| -------- | ------ | ----- | ---- | -------- |
| category | string | path  | 是   | 配置类别 |
| tenantId | number | query | 否   |          |

**响应示例**:

---

### 测试租户配置

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-configs/{category}/test`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明     |
| -------- | ------ | ----- | ---- | -------- |
| category | string | path  | 是   | 配置类别 |
| tenantId | number | query | 否   |          |

**响应示例**:

---

### 获取配置模板

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-configs/templates/{category}`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明     |
| -------- | ------ | ---- | ---- | -------- |
| category | string | path | 是   | 配置类别 |

**响应示例**:

---

## 功能代码管理

### 获取所有功能代码

**请求方式**: `GET`

**请求路径**: `/api/system/feature-codes/list`

**请求参数**:

| 参数名          | 类型    | 位置  | 必填 | 说明                     |
| --------------- | ------- | ----- | ---- | ------------------------ |
| includeInactive | boolean | query | 否   | 是否包含未激活的功能代码 |
| forceRefresh    | boolean | query | 否   | 是否强制刷新缓存         |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "code": "0",
    "data": "[{\n  \"id\": \"1\",\n  \"code\": \"\\\"ai.ppt\\\"\",\n  \"name\": \"\\\"AI PPT生成\\\"\",\n  \"description\": \"\\\"AI PPT生成功能\\\"\",\n  \"module\": \"\\\"AI模块\\\"\",\n  \"isActive\": \"true\",\n  \"sortOrder\": \"0\",\n  \"metadata\": \"{}\"\n}]",
    "message": "\"获取成功\""
  }
}
```

---

### 获取功能代码详情

**请求方式**: `GET`

**请求路径**: `/api/system/feature-codes/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 功能代码 |

**响应示例**:

---

### 删除功能代码

**请求方式**: `DELETE`

**请求路径**: `/api/system/feature-codes/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 功能代码 |

**响应示例**:

---

### 创建或更新功能代码

**请求方式**: `POST`

**请求路径**: `/api/system/feature-codes/upsert`

**请求体**:

```json
{}
```

**响应示例**:

---

## 功能菜单管理

### 获取功能代码关联的菜单

**请求方式**: `GET`

**请求路径**: `/api/feature-menu/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 功能代码 |

**响应示例**:

---

### 设置功能代码关联的菜单

**请求方式**: `POST`

**请求路径**: `/api/feature-menu/{code}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明     |
| ------ | ------ | ---- | ---- | -------- |
| code   | string | path | 是   | 功能代码 |

**响应示例**:

---

## 功能模板菜单管理

### 获取功能模板关联的菜单

**请求方式**: `GET`

**请求路径**: `/api/system/feature-templates/{code}/menus`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明         |
| ------ | ------ | ---- | ---- | ------------ |
| code   | string | path | 是   | 功能模板代码 |

**响应示例**:

---

## 租户功能菜单管理

### 获取租户可用菜单

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-features/{tenantId}/menus`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明   |
| -------- | ------ | ---- | ---- | ------ |
| tenantId | number | path | 是   | 租户ID |

**响应示例**:

---

### 同步租户菜单

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-features/{tenantId}/sync-menus`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明   |
| -------- | ------ | ---- | ---- | ------ |
| tenantId | number | path | 是   | 租户ID |

**响应示例**:

---

## membership-plans

### 创建会员计划

**请求方式**: `POST`

**请求路径**: `/api/membership/plans`

**请求体**:

```json
{
  "code": "\"basic\"",
  "name": "\"基础版\"",
  "description": "\"适合个人用户的基础功能套餐\"",
  "price": "99",
  "originalPrice": "199",
  "billingCycle": "\"monthly\"",
  "features": "{}",
  "isActive": "true",
  "sortOrder": "1"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "1",
    "tenantId": "1",
    "code": "\"basic\"",
    "name": "\"基础版\"",
    "description": "\"适合个人用户的基础功能套餐\"",
    "price": "99",
    "originalPrice": "199",
    "billingCycle": "\"monthly\"",
    "features": "{}",
    "isActive": "true",
    "sortOrder": "1",
    "createTime": "\"2025-06-01 12:00:00\"",
    "updateTime": "\"2025-06-01 12:00:00\""
  }
}
```

---

### 获取会员计划列表

**请求方式**: `GET`

**请求路径**: `/api/membership/plans/list`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明               |
| -------- | ------ | ----- | ---- | ------------------ |
| page     | number | query | 否   | 页码，默认为1      |
| pageSize | number | query | 否   | 每页数量，默认为10 |
| status   | string | query | 否   | 状态筛选           |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "tenantId": "1",
      "code": "\"basic\"",
      "name": "\"基础版\"",
      "description": "\"适合个人用户的基础功能套餐\"",
      "price": "99",
      "originalPrice": "199",
      "billingCycle": "\"monthly\"",
      "features": "{}",
      "isActive": "true",
      "sortOrder": "1",
      "createTime": "\"2025-06-01 12:00:00\"",
      "updateTime": "\"2025-06-01 12:00:00\""
    }
  ]
}
```

---

### 获取会员计划详情

**请求方式**: `GET`

**请求路径**: `/api/membership/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "1",
    "tenantId": "1",
    "code": "\"basic\"",
    "name": "\"基础版\"",
    "description": "\"适合个人用户的基础功能套餐\"",
    "price": "99",
    "originalPrice": "199",
    "billingCycle": "\"monthly\"",
    "features": "{}",
    "isActive": "true",
    "sortOrder": "1",
    "createTime": "\"2025-06-01 12:00:00\"",
    "updateTime": "\"2025-06-01 12:00:00\""
  }
}
```

---

### 更新会员计划

**请求方式**: `PUT`

**请求路径**: `/api/membership/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**请求体**:

```json
{
  "name": "\"基础版Plus\"",
  "description": "\"适合个人用户的增强功能套餐\"",
  "price": "129",
  "originalPrice": "199",
  "features": "{}",
  "isActive": "true",
  "sortOrder": "2"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "1",
    "tenantId": "1",
    "code": "\"basic\"",
    "name": "\"基础版\"",
    "description": "\"适合个人用户的基础功能套餐\"",
    "price": "99",
    "originalPrice": "199",
    "billingCycle": "\"monthly\"",
    "features": "{}",
    "isActive": "true",
    "sortOrder": "1",
    "createTime": "\"2025-06-01 12:00:00\"",
    "updateTime": "\"2025-06-01 12:00:00\""
  }
}
```

---

### 删除会员计划

**请求方式**: `DELETE`

**请求路径**: `/api/membership/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | string | path | 是   |      |

**响应示例**:

---

### 检查计划代码是否存在

**请求方式**: `GET`

**请求路径**: `/api/membership/plans/code-exists`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明     |
| ------ | ------ | ----- | ---- | -------- |
| code   | string | query | 是   | 计划代码 |

**响应示例**:

---

## 租户订阅管理

### 创建订阅计划

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-subscription/plans`

**请求体**:

```json
{
  "code": "\"basic\"",
  "name": "\"基础版\"",
  "description": "\"string\"",
  "price": "99",
  "originalPrice": "199",
  "billingCycle": "\"monthly\"",
  "features": "{}",
  "isActive": "true",
  "sortOrder": "0",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "price": "0",
    "originalPrice": "0",
    "billingCycle": "\"string\"",
    "features": "{}",
    "isActive": "true",
    "status": "\"string\"",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 获取订阅计划列表

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/plans`

**请求参数**:

| 参数名   | 类型    | 位置  | 必填 | 说明 |
| -------- | ------- | ----- | ---- | ---- |
| status   | string  | query | 否   |      |
| isActive | boolean | query | 否   |      |
| page     | number  | query | 否   |      |
| pageSize | number  | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "code": "\"string\"",
      "name": "\"string\"",
      "description": "\"string\"",
      "price": "0",
      "originalPrice": "0",
      "billingCycle": "\"string\"",
      "features": "{}",
      "isActive": "true",
      "status": "\"string\"",
      "sortOrder": "0",
      "metadata": "{}",
      "createTime": "\"string\"",
      "updateTime": "\"string\""
    }
  ]
}
```

---

### 获取订阅计划详情

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "price": "0",
    "originalPrice": "0",
    "billingCycle": "\"string\"",
    "features": "{}",
    "isActive": "true",
    "status": "\"string\"",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 更新订阅计划

**请求方式**: `PUT`

**请求路径**: `/api/system/tenant-subscription/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "code": "\"basic\"",
  "name": "\"基础版\"",
  "description": "\"string\"",
  "price": "99",
  "originalPrice": "199",
  "billingCycle": "\"monthly\"",
  "features": "{}",
  "isActive": "true",
  "sortOrder": "0",
  "metadata": "{}",
  "status": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "price": "0",
    "originalPrice": "0",
    "billingCycle": "\"string\"",
    "features": "{}",
    "isActive": "true",
    "status": "\"string\"",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 删除订阅计划

**请求方式**: `DELETE`

**请求路径**: `/api/system/tenant-subscription/plans/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

---

### 创建租户订阅

**请求方式**: `POST`

**请求路径**: `/api/system/tenant-subscription/subscriptions`

**请求体**:

```json
{
  "tenantId": "\"string\"",
  "planId": "0",
  "duration": "0",
  "billingCycle": "\"string\"",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "autoRenew": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "tenantName": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "duration": "0",
    "billingCycle": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 获取租户订阅列表

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/subscriptions`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明 |
| -------- | ------ | ----- | ---- | ---- |
| tenantId | string | query | 否   |      |
| planId   | number | query | 否   |      |
| status   | string | query | 否   |      |
| page     | number | query | 否   |      |
| pageSize | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "tenantId": "\"string\"",
      "tenantName": "\"string\"",
      "planId": "0",
      "planName": "\"string\"",
      "planCode": "\"string\"",
      "duration": "0",
      "billingCycle": "\"string\"",
      "startDate": "\"string\"",
      "endDate": "\"string\"",
      "status": "\"string\"",
      "autoRenew": "true",
      "metadata": "{}",
      "createTime": "\"string\"",
      "updateTime": "\"string\""
    }
  ]
}
```

---

### 获取租户订阅详情

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/subscriptions/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "tenantName": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "duration": "0",
    "billingCycle": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 更新租户订阅

**请求方式**: `PUT`

**请求路径**: `/api/system/tenant-subscription/subscriptions/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "tenantId": "\"string\"",
  "planId": "0",
  "duration": "0",
  "billingCycle": "\"string\"",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "autoRenew": "true",
  "metadata": "{}",
  "status": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "tenantName": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "duration": "0",
    "billingCycle": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 取消租户订阅

**请求方式**: `DELETE`

**请求路径**: `/api/system/tenant-subscription/subscriptions/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

---

### 获取订阅统计数据

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/stats`

**响应示例**:

---

### 获取租户订阅历史

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/tenant/{tenantId}/history`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明 |
| -------- | ------ | ---- | ---- | ---- |
| tenantId | string | path | 是   |      |

**响应示例**:

---

### 获取租户活跃订阅

**请求方式**: `GET`

**请求路径**: `/api/system/tenant-subscription/tenant/{tenantId}/active`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明 |
| -------- | ------ | ---- | ---- | ---- |
| tenantId | string | path | 是   |      |

**响应示例**:

---

## 租户会员管理

### 创建用户会员

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users`

**请求体**:

```json
{
  "userId": "0",
  "planId": "0",
  "duration": "0",
  "billingCycle": "\"string\"",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "autoRenew": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "userId": "0",
    "username": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 获取用户会员列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/users/list`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明 |
| -------- | ------ | ----- | ---- | ---- |
| userId   | number | query | 否   |      |
| planId   | number | query | 否   |      |
| status   | string | query | 否   |      |
| page     | number | query | 否   |      |
| pageSize | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取用户会员详情

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "userId": "0",
    "username": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 更新用户会员

**请求方式**: `PUT`

**请求路径**: `/api/tenant/membership/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "userId": "0",
  "planId": "0",
  "duration": "0",
  "billingCycle": "\"string\"",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "autoRenew": "true",
  "metadata": "{}",
  "status": "\"string\""
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "userId": "0",
    "username": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 取消用户会员

**请求方式**: `DELETE`

**请求路径**: `/api/tenant/membership/users/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

---

### 检查用户会员权益

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users/benefit/check`

**请求体**:

```json
{
  "userId": "0",
  "benefitCode": "\"string\"",
  "quantity": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "hasAccess": "true",
    "hasQuota": "true",
    "totalQuota": "0",
    "usedQuota": "0",
    "remainingQuota": "0",
    "benefit": "{}",
    "plan": "{}"
  }
}
```

---

### 使用会员权益

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users/benefit/use`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明 |
| ------ | ------ | ----- | ---- | ---- |
| userId | number | query | 是   |      |

**请求体**:

```json
{
  "benefitCode": "\"string\"",
  "quantity": "0",
  "metadata": "{}"
}
```

**响应示例**:

---

### 激活会员卡密

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users/card/activate`

**请求体**:

```json
{
  "userId": "0",
  "cardCode": "\"string\""
}
```

**响应示例**:

---

### 获取会员统计数据

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/users/stats`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "totalMembers": "0",
    "activeMembers": "0",
    "expiringMembers": "0",
    "expiredMembers": "0",
    "planDistribution": "[\"string\"]"
  }
}
```

---

### 处理过期会员（系统调用）

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users/expired/process`

**响应示例**:

---

### 处理自动续费（系统调用）

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/users/renewal/process`

**响应示例**:

---

## 用户会员

### 获取当前用户会员信息

**请求方式**: `GET`

**请求路径**: `/api/membership/info`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明 |
| ------ | ------ | ----- | ---- | ---- |
| userId | number | query | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "userId": "0",
    "username": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "planCode": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "status": "\"string\"",
    "autoRenew": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 用户激活会员卡密

**请求方式**: `POST`

**请求路径**: `/api/membership/card/activate`

**请求体**:

```json
{
  "userId": "0",
  "cardCode": "\"string\""
}
```

**响应示例**:

---

### 检查用户是否有权益

**请求方式**: `GET`

**请求路径**: `/api/membership/benefit/check`

**请求参数**:

| 参数名      | 类型   | 位置  | 必填 | 说明 |
| ----------- | ------ | ----- | ---- | ---- |
| userId      | number | query | 是   |      |
| benefitCode | string | query | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "hasAccess": "true",
    "hasQuota": "true",
    "totalQuota": "0",
    "usedQuota": "0",
    "remainingQuota": "0",
    "benefit": "{}",
    "plan": "{}"
  }
}
```

---

## 租户会员权益管理

### 创建会员权益

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/benefits`

**请求体**:

```json
{
  "code": "\"ai_ppt_pro\"",
  "name": "\"AI PPT专业版\"",
  "description": "\"使用AI生成专业级PPT\"",
  "category": "\"ai\"",
  "icon": "\"ppt-icon\"",
  "details": "\"可使用所有专业模板，支持高级编辑功能\"",
  "sortOrder": "1",
  "isActive": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "category": "\"string\"",
    "icon": "\"string\"",
    "details": "\"string\"",
    "isActive": "true",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 获取会员权益列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/benefits/list`

**请求参数**:

| 参数名   | 类型    | 位置  | 必填 | 说明 |
| -------- | ------- | ----- | ---- | ---- |
| category | string  | query | 否   |      |
| isActive | boolean | query | 否   |      |
| page     | number  | query | 否   |      |
| pageSize | number  | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取会员权益详情

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/benefits/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "category": "\"string\"",
    "icon": "\"string\"",
    "details": "\"string\"",
    "isActive": "true",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 更新会员权益

**请求方式**: `PUT`

**请求路径**: `/api/tenant/membership/benefits/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "code": "\"ai_ppt_pro\"",
  "name": "\"AI PPT专业版\"",
  "description": "\"使用AI生成专业级PPT\"",
  "category": "\"ai\"",
  "icon": "\"ppt-icon\"",
  "details": "\"可使用所有专业模板，支持高级编辑功能\"",
  "sortOrder": "1",
  "isActive": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "category": "\"string\"",
    "icon": "\"string\"",
    "details": "\"string\"",
    "isActive": "true",
    "sortOrder": "0",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 删除会员权益

**请求方式**: `DELETE`

**请求路径**: `/api/tenant/membership/benefits/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

---

## 计划权益关联

### 关联会员权益到会员计划

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/plans/{planId}/benefits`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| planId | number | path | 是   |      |

**请求体**:

```json
{
  "benefitIds": "[0]",
  "quotas": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "planId": "0",
    "planName": "\"string\"",
    "benefits": "[{}]"
  }
}
```

---

### 获取会员计划的权益列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/plans/{planId}/benefits`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| planId | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "planId": "0",
    "planName": "\"string\"",
    "benefits": "[{}]"
  }
}
```

---

## 用户会员权益

### 获取用户可用的会员权益列表

**请求方式**: `GET`

**请求路径**: `/api/membership/benefits`

**请求参数**:

| 参数名   | 类型   | 位置  | 必填 | 说明 |
| -------- | ------ | ----- | ---- | ---- |
| userId   | number | query | 是   |      |
| category | string | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "tenantId": "\"string\"",
      "code": "\"string\"",
      "name": "\"string\"",
      "description": "\"string\"",
      "category": "\"string\"",
      "icon": "\"string\"",
      "details": "\"string\"",
      "isActive": "true",
      "sortOrder": "0",
      "metadata": "{}",
      "createTime": "\"string\"",
      "updateTime": "\"string\""
    }
  ]
}
```

---

## 租户虚拟币类型管理

### 创建虚拟币类型

**请求方式**: `POST`

**请求路径**: `/api/tenant/virtual-currency/types`

**请求体**:

```json
{
  "code": "\"ai_coin\"",
  "name": "\"AI币\"",
  "description": "\"用于AI功能消费的虚拟币\"",
  "icon": "\"coin-icon\"",
  "exchangeRate": "1",
  "isActive": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "icon": "\"string\"",
    "exchangeRate": "0",
    "isActive": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 获取虚拟币类型列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/virtual-currency/types/list`

**请求参数**:

| 参数名   | 类型    | 位置  | 必填 | 说明 |
| -------- | ------- | ----- | ---- | ---- |
| isActive | boolean | query | 否   |      |
| page     | number  | query | 否   |      |
| pageSize | number  | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取虚拟币类型详情

**请求方式**: `GET`

**请求路径**: `/api/tenant/virtual-currency/types/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "icon": "\"string\"",
    "exchangeRate": "0",
    "isActive": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 更新虚拟币类型

**请求方式**: `PUT`

**请求路径**: `/api/tenant/virtual-currency/types/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "code": "\"ai_coin\"",
  "name": "\"AI币\"",
  "description": "\"用于AI功能消费的虚拟币\"",
  "icon": "\"coin-icon\"",
  "exchangeRate": "1",
  "isActive": "true",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "code": "\"string\"",
    "name": "\"string\"",
    "description": "\"string\"",
    "icon": "\"string\"",
    "exchangeRate": "0",
    "isActive": "true",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

## 租户虚拟币钱包管理

### 用户充值虚拟币

**请求方式**: `POST`

**请求路径**: `/api/tenant/virtual-currency/recharge`

**请求体**:

```json
{
  "userId": "0",
  "currencyTypeId": "0",
  "amount": "0",
  "remark": "\"string\"",
  "orderNo": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

---

### 用户消费虚拟币

**请求方式**: `POST`

**请求路径**: `/api/tenant/virtual-currency/consume`

**请求体**:

```json
{
  "userId": "0",
  "currencyTypeId": "0",
  "amount": "0",
  "description": "\"string\"",
  "featureCode": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

---

### 查询用户钱包余额

**请求方式**: `GET`

**请求路径**: `/api/tenant/virtual-currency/wallets`

**请求参数**:

| 参数名         | 类型   | 位置  | 必填 | 说明 |
| -------------- | ------ | ----- | ---- | ---- |
| userId         | number | query | 是   |      |
| currencyTypeId | number | query | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "userId": "0",
      "currencyTypeId": "0",
      "currencyCode": "\"string\"",
      "currencyName": "\"string\"",
      "balance": "0",
      "icon": "\"string\"",
      "metadata": "{}"
    }
  ]
}
```

---

### 查询用户交易记录

**请求方式**: `GET`

**请求路径**: `/api/tenant/virtual-currency/transactions`

**请求参数**:

| 参数名         | 类型   | 位置  | 必填 | 说明 |
| -------------- | ------ | ----- | ---- | ---- |
| userId         | number | query | 是   |      |
| currencyTypeId | number | query | 否   |      |
| type           | string | query | 否   |      |
| startTime      | string | query | 否   |      |
| endTime        | string | query | 否   |      |
| page           | number | query | 否   |      |
| pageSize       | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 设置功能虚拟币消耗

**请求方式**: `POST`

**请求路径**: `/api/tenant/virtual-currency/features/{featureCode}/cost`

**请求参数**:

| 参数名      | 类型   | 位置 | 必填 | 说明 |
| ----------- | ------ | ---- | ---- | ---- |
| featureCode | string | path | 是   |      |

**请求体**:

```json
{
  "currencyCode": "\"string\"",
  "cost": "0",
  "memberDiscounts": "{}",
  "bulkDiscounts": "[\"string\"]",
  "isActive": "true"
}
```

**响应示例**:

---

## 用户虚拟币

### 查询虚拟币余额

**请求方式**: `GET`

**请求路径**: `/api/virtual-currency/balance`

**请求参数**:

| 参数名         | 类型   | 位置  | 必填 | 说明 |
| -------------- | ------ | ----- | ---- | ---- |
| userId         | number | query | 是   |      |
| currencyTypeId | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "userId": "0",
      "currencyTypeId": "0",
      "currencyCode": "\"string\"",
      "currencyName": "\"string\"",
      "balance": "0",
      "icon": "\"string\"",
      "metadata": "{}"
    }
  ]
}
```

---

### 查询虚拟币交易记录

**请求方式**: `GET`

**请求路径**: `/api/virtual-currency/transactions/list`

**请求参数**:

| 参数名         | 类型   | 位置  | 必填 | 说明 |
| -------------- | ------ | ----- | ---- | ---- |
| userId         | number | query | 是   |      |
| currencyTypeId | number | query | 否   |      |
| type           | string | query | 否   |      |
| startTime      | string | query | 否   |      |
| endTime        | string | query | 否   |      |
| page           | number | query | 否   |      |
| pageSize       | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

## 租户会员卡管理

### 创建会员卡批次

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/cards`

**请求体**:

```json
{
  "planId": "0",
  "prefix": "\"VIP\"",
  "quantity": "10",
  "duration": "1",
  "batchNote": "\"618促销批次\"",
  "codeLength": "0",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

---

### 查询会员卡列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/cards/list`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明 |
| --------- | ------ | ----- | ---- | ---- |
| planId    | number | query | 否   |      |
| status    | string | query | 否   |      |
| batchNote | string | query | 否   |      |
| startDate | string | query | 否   |      |
| endDate   | string | query | 否   |      |
| page      | number | query | 否   |      |
| pageSize  | number | query | 否   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 查询会员卡批次统计

**请求方式**: `GET`

**请求路径**: `/api/tenant/membership/cards/stats`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "batchNote": "\"string\"",
      "planId": "0",
      "planName": "\"string\"",
      "totalCount": "0",
      "activatedCount": "0",
      "inactiveCount": "0",
      "expiredCount": "0",
      "activationRate": "\"string\"",
      "createTime": "\"string\""
    }
  ]
}
```

---

### 更新会员卡

**请求方式**: `PUT`

**请求路径**: `/api/tenant/membership/cards/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明 |
| ------ | ------ | ---- | ---- | ---- |
| id     | number | path | 是   |      |

**请求体**:

```json
{
  "status": "\"string\"",
  "batchNote": "\"string\"",
  "startDate": "\"string\"",
  "endDate": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "tenantId": "\"string\"",
    "planId": "0",
    "planName": "\"string\"",
    "cardCode": "\"string\"",
    "status": "\"string\"",
    "duration": "0",
    "batchNote": "\"string\"",
    "activatedUserId": "0",
    "activatedTime": "\"string\"",
    "startDate": "\"string\"",
    "endDate": "\"string\"",
    "metadata": "{}",
    "createTime": "\"string\"",
    "updateTime": "\"string\""
  }
}
```

---

### 导出会员卡

**请求方式**: `POST`

**请求路径**: `/api/tenant/membership/cards/export`

**请求体**:

```json
{
  "planId": "0",
  "status": "\"string\"",
  "batchNote": "\"string\"",
  "format": "\"string\""
}
```

**响应示例**:

---

## 用户会员卡

### 激活会员卡

**请求方式**: `POST`

**请求路径**: `/api/membership/cards/activate`

**请求体**:

```json
{
  "userId": "0",
  "cardCode": "\"string\""
}
```

**响应示例**:

---

## 支付

### 创建支付订单

**请求方式**: `POST`

**请求路径**: `/api/payment/create`

**请求体**:

```json
{
  "businessType": "\"membership\"",
  "businessId": "\"1\"",
  "amount": "299",
  "paymentMethod": "\"alipay\"",
  "subject": "\"会员订阅-高级版\"",
  "description": "\"购买3个月高级会员\"",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "orderNo": "\"P2305010001\"",
    "amount": "299",
    "paymentUrl": "\"https://payment-gateway.com/pay?orderNo=P2305010001\"",
    "qrCode": "\"base64-encoded-qr-code-image\""
  }
}
```

---

### 查询支付状态

**请求方式**: `GET`

**请求路径**: `/api/payment/status/{orderNo}`

**请求参数**:

| 参数名  | 类型   | 位置 | 必填 | 说明 |
| ------- | ------ | ---- | ---- | ---- |
| orderNo | string | path | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "1",
    "orderNo": "\"P2305010001\"",
    "userId": "1",
    "username": "\"user123\"",
    "amount": "299",
    "status": "\"success\"",
    "paymentMethod": "\"alipay\"",
    "businessType": "\"membership\"",
    "businessId": "\"1\"",
    "subject": "\"会员订阅-高级版\"",
    "createTime": "\"2023-05-01T10:30:00Z\"",
    "paymentTime": "\"2023-05-01T10:35:00Z\"",
    "metadata": "{}"
  }
}
```

---

### 申请退款

**请求方式**: `POST`

**请求路径**: `/api/payment/refund`

**请求体**:

```json
{
  "orderNo": "\"P2305010001\"",
  "amount": "299",
  "reason": "\"不想要了\""
}
```

**响应示例**:

---

### 支付回调接口

**请求方式**: `POST`

**请求路径**: `/api/payment/callback/{provider}`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明 |
| -------- | ------ | ---- | ---- | ---- |
| provider | string | path | 是   |      |

**响应示例**:

---

### 测试用-手动更新支付状态

**请求方式**: `PUT`

**请求路径**: `/api/payment/test/update-status/{orderNo}`

**请求参数**:

| 参数名  | 类型   | 位置 | 必填 | 说明 |
| ------- | ------ | ---- | ---- | ---- |
| orderNo | string | path | 是   |      |

**响应示例**:

---

## 租户支付管理

### 获取支付订单列表

**请求方式**: `GET`

**请求路径**: `/api/tenant/payment/orders/list`

**请求参数**:

| 参数名       | 类型   | 位置  | 必填 | 说明 |
| ------------ | ------ | ----- | ---- | ---- |
| businessType | string | query | 是   |      |
| status       | string | query | 是   |      |
| startTime    | string | query | 是   |      |
| endTime      | string | query | 是   |      |
| page         | number | query | 是   |      |
| pageSize     | number | query | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取支付统计数据

**请求方式**: `GET`

**请求路径**: `/api/tenant/payment/statistics`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明 |
| --------- | ------ | ----- | ---- | ---- |
| startTime | string | query | 是   |      |
| endTime   | string | query | 是   |      |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "totalRevenue": "9999",
    "successOrders": "100",
    "pendingOrders": "20",
    "failedOrders": "5",
    "refundedAmount": "299",
    "paymentMethodDistribution": "[{}]",
    "businessTypeDistribution": "[{}]",
    "dailyRevenue": "[{}]"
  }
}
```

---

### 处理退款申请

**请求方式**: `POST`

**请求路径**: `/api/tenant/payment/refund/process`

**请求体**:

```json
{
  "refundNo": "\"R2305010001\"",
  "action": "\"approve\"",
  "comment": "\"同意退款\""
}
```

**响应示例**:

---

### 测试用-手动更新支付状态

**请求方式**: `PUT`

**请求路径**: `/api/tenant/payment/test/update-status/{orderNo}`

**请求参数**:

| 参数名  | 类型   | 位置 | 必填 | 说明 |
| ------- | ------ | ---- | ---- | ---- |
| orderNo | string | path | 是   |      |

**响应示例**:

---

## 网站模板管理

### 创建网站模板

**请求方式**: `POST`

**请求路径**: `/api/website-templates`

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "category": "\"string\"",
  "industry": "\"string\"",
  "thumbnail": "\"string\"",
  "preview": "\"string\"",
  "config": "{}",
  "features": "{}",
  "isPremium": "true",
  "isActive": "true",
  "sortOrder": "0",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取模板列表

**请求方式**: `GET`

**请求路径**: `/api/website-templates`

**请求参数**:

| 参数名    | 类型    | 位置  | 必填 | 说明         |
| --------- | ------- | ----- | ---- | ------------ |
| page      | number  | query | 否   | 页码         |
| limit     | number  | query | 否   | 每页数量     |
| keyword   | string  | query | 否   | 关键词搜索   |
| category  | string  | query | 否   | 模板分类过滤 |
| industry  | string  | query | 否   | 行业分类过滤 |
| isPremium | boolean | query | 否   | 是否付费模板 |
| isActive  | boolean | query | 否   | 是否激活     |
| sortBy    | string  | query | 否   | 排序字段     |
| sortOrder | string  | query | 否   | 排序方向     |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取模板分类列表

**请求方式**: `GET`

**请求路径**: `/api/website-templates/categories`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取行业分类列表

**请求方式**: `GET`

**请求路径**: `/api/website-templates/industries`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 获取单个模板详情

**请求方式**: `GET`

**请求路径**: `/api/website-templates/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 模板ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 更新模板信息

**请求方式**: `PATCH`

**请求路径**: `/api/website-templates/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 模板ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "category": "\"string\"",
  "industry": "\"string\"",
  "thumbnail": "\"string\"",
  "preview": "\"string\"",
  "config": "{}",
  "features": "{}",
  "isPremium": "true",
  "isActive": "true",
  "sortOrder": "0",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 删除模板

**请求方式**: `DELETE`

**请求路径**: `/api/website-templates/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 模板ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

### 复制模板

**请求方式**: `POST`

**请求路径**: `/api/website-templates/{id}/duplicate`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明       |
| ------ | ------ | ----- | ---- | ---------- |
| id     | number | path  | 是   | 源模板ID   |
| name   | string | query | 是   | 新模板名称 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": null
}
```

---

## 网站管理

### 创建网站

**请求方式**: `POST`

**请求路径**: `/api/websites`

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "domain": "\"string\"",
  "subdomain": "\"string\"",
  "templateId": "0",
  "favicon": "\"string\"",
  "logo": "\"string\"",
  "seoConfig": "{}",
  "themeConfig": "{}",
  "settings": "{}",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

### 获取网站列表

**请求方式**: `GET`

**请求路径**: `/api/websites`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明             |
| ---------------- | ------- | ----- | ---- | ---------------- |
| page             | number  | query | 否   | 页码             |
| limit            | number  | query | 否   | 每页数量         |
| keyword          | string  | query | 否   | 关键词搜索       |
| status           | string  | query | 否   | 网站状态过滤     |
| templateId       | number  | query | 否   | 模板ID过滤       |
| userId           | number  | query | 否   | 创建者ID过滤     |
| sortBy           | string  | query | 否   | 排序字段         |
| sortOrder        | string  | query | 否   | 排序方向         |
| includeRelations | boolean | query | 否   | 是否包含关联数据 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"name\": \"{}\",\n  \"description\": \"{}\",\n  \"domain\": \"{}\",\n  \"subdomain\": \"{}\",\n  \"templateId\": \"{}\",\n  \"favicon\": \"{}\",\n  \"logo\": \"{}\",\n  \"status\": \"{}\",\n  \"seoConfig\": \"{}\",\n  \"themeConfig\": \"{}\",\n  \"settings\": \"{}\",\n  \"metadata\": \"{}\",\n  \"publishedAt\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"userId\": \"{}\",\n  \"createTime\": \"{}\",\n  \"updateTime\": \"{}\",\n  \"template\": \"{}\",\n  \"user\": \"{}\",\n  \"pageCount\": \"{}\",\n  \"mediaCount\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取网站统计信息

**请求方式**: `GET`

**请求路径**: `/api/websites/stats`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "total": "0",
    "published": "0",
    "draft": "0",
    "archived": "0"
  }
}
```

---

### 获取单个网站详情

**请求方式**: `GET`

**请求路径**: `/api/websites/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

### 更新网站信息

**请求方式**: `PATCH`

**请求路径**: `/api/websites/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 网站ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "domain": "\"string\"",
  "subdomain": "\"string\"",
  "templateId": "0",
  "favicon": "\"string\"",
  "logo": "\"string\"",
  "seoConfig": "{}",
  "themeConfig": "{}",
  "settings": "{}",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

### 删除网站

**请求方式**: `DELETE`

**请求路径**: `/api/websites/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 网站ID |

**响应示例**:

---

### 发布网站

**请求方式**: `POST`

**请求路径**: `/api/websites/{id}/publish`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

### 取消发布网站

**请求方式**: `POST`

**请求路径**: `/api/websites/{id}/unpublish`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

### 基于模板创建网站

**请求方式**: `POST`

**请求路径**: `/api/websites/from-template/{templateId}`

**请求参数**:

| 参数名     | 类型   | 位置 | 必填 | 说明   |
| ---------- | ------ | ---- | ---- | ------ |
| templateId | number | path | 是   | 模板ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "domain": "\"string\"",
    "subdomain": "\"string\"",
    "templateId": "0",
    "favicon": "\"string\"",
    "logo": "\"string\"",
    "status": "\"string\"",
    "seoConfig": "{}",
    "themeConfig": "{}",
    "settings": "{}",
    "metadata": "{}",
    "publishedAt": "\"string\"",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "template": "{}",
    "user": "{}",
    "pageCount": "0",
    "mediaCount": "0"
  }
}
```

---

## 网站页面管理

### 创建网站页面

**请求方式**: `POST`

**请求路径**: `/api/website-pages`

**请求体**:

```json
{
  "title": "\"string\"",
  "path": "\"string\"",
  "content": "{}",
  "seoMeta": "{}",
  "metadata": "{}",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 获取页面列表

**请求方式**: `GET`

**请求路径**: `/api/website-pages`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明             |
| ---------------- | ------- | ----- | ---- | ---------------- |
| page             | number  | query | 否   | 页码             |
| limit            | number  | query | 否   | 每页数量         |
| keyword          | string  | query | 否   | 关键词搜索       |
| status           | string  | query | 否   | 页面状态过滤     |
| type             | string  | query | 否   | 页面类型过滤     |
| websiteId        | number  | query | 否   | 所属网站ID过滤   |
| isHomePage       | boolean | query | 否   | 是否只显示首页   |
| sortBy           | string  | query | 否   | 排序字段         |
| sortOrder        | string  | query | 否   | 排序方向         |
| includeRelations | boolean | query | 否   | 是否包含关联数据 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"title\": \"{}\",\n  \"path\": \"{}\",\n  \"content\": \"{}\",\n  \"status\": \"{}\",\n  \"seoMeta\": \"{}\",\n  \"metadata\": \"{}\",\n  \"websiteId\": \"{}\",\n  \"createdAt\": \"{}\",\n  \"updatedAt\": \"{}\",\n  \"website\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取页面统计信息

**请求方式**: `GET`

**请求路径**: `/api/website-pages/stats`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明       |
| --------- | ------ | ----- | ---- | ---------- |
| websiteId | number | query | 否   | 网站ID过滤 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "total": "0",
    "published": "0",
    "draft": "0"
  }
}
```

---

### 获取指定网站的页面列表

**请求方式**: `GET`

**请求路径**: `/api/website-pages/by-website/{websiteId}`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "title": "\"string\"",
      "slug": "\"string\"",
      "type": "\"string\"",
      "status": "\"string\"",
      "isHomePage": "true",
      "sortOrder": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\""
    }
  ]
}
```

---

### 获取单个页面详情

**请求方式**: `GET`

**请求路径**: `/api/website-pages/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 页面ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 更新页面信息

**请求方式**: `PATCH`

**请求路径**: `/api/website-pages/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 页面ID |

**请求体**:

```json
{
  "title": "\"string\"",
  "path": "\"string\"",
  "content": "{}",
  "seoMeta": "{}",
  "metadata": "{}",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 删除页面

**请求方式**: `DELETE`

**请求路径**: `/api/website-pages/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 页面ID |

**响应示例**:

---

### 根据路径获取页面

**请求方式**: `GET`

**请求路径**: `/api/website-pages/{websiteId}/{slug}`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明     |
| --------- | ------ | ---- | ---- | -------- |
| websiteId | number | path | 是   | 网站ID   |
| slug      | string | path | 是   | 页面路径 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 发布页面

**请求方式**: `POST`

**请求路径**: `/api/website-pages/{id}/publish`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 页面ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 取消发布页面

**请求方式**: `POST`

**请求路径**: `/api/website-pages/{id}/unpublish`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 页面ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

### 复制页面

**请求方式**: `POST`

**请求路径**: `/api/website-pages/{id}/duplicate`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明       |
| ------ | ------ | ----- | ---- | ---------- |
| id     | number | path  | 是   | 源页面ID   |
| slug   | string | query | 是   | 新页面路径 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "title": "\"string\"",
    "path": "\"string\"",
    "content": "{}",
    "status": "\"string\"",
    "seoMeta": "{}",
    "metadata": "{}",
    "websiteId": "0",
    "createdAt": "\"string\"",
    "updatedAt": "\"string\"",
    "website": "{}"
  }
}
```

---

## 媒体资源管理

### 创建媒体资源

**请求方式**: `POST`

**请求路径**: `/api/media-assets`

**请求体**:

```json
{
  "filename": "\"string\"",
  "originalName": "\"string\"",
  "mimeType": "\"string\"",
  "size": "0",
  "url": "\"string\"",
  "thumbnailUrl": "\"string\"",
  "alt": "\"string\"",
  "category": "\"string\"",
  "tags": "[\"string\"]",
  "metadata": "{}",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "filename": "\"string\"",
    "originalName": "\"string\"",
    "mimeType": "\"string\"",
    "size": "0",
    "url": "\"string\"",
    "thumbnailUrl": "\"string\"",
    "alt": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "metadata": "{}",
    "websiteId": "0",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "user": "{}"
  }
}
```

---

### 获取媒体资源列表

**请求方式**: `GET`

**请求路径**: `/api/media-assets`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明             |
| ---------------- | ------- | ----- | ---- | ---------------- |
| page             | number  | query | 否   | 页码             |
| limit            | number  | query | 否   | 每页数量         |
| keyword          | string  | query | 否   | 关键词搜索       |
| category         | string  | query | 否   | 媒体分类过滤     |
| mimeType         | string  | query | 否   | MIME类型过滤     |
| websiteId        | number  | query | 否   | 所属网站ID过滤   |
| userId           | number  | query | 否   | 上传者ID过滤     |
| tags             | array   | query | 否   | 标签过滤         |
| sortBy           | string  | query | 否   | 排序字段         |
| sortOrder        | string  | query | 否   | 排序方向         |
| includeRelations | boolean | query | 否   | 是否包含关联数据 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"filename\": \"{}\",\n  \"originalName\": \"{}\",\n  \"mimeType\": \"{}\",\n  \"size\": \"{}\",\n  \"url\": \"{}\",\n  \"thumbnailUrl\": \"{}\",\n  \"alt\": \"{}\",\n  \"category\": \"{}\",\n  \"tags\": \"{}\",\n  \"metadata\": \"{}\",\n  \"websiteId\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"userId\": \"{}\",\n  \"createTime\": \"{}\",\n  \"updateTime\": \"{}\",\n  \"website\": \"{}\",\n  \"user\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取媒体资源统计信息

**请求方式**: `GET`

**请求路径**: `/api/media-assets/stats`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明       |
| --------- | ------ | ----- | ---- | ---------- |
| websiteId | number | query | 否   | 网站ID过滤 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "total": "0",
    "byCategory": "{\n  \"images\": \"0\",\n  \"videos\": \"0\",\n  \"documents\": \"0\",\n  \"audio\": \"0\"\n}",
    "totalSize": "0"
  }
}
```

---

### 获取媒体分类列表

**请求方式**: `GET`

**请求路径**: `/api/media-assets/categories`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": ["string"]
}
```

---

### 获取所有标签

**请求方式**: `GET`

**请求路径**: `/api/media-assets/tags`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": ["string"]
}
```

---

### 获取指定网站的媒体资源

**请求方式**: `GET`

**请求路径**: `/api/media-assets/by-website/{websiteId}`

**请求参数**:

| 参数名    | 类型   | 位置  | 必填 | 说明         |
| --------- | ------ | ----- | ---- | ------------ |
| websiteId | number | path  | 是   | 网站ID       |
| category  | string | query | 否   | 媒体分类过滤 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "filename": "\"string\"",
      "originalName": "\"string\"",
      "url": "\"string\"",
      "thumbnailUrl": "\"string\"",
      "mimeType": "\"string\"",
      "category": "\"string\"",
      "size": "0",
      "alt": "\"string\"",
      "tags": "[\"string\"]",
      "createTime": "\"string\""
    }
  ]
}
```

---

### 获取单个媒体资源详情

**请求方式**: `GET`

**请求路径**: `/api/media-assets/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 媒体资源ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "filename": "\"string\"",
    "originalName": "\"string\"",
    "mimeType": "\"string\"",
    "size": "0",
    "url": "\"string\"",
    "thumbnailUrl": "\"string\"",
    "alt": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "metadata": "{}",
    "websiteId": "0",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "user": "{}"
  }
}
```

---

### 更新媒体资源信息

**请求方式**: `PATCH`

**请求路径**: `/api/media-assets/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 媒体资源ID |

**请求体**:

```json
{
  "filename": "\"string\"",
  "originalName": "\"string\"",
  "mimeType": "\"string\"",
  "size": "0",
  "url": "\"string\"",
  "thumbnailUrl": "\"string\"",
  "alt": "\"string\"",
  "category": "\"string\"",
  "tags": "[\"string\"]",
  "metadata": "{}",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "filename": "\"string\"",
    "originalName": "\"string\"",
    "mimeType": "\"string\"",
    "size": "0",
    "url": "\"string\"",
    "thumbnailUrl": "\"string\"",
    "alt": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "metadata": "{}",
    "websiteId": "0",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "user": "{}"
  }
}
```

---

### 删除媒体资源

**请求方式**: `DELETE`

**请求路径**: `/api/media-assets/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 媒体资源ID |

**响应示例**:

---

### 移动媒体资源到指定网站

**请求方式**: `POST`

**请求路径**: `/api/media-assets/move-to-website`

**请求体**:

```json
{
  "mediaIds": "[0]",
  "websiteId": "0"
}
```

**响应示例**:

---

### 添加标签

**请求方式**: `POST`

**请求路径**: `/api/media-assets/{id}/tags`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 媒体资源ID |

**请求体**:

```json
{
  "tags": "[\"string\"]"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "filename": "\"string\"",
    "originalName": "\"string\"",
    "mimeType": "\"string\"",
    "size": "0",
    "url": "\"string\"",
    "thumbnailUrl": "\"string\"",
    "alt": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "metadata": "{}",
    "websiteId": "0",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "user": "{}"
  }
}
```

---

### 移除标签

**请求方式**: `DELETE`

**请求路径**: `/api/media-assets/{id}/tags`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 媒体资源ID |

**请求体**:

```json
{
  "tags": "[\"string\"]"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "filename": "\"string\"",
    "originalName": "\"string\"",
    "mimeType": "\"string\"",
    "size": "0",
    "url": "\"string\"",
    "thumbnailUrl": "\"string\"",
    "alt": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "metadata": "{}",
    "websiteId": "0",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "user": "{}"
  }
}
```

---

## 组件库管理

### 创建组件

**请求方式**: `POST`

**请求路径**: `/api/component-library`

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "type": "\"string\"",
  "category": "\"string\"",
  "tags": "[\"string\"]",
  "schema": "{}",
  "defaultProps": "{}",
  "styleConfig": "{}",
  "previewImage": "\"string\"",
  "icon": "\"string\"",
  "isSystem": "true",
  "isActive": "true",
  "sortOrder": "0",
  "version": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "type": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "schema": "{}",
    "defaultProps": "{}",
    "styleConfig": "{}",
    "previewImage": "\"string\"",
    "icon": "\"string\"",
    "isSystem": "true",
    "isActive": "true",
    "sortOrder": "0",
    "version": "\"string\"",
    "metadata": "{}",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "user": "{}",
    "usageCount": "0"
  }
}
```

---

### 获取组件列表

**请求方式**: `GET`

**请求路径**: `/api/component-library`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明               |
| ---------------- | ------- | ----- | ---- | ------------------ |
| page             | number  | query | 否   | 页码               |
| limit            | number  | query | 否   | 每页数量           |
| keyword          | string  | query | 否   | 关键词搜索         |
| type             | string  | query | 否   | 组件类型过滤       |
| category         | string  | query | 否   | 组件分类过滤       |
| tags             | array   | query | 否   | 标签过滤           |
| isSystem         | boolean | query | 否   | 是否只显示系统组件 |
| isActive         | boolean | query | 否   | 是否只显示激活组件 |
| userId           | number  | query | 否   | 创建者ID过滤       |
| sortBy           | string  | query | 否   | 排序字段           |
| sortOrder        | string  | query | 否   | 排序方向           |
| includeRelations | boolean | query | 否   | 是否包含关联数据   |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"name\": \"{}\",\n  \"description\": \"{}\",\n  \"type\": \"{}\",\n  \"category\": \"{}\",\n  \"tags\": \"{}\",\n  \"schema\": \"{}\",\n  \"defaultProps\": \"{}\",\n  \"styleConfig\": \"{}\",\n  \"previewImage\": \"{}\",\n  \"icon\": \"{}\",\n  \"isSystem\": \"{}\",\n  \"isActive\": \"{}\",\n  \"sortOrder\": \"{}\",\n  \"version\": \"{}\",\n  \"metadata\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"userId\": \"{}\",\n  \"createTime\": \"{}\",\n  \"updateTime\": \"{}\",\n  \"user\": \"{}\",\n  \"usageCount\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取组件统计信息

**请求方式**: `GET`

**请求路径**: `/api/component-library/stats`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "total": "0",
    "active": "0",
    "system": "0",
    "custom": "0"
  }
}
```

---

### 获取组件分类列表

**请求方式**: `GET`

**请求路径**: `/api/component-library/categories`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": ["string"]
}
```

---

### 获取指定分类的组件

**请求方式**: `GET`

**请求路径**: `/api/component-library/by-category/{category}`

**请求参数**:

| 参数名   | 类型   | 位置 | 必填 | 说明     |
| -------- | ------ | ---- | ---- | -------- |
| category | string | path | 是   | 组件分类 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "name": "\"string\"",
      "displayName": "\"string\"",
      "description": "\"string\"",
      "category": "\"string\"",
      "icon": "\"string\"",
      "thumbnail": "\"string\"",
      "defaultProps": "{}",
      "config": "{}",
      "version": "\"string\"",
      "isBuiltIn": "true"
    }
  ]
}
```

---

### 获取单个组件详情

**请求方式**: `GET`

**请求路径**: `/api/component-library/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 组件ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "type": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "schema": "{}",
    "defaultProps": "{}",
    "styleConfig": "{}",
    "previewImage": "\"string\"",
    "icon": "\"string\"",
    "isSystem": "true",
    "isActive": "true",
    "sortOrder": "0",
    "version": "\"string\"",
    "metadata": "{}",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "user": "{}",
    "usageCount": "0"
  }
}
```

---

### 更新组件信息

**请求方式**: `PATCH`

**请求路径**: `/api/component-library/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 组件ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "description": "\"string\"",
  "type": "\"string\"",
  "category": "\"string\"",
  "tags": "[\"string\"]",
  "schema": "{}",
  "defaultProps": "{}",
  "styleConfig": "{}",
  "previewImage": "\"string\"",
  "icon": "\"string\"",
  "isSystem": "true",
  "isActive": "true",
  "sortOrder": "0",
  "version": "\"string\"",
  "metadata": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "type": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "schema": "{}",
    "defaultProps": "{}",
    "styleConfig": "{}",
    "previewImage": "\"string\"",
    "icon": "\"string\"",
    "isSystem": "true",
    "isActive": "true",
    "sortOrder": "0",
    "version": "\"string\"",
    "metadata": "{}",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "user": "{}",
    "usageCount": "0"
  }
}
```

---

### 删除组件

**请求方式**: `DELETE`

**请求路径**: `/api/component-library/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 组件ID |

**响应示例**:

---

### 复制组件

**请求方式**: `POST`

**请求路径**: `/api/component-library/{id}/duplicate`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明       |
| ------ | ------ | ----- | ---- | ---------- |
| id     | number | path  | 是   | 源组件ID   |
| name   | string | query | 是   | 新组件名称 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "type": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "schema": "{}",
    "defaultProps": "{}",
    "styleConfig": "{}",
    "previewImage": "\"string\"",
    "icon": "\"string\"",
    "isSystem": "true",
    "isActive": "true",
    "sortOrder": "0",
    "version": "\"string\"",
    "metadata": "{}",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "user": "{}",
    "usageCount": "0"
  }
}
```

---

### 切换组件激活状态

**请求方式**: `POST`

**请求路径**: `/api/component-library/{id}/toggle-active`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 组件ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "description": "\"string\"",
    "type": "\"string\"",
    "category": "\"string\"",
    "tags": "[\"string\"]",
    "schema": "{}",
    "defaultProps": "{}",
    "styleConfig": "{}",
    "previewImage": "\"string\"",
    "icon": "\"string\"",
    "isSystem": "true",
    "isActive": "true",
    "sortOrder": "0",
    "version": "\"string\"",
    "metadata": "{}",
    "tenantId": "0",
    "userId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "user": "{}",
    "usageCount": "0"
  }
}
```

---

## 网站SEO管理

### 创建网站SEO配置

**请求方式**: `POST`

**请求路径**: `/api/website-seo`

**请求体**:

```json
{
  "websiteId": "0",
  "title": "\"string\"",
  "description": "\"string\"",
  "keywords": "\"string\"",
  "author": "\"string\"",
  "robots": "\"string\"",
  "canonical": "\"string\"",
  "ogTitle": "\"string\"",
  "ogDescription": "\"string\"",
  "ogImage": "\"string\"",
  "ogType": "\"string\"",
  "twitterCard": "\"string\"",
  "twitterSite": "\"string\"",
  "twitterCreator": "\"string\"",
  "structuredData": "{}",
  "customMeta": "{}",
  "analytics": "{}",
  "verification": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "title": "\"string\"",
    "description": "\"string\"",
    "keywords": "\"string\"",
    "author": "\"string\"",
    "robots": "\"string\"",
    "canonical": "\"string\"",
    "ogTitle": "\"string\"",
    "ogDescription": "\"string\"",
    "ogImage": "\"string\"",
    "ogType": "\"string\"",
    "twitterCard": "\"string\"",
    "twitterSite": "\"string\"",
    "twitterCreator": "\"string\"",
    "structuredData": "{}",
    "customMeta": "{}",
    "analytics": "{}",
    "verification": "{}",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 获取SEO配置列表

**请求方式**: `GET`

**请求路径**: `/api/website-seo`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明             |
| ---------------- | ------- | ----- | ---- | ---------------- |
| page             | number  | query | 否   | 页码             |
| limit            | number  | query | 否   | 每页数量         |
| keyword          | string  | query | 否   | 关键词搜索       |
| websiteId        | number  | query | 否   | 所属网站ID过滤   |
| sortBy           | string  | query | 否   | 排序字段         |
| sortOrder        | string  | query | 否   | 排序方向         |
| includeRelations | boolean | query | 否   | 是否包含关联数据 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"websiteId\": \"{}\",\n  \"title\": \"{}\",\n  \"description\": \"{}\",\n  \"keywords\": \"{}\",\n  \"author\": \"{}\",\n  \"robots\": \"{}\",\n  \"canonical\": \"{}\",\n  \"ogTitle\": \"{}\",\n  \"ogDescription\": \"{}\",\n  \"ogImage\": \"{}\",\n  \"ogType\": \"{}\",\n  \"twitterCard\": \"{}\",\n  \"twitterSite\": \"{}\",\n  \"twitterCreator\": \"{}\",\n  \"structuredData\": \"{}\",\n  \"customMeta\": \"{}\",\n  \"analytics\": \"{}\",\n  \"verification\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"createTime\": \"{}\",\n  \"updateTime\": \"{}\",\n  \"website\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取单个SEO配置详情

**请求方式**: `GET`

**请求路径**: `/api/website-seo/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明      |
| ------ | ------ | ---- | ---- | --------- |
| id     | number | path | 是   | SEO配置ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "title": "\"string\"",
    "description": "\"string\"",
    "keywords": "\"string\"",
    "author": "\"string\"",
    "robots": "\"string\"",
    "canonical": "\"string\"",
    "ogTitle": "\"string\"",
    "ogDescription": "\"string\"",
    "ogImage": "\"string\"",
    "ogType": "\"string\"",
    "twitterCard": "\"string\"",
    "twitterSite": "\"string\"",
    "twitterCreator": "\"string\"",
    "structuredData": "{}",
    "customMeta": "{}",
    "analytics": "{}",
    "verification": "{}",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 更新SEO配置

**请求方式**: `PATCH`

**请求路径**: `/api/website-seo/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明      |
| ------ | ------ | ---- | ---- | --------- |
| id     | number | path | 是   | SEO配置ID |

**请求体**:

```json
{
  "websiteId": "0",
  "title": "\"string\"",
  "description": "\"string\"",
  "keywords": "\"string\"",
  "author": "\"string\"",
  "robots": "\"string\"",
  "canonical": "\"string\"",
  "ogTitle": "\"string\"",
  "ogDescription": "\"string\"",
  "ogImage": "\"string\"",
  "ogType": "\"string\"",
  "twitterCard": "\"string\"",
  "twitterSite": "\"string\"",
  "twitterCreator": "\"string\"",
  "structuredData": "{}",
  "customMeta": "{}",
  "analytics": "{}",
  "verification": "{}"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "title": "\"string\"",
    "description": "\"string\"",
    "keywords": "\"string\"",
    "author": "\"string\"",
    "robots": "\"string\"",
    "canonical": "\"string\"",
    "ogTitle": "\"string\"",
    "ogDescription": "\"string\"",
    "ogImage": "\"string\"",
    "ogType": "\"string\"",
    "twitterCard": "\"string\"",
    "twitterSite": "\"string\"",
    "twitterCreator": "\"string\"",
    "structuredData": "{}",
    "customMeta": "{}",
    "analytics": "{}",
    "verification": "{}",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 删除SEO配置

**请求方式**: `DELETE`

**请求路径**: `/api/website-seo/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明      |
| ------ | ------ | ---- | ---- | --------- |
| id     | number | path | 是   | SEO配置ID |

**响应示例**:

---

### 根据网站ID获取SEO配置

**请求方式**: `GET`

**请求路径**: `/api/website-seo/website/{websiteId}`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "title": "\"string\"",
    "description": "\"string\"",
    "keywords": "\"string\"",
    "author": "\"string\"",
    "robots": "\"string\"",
    "canonical": "\"string\"",
    "ogTitle": "\"string\"",
    "ogDescription": "\"string\"",
    "ogImage": "\"string\"",
    "ogType": "\"string\"",
    "twitterCard": "\"string\"",
    "twitterSite": "\"string\"",
    "twitterCreator": "\"string\"",
    "structuredData": "{}",
    "customMeta": "{}",
    "analytics": "{}",
    "verification": "{}",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 生成网站meta标签

**请求方式**: `GET`

**请求路径**: `/api/website-seo/website/{websiteId}/meta-tags`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

---

### 获取结构化数据

**请求方式**: `GET`

**请求路径**: `/api/website-seo/website/{websiteId}/structured-data`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

---

### 获取分析工具配置

**请求方式**: `GET`

**请求路径**: `/api/website-seo/website/{websiteId}/analytics`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

---

### 获取网站验证码

**请求方式**: `GET`

**请求路径**: `/api/website-seo/website/{websiteId}/verification`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

---

## 站点地图管理

### 生成站点地图

**请求方式**: `POST`

**请求路径**: `/api/sitemap/generate`

**请求体**:

```json
{
  "websiteId": "0",
  "type": "\"string\"",
  "includeImages": "true",
  "includeVideos": "true",
  "includeNews": "true",
  "maxUrls": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "type": "\"string\"",
    "url": "\"string\"",
    "content": "\"string\"",
    "lastGenerated": "\"string\"",
    "isActive": "true",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 获取网站的所有站点地图

**请求方式**: `GET`

**请求路径**: `/api/sitemap/website/{websiteId}`

**请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明   |
| --------- | ------ | ---- | ---- | ------ |
| websiteId | number | path | 是   | 网站ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": [
    {
      "id": "0",
      "websiteId": "0",
      "type": "\"string\"",
      "url": "\"string\"",
      "content": "\"string\"",
      "lastGenerated": "\"string\"",
      "isActive": "true",
      "tenantId": "0",
      "createTime": "\"string\"",
      "updateTime": "\"string\"",
      "website": "{}"
    }
  ]
}
```

---

### 获取单个站点地图详情

**请求方式**: `GET`

**请求路径**: `/api/sitemap/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 站点地图ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "type": "\"string\"",
    "url": "\"string\"",
    "content": "\"string\"",
    "lastGenerated": "\"string\"",
    "isActive": "true",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

### 删除站点地图

**请求方式**: `DELETE`

**请求路径**: `/api/sitemap/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 站点地图ID |

**响应示例**:

---

### 切换站点地图激活状态

**请求方式**: `PATCH`

**请求路径**: `/api/sitemap/{id}/toggle`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明       |
| ------ | ------ | ---- | ---- | ---------- |
| id     | number | path | 是   | 站点地图ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "websiteId": "0",
    "type": "\"string\"",
    "url": "\"string\"",
    "content": "\"string\"",
    "lastGenerated": "\"string\"",
    "isActive": "true",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}"
  }
}
```

---

## 网站表单管理

### 创建网站表单

**请求方式**: `POST`

**请求路径**: `/api/website-forms`

**请求体**:

```json
{
  "name": "\"string\"",
  "title": "\"string\"",
  "description": "\"string\"",
  "fields": "{}",
  "settings": "{}",
  "isActive": "true",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "title": "\"string\"",
    "description": "\"string\"",
    "fields": "{}",
    "settings": "{}",
    "isActive": "true",
    "websiteId": "0",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "submissionCount": "0"
  }
}
```

---

### 获取网站表单列表

**请求方式**: `GET`

**请求路径**: `/api/website-forms`

**请求参数**:

| 参数名           | 类型    | 位置  | 必填 | 说明             |
| ---------------- | ------- | ----- | ---- | ---------------- |
| page             | number  | query | 否   | 页码             |
| limit            | number  | query | 否   | 每页数量         |
| keyword          | string  | query | 否   | 关键词搜索       |
| websiteId        | number  | query | 否   | 所属网站ID过滤   |
| isActive         | boolean | query | 否   | 是否激活状态过滤 |
| sortBy           | string  | query | 否   | 排序字段         |
| sortOrder        | string  | query | 否   | 排序方向         |
| includeRelations | boolean | query | 否   | 是否包含关联数据 |
| includeStats     | boolean | query | 否   | 是否包含提交统计 |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "data": "[{\n  \"id\": \"{}\",\n  \"name\": \"{}\",\n  \"title\": \"{}\",\n  \"description\": \"{}\",\n  \"fields\": \"{}\",\n  \"settings\": \"{}\",\n  \"isActive\": \"{}\",\n  \"websiteId\": \"{}\",\n  \"tenantId\": \"{}\",\n  \"createTime\": \"{}\",\n  \"updateTime\": \"{}\",\n  \"website\": \"{}\",\n  \"submissionCount\": \"{}\"\n}]",
    "total": "0",
    "page": "0",
    "limit": "0",
    "totalPages": "0"
  }
}
```

---

### 获取表单统计信息

**请求方式**: `GET`

**请求路径**: `/api/website-forms/stats`

**请求参数**:

| 参数名 | 类型   | 位置  | 必填 | 说明   |
| ------ | ------ | ----- | ---- | ------ |
| formId | number | query | 否   | 表单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "totalSubmissions": "0",
    "recentSubmissions": "0",
    "statusDistribution": "{}"
  }
}
```

---

### 获取单个网站表单详情

**请求方式**: `GET`

**请求路径**: `/api/website-forms/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 表单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "title": "\"string\"",
    "description": "\"string\"",
    "fields": "{}",
    "settings": "{}",
    "isActive": "true",
    "websiteId": "0",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "submissionCount": "0"
  }
}
```

---

### 更新网站表单

**请求方式**: `PATCH`

**请求路径**: `/api/website-forms/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 表单ID |

**请求体**:

```json
{
  "name": "\"string\"",
  "title": "\"string\"",
  "description": "\"string\"",
  "fields": "{}",
  "settings": "{}",
  "isActive": "true",
  "websiteId": "0"
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "title": "\"string\"",
    "description": "\"string\"",
    "fields": "{}",
    "settings": "{}",
    "isActive": "true",
    "websiteId": "0",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "submissionCount": "0"
  }
}
```

---

### 删除网站表单

**请求方式**: `DELETE`

**请求路径**: `/api/website-forms/{id}`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 表单ID |

**响应示例**:

---

### 切换表单激活状态

**请求方式**: `POST`

**请求路径**: `/api/website-forms/{id}/toggle-active`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 表单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "title": "\"string\"",
    "description": "\"string\"",
    "fields": "{}",
    "settings": "{}",
    "isActive": "true",
    "websiteId": "0",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "submissionCount": "0"
  }
}
```

---

### 复制表单

**请求方式**: `POST`

**请求路径**: `/api/website-forms/{id}/duplicate`

**请求参数**:

| 参数名 | 类型   | 位置 | 必填 | 说明   |
| ------ | ------ | ---- | ---- | ------ |
| id     | number | path | 是   | 表单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "0",
    "name": "\"string\"",
    "title": "\"string\"",
    "description": "\"string\"",
    "fields": "{}",
    "settings": "{}",
    "isActive": "true",
    "websiteId": "0",
    "tenantId": "0",
    "createTime": "\"string\"",
    "updateTime": "\"string\"",
    "website": "{}",
    "submissionCount": "0"
  }
}
```

---
