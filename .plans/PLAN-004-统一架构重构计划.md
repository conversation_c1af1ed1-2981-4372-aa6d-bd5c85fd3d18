# FlexiHub 统一架构重构计划 🚀

## 项目概述

将FlexiHub从分离的editor+web架构重构为**统一的Next.js应用架构**，实现编辑器和用户网站渲染的完全统一，简化开发和部署流程。

## 🎯 核心目标

- [x] **🔥 统一应用架构，简化部署**
- [x] **🌐 支持自定义域名访问**
- [x] **👁️ 实现预览功能**
- [x] **📡 完整的API文档和封装**
- [ ] 确保编辑和渲染的完全一致性
- [ ] 支持多租户和权限管理
- [ ] 实现全局主题颜色切换和自定义功能
- [ ] 支持PC、平板、移动端响应式编辑预览
- [ ] 🌐 实现完整的国际化(i18n)支持

## 🏗️ 新统一架构设计

### 目录结构
```
apps/
├── admin/              # Vue3 管理后台（保持不变）
└── website/            # 🆕 统一的 Next.js 应用
    ├── src/
    │   ├── app/
    │   │   ├── [locale]/
    │   │   │   ├── editor/[siteId]/     # 编辑器路由
    │   │   │   │   └── preview/         # 🆕 预览路由
    │   │   │   ├── site/[domain]/       # 内部网站路由
    │   │   │   └── [domain]/            # 🆕 自定义域名路由
    │   │   ├── api/                     # API路由
    │   │   └── api-docs/                # 🆕 API文档
    │   ├── components/
    │   │   ├── editor/                  # 编辑器专用组件
    │   │   ├── layouts/                 # 布局组件
    │   │   └── ui/                      # UI组件
    │   ├── hooks/                       # 共享钩子
    │   ├── lib/
    │   │   ├── auth.ts                  # 认证工具
    │   │   ├── api-client.ts            # 🆕 API客户端封装
    │   │   └── domain.ts                # 🆕 域名检测工具
    │   └── middleware.ts                # 权限中间件
    └── package.json

packages/                               # 共享包（保持不变）
├── @flexihub/shared/
├── @flexihub/website-components/
└── @flexihub/ui/
```

### 🎯 架构优势

1. **🚀 简化部署**：单一Next.js应用，一次部署
2. **🔄 代码共享**：编辑器和渲染器共用组件
3. **⚡ 开发效率**：统一开发环境和工具链
4. **🌐 SEO友好**：原生Next.js SSR/SSG支持
5. **💰 成本降低**：减少服务器资源和维护成本
6. **🌍 自定义域名**：支持 example.com 直接访问用户网站
7. **👁️ 实时预览**：编辑器集成预览功能
8. **📡 API完整性**：全面的API文档和类型安全封装

## 📅 开发时间线（16周）

### 🏗️ 第一阶段：统一架构搭建（第1-4周）

#### **第1周：架构重构** ✅ 已完成
- [x] 合并 `apps/editor` 和 `apps/web` 为 `apps/website`
- [x] 配置统一的Next.js路由系统
- [x] 实现模式检测逻辑（编辑器模式 vs 渲染模式）
- [x] 建立条件布局系统

#### **第2周：路由和权限系统** ✅ 已完成
- [x] 实现编辑器路由：`/[locale]/editor/[siteId]`
- [x] 实现用户网站路由：`/[locale]/[domain]`
- [x] 搭建权限中间件
- [x] 集成JWT身份验证

#### **第2.5周：核心功能增强** ✅ 已完成
- [x] **🆕 添加预览路由**：`/[locale]/editor/[siteId]/preview`
- [x] **🆕 自定义域名支持**：example.com 直接访问
- [x] **🆕 API文档系统**：完整的接口文档
- [x] **🆕 API客户端封装**：类型安全的接口调用
- [x] **🆕 域名检测系统**：智能域名识别和映射

#### **第3周：基础UI框架** ✅ 已完成
- [x] 统一Tailwind CSS配置
- [x] 建立设计系统基础  
- [x] 实现响应式布局框架
- [x] 创建基础UI组件库

#### **第3.5周：UI组件库扩展** ✅ 已完成
- [x] **🆕 Button组件**：多变体、大小和状态支持
- [x] **🆕 Input组件**：支持标签、错误提示和图标
- [x] **🆕 Card组件**：头部、内容、底部区域
- [x] **🆕 布局组件**：Container、Section、Grid、Flex
- [x] **🆕 工具函数库**：className合并、防抖、节流等
- [x] **🆕 UI演示页面**：完整的组件展示和测试

#### **第4周：国际化系统** ✅ 已完成
- [x] 集成next-intl
- [x] 实现多语言路由
- [x] 创建语言切换组件
- [x] 建立翻译资源管理

### 🎨 第二阶段：编辑器核心功能（第5-8周）

#### **第5周：Craft.js集成** ✅ 已完成
- [x] 安装和配置Craft.js
- [x] 实现基础拖拽功能
- [x] 创建组件面板
- [x] 实现撤销/重做功能

#### **第5.5周：基础组件和工具** ✅ 已完成
- [x] **🆕 Text组件**：可编辑文本组件，支持字体、颜色、对齐
- [x] **🆕 Container组件**：容器组件，支持背景、边距、圆角
- [x] **🆕 Toolbar工具栏**：撤销/重做、预览、保存功能
- [x] **🆕 ComponentPanel组件面板**：拖拽式组件库
- [x] **🆕 PropertyPanel属性面板**：实时属性编辑
- [x] **🆕 编辑器主界面**：完整的编辑器布局集成

#### **第6周：编辑器界面** ✅ 已完成
- [x] 设计编辑器布局
- [x] 实现工具栏组件
- [x] 创建属性面板
- [x] 实现图层管理
- [x] **🆕 LayersPanel图层面板**：树形结构展示，支持选择、删除、复制
- [x] **🆕 SettingsPanel设置面板**：全局设置、主题配置、导入导出

#### **第7周：组件系统** ⏳ 进行中
- [ ] 扩展website-components库
- [ ] 实现Text、Container等基础组件
- [ ] 添加图片、按钮、表单组件
- [ ] 实现组件配置面板

#### **第8周：主题系统**
- [ ] 实现全局主题管理
- [ ] 创建颜色选择器
- [ ] 实现实时主题切换
- [ ] 支持自定义CSS变量

### 🌐 第三阶段：渲染引擎和高级功能（第9-12周）

#### **第9周：渲染引擎优化**
- [ ] 完善PageRenderer组件
- [ ] 实现SSR/SSG支持
- [ ] 优化SEO元数据
- [ ] 实现动态组件加载

#### **第10周：响应式系统**
- [ ] 实现多设备预览
- [ ] 添加断点管理
- [ ] 创建响应式编辑工具
- [ ] 优化移动端体验

#### **第11周：数据管理**
- [ ] 实现页面保存/加载
- [ ] 添加版本管理
- [ ] 实现模板系统
- [ ] 创建内容管理接口

#### **第12周：高级组件**
- [ ] 实现导航菜单组件
- [ ] 添加幻灯片组件
- [ ] 创建表格组件
- [ ] 实现表单构建器

### ⚡ 第四阶段：集成和优化（第13-16周）

#### **第13周：管理后台集成**
- [ ] 更新Vue3管理后台
- [ ] 实现"编辑网站"跳转
- [ ] 同步权限系统
- [ ] 添加网站管理功能

#### **第14周：性能优化**
- [ ] 代码分割优化
- [ ] 图片懒加载
- [ ] 缓存策略优化
- [ ] 构建性能优化

#### **第15周：测试和调试**
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] 端到端测试
- [ ] 性能测试

#### **第16周：部署和文档**
- [ ] 生产环境配置
- [ ] Docker配置优化
- [ ] 用户文档编写
- [ ] 开发文档完善

## 🔧 核心技术实现

### 统一Next.js配置
```typescript
// apps/website/next.config.ts
const nextConfig: NextConfig = {
  async rewrites() {
    return [
      // 编辑器模式
      {
        source: '/:locale/editor/:siteId*',
        destination: '/:locale/editor/:siteId*'
      },
      // 预览模式 🆕
      {
        source: '/:locale/editor/:siteId/preview',
        destination: '/:locale/editor/:siteId/preview'
      },
      // 自定义域名支持 🆕
      {
        source: '/:locale/:domain*',
        destination: '/:locale/site/:domain*'
      }
    ];
  },
  
  env: {
    APP_MODE: process.env.APP_MODE || 'unified',
  }
};
```

### 预览功能实现 🆕
```typescript
// 预览页面路由：/[locale]/editor/[siteId]/preview
// 编辑器预览按钮集成
<Link
  href={`/${locale}/editor/${siteId}/preview`}
  target="_blank"
  className="preview-button"
>
  预览
</Link>
```

### 自定义域名检测 🆕
```typescript
// src/lib/domain.ts
export function isCustomDomain(request: NextRequest): boolean {
  // 检测是否为自定义域名
  // 生产环境：example.com → demo-site
}

export function getCustomDomainSite(request: NextRequest): string {
  // 域名映射：example.com → demo-site
}
```

### API客户端封装 🆕
```typescript
// src/lib/api-client.ts
export class ApiClient {
  static async getSite(domain: string, options?: {
    locale?: string;
    preview?: boolean;
  }): Promise<ApiResponse<Site>>;
  
  static async updateSite(domain: string, data: Partial<Site>);
  static async publishSite(domain: string);
}
```

## 🚀 当前执行状态

**✅ 第1周任务已完成**  
**✅ 第2周任务已完成**  
**✅ 第2.5周核心功能增强已完成**  
**✅ 第3周基础UI框架已完成**
**✅ 第4周国际化系统已完成**
**✅ 第5周Craft.js集成已完成**
**✅ 第5.5周基础组件和工具已完成**
**✅ 第6周编辑器界面已完成**
**⏳ 准备开始第7周：组件系统**

### **已实现功能**：

#### 🏗️ 架构层面
- ✅ 统一Next.js应用架构
- ✅ 智能路由系统（编辑器/用户网站/预览）
- ✅ 条件布局系统
- ✅ JWT权限中间件
- ✅ **自定义域名支持** 🆕

#### 🔐 权限系统
- ✅ JWT令牌验证
- ✅ 编辑器访问保护
- ✅ 预览功能保护
- ✅ 登录页面和认证流程
- ✅ 权限检查和角色管理

#### 📡 API系统
- ✅ 登录API（/api/auth/login）
- ✅ 网站数据API（/api/sites/[domain]）
- ✅ 模拟用户和网站数据
- ✅ **完整API文档系统** 🆕
- ✅ **类型安全的API客户端** 🆕

#### 🎨 UI组件
- ✅ EditorLayout（编辑器布局，增强功能）
- ✅ SiteLayout（用户网站布局）
- ✅ **PreviewPage（预览页面）** 🆕
- ✅ PageRenderer（页面渲染器）
- ✅ **API文档页面** 🆕
- ✅ 错误页面和404页面
- ✅ **完整的UI组件库**：Button、Input、Card、Container等 🆕

#### 🔧 编辑器组件
- ✅ **Craft.js编辑器核心**：拖拽、撤销/重做、组件系统 🆕
- ✅ **Text组件**：可编辑文本，支持字体、颜色、对齐 🆕
- ✅ **Container组件**：容器布局，支持背景、间距、圆角 🆕
- ✅ **Button组件**：可配置按钮，多种样式变体 🆕
- ✅ **Toolbar工具栏**：撤销/重做、预览、保存、设置 🆕
- ✅ **ComponentPanel组件面板**：拖拽式组件库界面 🆕
- ✅ **PropertyPanel属性面板**：实时组件属性编辑 🆕
- ✅ **LayersPanel图层面板**：树形结构，选择、删除、复制 🆕
- ✅ **SettingsPanel设置面板**：全局设置、主题、导入导出 🆕

#### 🌐 路由系统
- ✅ 编辑器路由：`/[locale]/editor/[siteId]`
- ✅ **预览路由：`/[locale]/editor/[siteId]/preview`** 🆕
- ✅ 用户网站路由：`/[locale]/site/[domain]`
- ✅ **自定义域名路由：`example.com`** 🆕
- ✅ **API文档路由：`/api-docs`** 🆕

### **功能验证**：
- ✅ 应用在端口3000正常运行
- ✅ 编辑器访问自动重定向到登录页面
- ✅ **预览功能需要权限验证** 🆕
- ✅ 用户网站可以正常访问
- ✅ **API文档页面正常显示** 🆕
- ✅ 权限中间件工作正常

### **新解决的问题**：
1. ✅ **预览路由**：完整的预览系统，支持权限保护
2. ✅ **自定义域名**：支持 example.com 直接访问用户网站
3. ✅ **API文档**：完整的API接口文档和类型安全封装

### **下周目标**：
🎯 完善基础UI框架，建立设计系统和组件库基础

---

*更新时间：2024年12月 - 增加预览、自定义域名和API文档功能*
