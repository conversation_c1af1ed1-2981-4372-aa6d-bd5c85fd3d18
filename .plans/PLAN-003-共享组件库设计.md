# 共享组件库设计方案

## 组件库架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "@flexihub/ui (基础UI组件)"
        UI1[Button]
        UI2[Input]
        UI3[Dialog]
        UI4[Card]
        UI5[Dropdown]
        UI6[Checkbox]
        UI7[Select]
        UI8[Tooltip]
    end

    subgraph "@flexihub/website-components (网站组件)"
        subgraph "Layout 布局组件"
            L1[Container]
            L2[Grid]
            L3[Flex]
            L4[Header]
            L5[Footer]
        end

        subgraph "Content 内容组件"
            C1[Text]
            C2[Image]
            C3[Video]
            C4[RichText]
        end

        subgraph "Interactive 交互组件"
            I1[Button]
            I2[Form]
            I3[Modal]
            I4[ContactForm]
        end

        subgraph "Complex 复杂组件"
            X1[Gallery]
            X2[Carousel]
            X3[Tabs]
            X4[Accordion]
        end
    end

    subgraph "@flexihub/shared (共享逻辑)"
        S1[Types]
        S2[Utils]
        S3[API Client]
        S4[Validation]
    end

    L1 --> UI4
    I1 --> UI1
    I2 --> UI2
    I3 --> UI3

    style UI1 fill:#e8f5e8
    style L1 fill:#f3e5f5
    style S1 fill:#fff3e0
```

## @flexihub/ui 基础组件库

### 设计原则

1. **Headless UI 理念**: 分离逻辑与样式
2. **Shadcn/UI 架构**: 可复制、可定制
3. **Radix UI 基础**: 可访问性优先
4. **TypeScript 优先**: 完整类型支持

### 核心组件规范

#### Button 组件

```typescript
// packages/ui/src/components/ui/button.tsx
import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants };
```

### 组件导出策略

```typescript
// packages/ui/src/index.ts
export * from './components/ui/button';
export * from './components/ui/input';
export * from './components/ui/dialog';
export * from './components/ui/card';
export * from './components/ui/dropdown-menu';
export * from './lib/utils';
```

## @flexihub/website-components 网站组件库

### 组件分类

#### Layout 布局组件

```typescript
// Container 容器组件
interface ContainerProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  background?: string;
  className?: string;
  children: React.ReactNode;
}

const Container: React.FC<ContainerProps> = ({
  maxWidth = 'lg',
  padding = 'md',
  background,
  className,
  children,
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6',
  };

  return (
    <div
      className={cn(
        'mx-auto',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
      style={{ backgroundColor: background }}
    >
      {children}
    </div>
  );
};

// Craft.js 配置
Container.craft = {
  props: {
    maxWidth: 'lg',
    padding: 'md',
    background: 'transparent',
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
  related: {
    settings: ContainerSettings,
  },
};
```

#### Content 内容组件

```typescript
// Text 文本组件
interface TextProps {
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  content?: string;
  fontSize?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  fontWeight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: string;
  align?: 'left' | 'center' | 'right' | 'justify';
  className?: string;
}

const Text: React.FC<TextProps> = ({
  tag = 'p',
  content = '请输入文本内容',
  fontSize = 'base',
  fontWeight = 'normal',
  color = '#000000',
  align = 'left',
  className,
}) => {
  const Component = tag;

  const fontSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  };

  const fontWeightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };

  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  return (
    <Component
      className={cn(
        fontSizeClasses[fontSize],
        fontWeightClasses[fontWeight],
        alignClasses[align],
        className
      )}
      style={{ color }}
    >
      {content}
    </Component>
  );
};

Text.craft = {
  props: {
    tag: 'p',
    content: '请输入文本内容',
    fontSize: 'base',
    fontWeight: 'normal',
    color: '#000000',
    align: 'left',
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
  },
  related: {
    settings: TextSettings,
  },
};
```

### 组件Schema系统

```typescript
// packages/website-components/src/schemas/component.ts
export interface ComponentSchema {
  type: string;
  name: string;
  category: 'layout' | 'content' | 'interactive' | 'complex';
  description: string;
  props: Record<string, PropSchema>;
  defaultProps: Record<string, any>;
  preview?: {
    thumbnail: string;
    description: string;
  };
}

export interface PropSchema {
  type:
    | 'string'
    | 'number'
    | 'boolean'
    | 'select'
    | 'color'
    | 'image'
    | 'array';
  label: string;
  description?: string;
  default: any;
  options?: Array<{ value: any; label: string }>;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// 组件注册表
export const componentRegistry: Record<string, ComponentSchema> = {
  Container: {
    type: 'Container',
    name: '容器',
    category: 'layout',
    description: '用于包装其他组件的容器',
    props: {
      maxWidth: {
        type: 'select',
        label: '最大宽度',
        default: 'lg',
        options: [
          { value: 'sm', label: '小' },
          { value: 'md', label: '中' },
          { value: 'lg', label: '大' },
          { value: 'xl', label: '超大' },
          { value: 'full', label: '全宽' },
        ],
      },
      padding: {
        type: 'select',
        label: '内边距',
        default: 'md',
        options: [
          { value: 'none', label: '无' },
          { value: 'sm', label: '小' },
          { value: 'md', label: '中' },
          { value: 'lg', label: '大' },
        ],
      },
      background: {
        type: 'color',
        label: '背景颜色',
        default: 'transparent',
      },
    },
    defaultProps: {
      maxWidth: 'lg',
      padding: 'md',
      background: 'transparent',
    },
  },
  // 更多组件定义...
};
```

## @flexihub/shared 共享逻辑包

### 类型定义

```typescript
// packages/shared/src/types/website.ts
export interface Website {
  id: string;
  tenantId: string;
  name: string;
  domain?: string;
  settings: WebsiteSettings;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface WebsiteSettings {
  theme: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
  seo: {
    title: string;
    description: string;
    keywords: string[];
    favicon?: string;
  };
  analytics?: {
    googleAnalyticsId?: string;
    facebookPixelId?: string;
  };
}

// packages/shared/src/types/page.ts
export interface Page {
  id: string;
  websiteId: string;
  slug: string;
  title: string;
  content: any; // Craft.js序列化数据
  metadata: PageMetadata;
  status: 'draft' | 'published' | 'archived';
  publishedAt?: string;
  version: number;
  createdAt: string;
  updatedAt: string;
}

export interface PageMetadata {
  title: string;
  description: string;
  keywords: string[];
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
  };
  robots?: {
    index: boolean;
    follow: boolean;
  };
}
```

### 工具函数

```typescript
// packages/shared/src/utils/validation.ts
import { z } from 'zod';

export const websiteSchema = z.object({
  name: z.string().min(1, '网站名称不能为空').max(100, '网站名称过长'),
  domain: z
    .string()
    .optional()
    .refine((domain) => {
      if (!domain) return true;
      const domainRegex =
        /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
      return domainRegex.test(domain);
    }, '域名格式不正确'),
  settings: z.object({
    theme: z.object({
      primaryColor: z.string(),
      secondaryColor: z.string(),
      fontFamily: z.string(),
    }),
    seo: z.object({
      title: z.string().min(1, 'SEO标题不能为空'),
      description: z.string().min(1, 'SEO描述不能为空'),
      keywords: z.array(z.string()),
    }),
  }),
});

export const pageSchema = z.object({
  title: z.string().min(1, '页面标题不能为空').max(200, '页面标题过长'),
  slug: z
    .string()
    .min(1, 'URL路径不能为空')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'URL路径只能包含小写字母、数字和连字符',
    ),
  metadata: z.object({
    title: z.string().min(1, 'Meta标题不能为空'),
    description: z.string().min(1, 'Meta描述不能为空'),
    keywords: z.array(z.string()),
  }),
});

// 验证工具函数
export const validateWebsite = (data: unknown) => {
  return websiteSchema.safeParse(data);
};

export const validatePage = (data: unknown) => {
  return pageSchema.safeParse(data);
};
```

### API客户端

```typescript
// packages/shared/src/api/client.ts
export class APIClient {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor(baseURL: string, token?: string) {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new APIError(
        response.status,
        `HTTP ${response.status}: ${response.statusText}`,
      );
    }

    return response.json();
  }

  // 网站相关方法
  async getWebsites(
    params?: GetWebsitesParams,
  ): Promise<APIResponse<Website[]>> {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return this.request<Website[]>(`/api/websites${query}`);
  }

  async getWebsite(id: string): Promise<APIResponse<Website>> {
    return this.request<Website>(`/api/websites/${id}`);
  }

  async createWebsite(
    data: CreateWebsiteRequest,
  ): Promise<APIResponse<Website>> {
    return this.request<Website>('/api/websites', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 页面相关方法
  async getPage(id: string): Promise<APIResponse<Page>> {
    return this.request<Page>(`/api/pages/${id}`);
  }

  async updatePage(
    id: string,
    data: UpdatePageRequest,
  ): Promise<APIResponse<Page>> {
    return this.request<Page>(`/api/pages/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async publishPage(id: string): Promise<APIResponse<{ publishedAt: string }>> {
    return this.request<{ publishedAt: string }>(`/api/pages/${id}/publish`, {
      method: 'POST',
    });
  }
}

export class APIError extends Error {
  constructor(
    public status: number,
    message: string,
    public details?: any,
  ) {
    super(message);
    this.name = 'APIError';
  }
}
```

## 组件开发规范

### 1. 命名规范

- 组件名使用PascalCase: `Container`, `TextBlock`
- 文件名使用kebab-case: `container.tsx`, `text-block.tsx`
- 属性名使用camelCase: `maxWidth`, `fontSize`

### 2. 组件结构

```typescript
// 标准组件结构
export interface ComponentProps {
  // 属性定义
}

const Component: React.FC<ComponentProps> = (props) => {
  // 组件实现
  return <div>{/* JSX */}</div>;
};

// Craft.js配置
Component.craft = {
  props: {
    // 默认属性
  },
  rules: {
    // 拖拽规则
  },
  related: {
    settings: ComponentSettings,
  },
};

export { Component };
```

### 3. 样式规范

- 使用Tailwind CSS进行样式设计
- 支持响应式设计
- 提供主题变量支持
- 确保可访问性（ARIA属性）

### 4. 测试规范

```typescript
// 组件测试示例
import { render, screen } from '@testing-library/react';
import { Container } from './container';

describe('Container', () => {
  it('应该正确渲染内容', () => {
    render(<Container>Test Content</Container>);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('应该应用正确的最大宽度类', () => {
    const { container } = render(
      <Container maxWidth="xl">Content</Container>
    );
    expect(container.firstChild).toHaveClass('max-w-6xl');
  });
});
```

这个共享组件库设计方案提供了：

1. **清晰的架构分层**
2. **标准化的组件规范**
3. **完整的类型定义**
4. **统一的开发规范**
5. **可扩展的设计系统**

确保了组件库的可维护性、可扩展性和一致性。
