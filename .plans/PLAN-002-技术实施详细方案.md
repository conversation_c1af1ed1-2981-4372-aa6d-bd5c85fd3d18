# 技术实施详细方案

## 用户编辑流程设计

### 完整用户编辑流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as Vue3管理后台
    participant Auth as 认证服务
    participant E as Next.js编辑器
    participant API as API服务
    participant DB as 数据库
    participant W as Next.js用户端

    U->>A: 登录管理后台
    A->>Auth: 验证用户凭据
    Auth->>A: 返回JWT Token

    U->>A: 点击编辑网站
    A->>A: 检查编辑权限<br/>(租户管理员/编辑权限)

    alt 有编辑权限
        A->>E: 跳转编辑器<br/>(URL + JWT Token + 网站ID)
        E->>Auth: 验证Token和权限
        Auth->>E: 返回用户信息和权限
        E->>API: 获取网站页面数据
        API->>DB: 查询页面结构和内容
        DB->>API: 返回页面JSON数据
        API->>E: 返回可编辑的页面数据
        E->>E: 加载Craft.js编辑器
        E->>E: 渲染现有页面结构

        loop 编辑过程
            U->>E: 拖拽组件/编辑属性
            E->>E: 实时更新预览
            E->>E: 更新内部状态
        end

        U->>E: 保存页面
        E->>E: 生成页面JSON数据
        E->>API: 提交页面数据
        API->>DB: 保存页面结构
        DB->>API: 确认保存成功
        API->>E: 返回保存结果

        U->>E: 预览/发布页面
        E->>API: 更新页面状态
        API->>DB: 更新发布状态

    else 无编辑权限
        A->>A: 显示权限不足提示
    end

    Note over U,W: 用户端访问流程
    U->>W: 访问网站页面
    W->>API: 获取页面数据
    API->>DB: 查询已发布页面
    DB->>API: 返回页面数据
    API->>W: 返回页面结构
    W->>W: SSR渲染页面
    W->>U: 返回渲染后的HTML
```

## 权限验证系统设计

### 权限模型

```typescript
// 用户权限类型定义
interface UserPermissions {
  userId: string;
  tenantId: string;
  roles: Role[];
  permissions: Permission[];
}

interface Role {
  id: string;
  name: string;
  level: 'tenant_admin' | 'editor' | 'viewer';
  permissions: Permission[];
}

interface Permission {
  id: string;
  resource: 'website' | 'page' | 'component' | 'settings';
  action: 'create' | 'read' | 'update' | 'delete' | 'publish';
  scope: 'all' | 'own' | 'assigned';
}

// 权限检查示例
const checkEditPermission = (user: User, websiteId: string): boolean => {
  // 1. 检查是否是租户管理员
  if (user.roles.some((role) => role.level === 'tenant_admin')) {
    return true;
  }

  // 2. 检查是否有特定网站的编辑权限
  const hasEditPermission = user.permissions.some(
    (permission) =>
      permission.resource === 'website' &&
      permission.action === 'update' &&
      (permission.scope === 'all' || isAssignedWebsite(user.id, websiteId)),
  );

  return hasEditPermission;
};
```

### JWT Token设计

```typescript
// JWT Payload结构
interface JWTPayload {
  sub: string; // 用户ID
  tenantId: string; // 租户ID
  roles: string[]; // 角色列表
  permissions: string[]; // 权限列表
  exp: number; // 过期时间
  iat: number; // 签发时间
}

// 编辑器跳转URL生成
const generateEditorURL = (
  websiteId: string,
  pageId: string,
  token: string,
): string => {
  const baseURL = process.env.NEXT_PUBLIC_EDITOR_URL;
  const params = new URLSearchParams({
    token,
    websiteId,
    pageId,
    timestamp: Date.now().toString(),
  });

  return `${baseURL}/editor/${websiteId}/${pageId}?${params.toString()}`;
};
```

## API接口设计

### RESTful API规范

```typescript
// 基础API响应格式
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
    };
  };
}

// 网站相关API
interface WebsiteAPI {
  // 获取网站列表
  'GET /api/websites': {
    query?: {
      page?: number;
      limit?: number;
      search?: string;
    };
    response: APIResponse<Website[]>;
  };

  // 获取网站详情
  'GET /api/websites/:id': {
    params: { id: string };
    response: APIResponse<Website>;
  };

  // 创建网站
  'POST /api/websites': {
    body: CreateWebsiteRequest;
    response: APIResponse<Website>;
  };

  // 更新网站
  'PUT /api/websites/:id': {
    params: { id: string };
    body: UpdateWebsiteRequest;
    response: APIResponse<Website>;
  };
}

// 页面相关API
interface PageAPI {
  // 获取页面数据 (编辑器使用)
  'GET /api/pages/:id': {
    params: { id: string };
    query?: { version?: string };
    response: APIResponse<PageData>;
  };

  // 保存页面数据
  'PUT /api/pages/:id': {
    params: { id: string };
    body: {
      content: any; // Craft.js序列化后的JSON
      metadata: {
        title: string;
        description: string;
        keywords: string[];
      };
    };
    response: APIResponse<PageData>;
  };

  // 发布页面
  'POST /api/pages/:id/publish': {
    params: { id: string };
    response: APIResponse<{ publishedAt: string }>;
  };
}

// 组件相关API
interface ComponentAPI {
  // 获取可用组件列表
  'GET /api/components': {
    query?: {
      category?: string;
      search?: string;
    };
    response: APIResponse<ComponentDefinition[]>;
  };

  // 获取组件配置Schema
  'GET /api/components/:type/schema': {
    params: { type: string };
    response: APIResponse<ComponentSchema>;
  };
}
```

### 数据库设计

```sql
-- 网站表
CREATE TABLE websites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  name VARCHAR(255) NOT NULL,
  domain VARCHAR(255) UNIQUE,
  settings JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL
);

-- 页面表
CREATE TABLE pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID NOT NULL REFERENCES websites(id) ON DELETE CASCADE,
  slug VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  content JSONB NOT NULL DEFAULT '{}', -- Craft.js序列化数据
  metadata JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'draft',
  published_at TIMESTAMP WITH TIME ZONE,
  version INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(website_id, slug)
);

-- 页面版本表 (用于版本控制)
CREATE TABLE page_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id UUID NOT NULL REFERENCES pages(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  content JSONB NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL,
  UNIQUE(page_id, version)
);

-- 组件定义表
CREATE TABLE component_definitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(100) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  schema JSONB NOT NULL,
  preview_props JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Craft.js集成方案

### 编辑器核心配置

```typescript
// Craft.js编辑器配置
import { Editor } from '@craftjs/core';
import { Container, Text, Button, Image } from '@flexihub/website-components';

const EditorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Editor
      resolver={{
        Container,
        Text,
        Button,
        Image,
        // 更多组件...
      }}
      enabled={true}
      indicator={{
        success: '#22c55e',
        error: '#ef4444',
      }}
      handlers={(store) => ({
        // 自定义事件处理
        onRender: () => {
          // 渲染完成后的回调
        },
        onSelectionChange: (nodeId) => {
          // 选择变化时的回调
        },
      })}
    >
      {children}
    </Editor>
  );
};
```

### 组件包装器设计

```typescript
// 统一的组件包装器
import { useNode } from '@craftjs/core';
import { ComponentSchema } from '@flexihub/shared';

interface CraftWrapperProps {
  children: React.ReactNode;
  schema: ComponentSchema;
}

export const withCraftWrapper = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  schema: ComponentSchema
) => {
  return (props: T) => {
    const {
      connectors: { connect, drag },
      selected,
      hovered,
      actions: { setProp },
    } = useNode((state) => ({
      selected: state.events.selected,
      hovered: state.events.hovered,
    }));

    return (
      <div
        ref={(ref) => connect(drag(ref))}
        className={cn(
          'relative',
          {
            'ring-2 ring-blue-500': selected,
            'ring-1 ring-blue-300': hovered,
          }
        )}
      >
        <Component {...props} />
        {selected && (
          <div className="absolute top-0 left-0 bg-blue-500 text-white text-xs px-2 py-1">
            {schema.name}
          </div>
        )}
      </div>
    );
  };
};

// 使用示例
export const CraftButton = withCraftWrapper(Button, {
  name: 'Button',
  category: 'interactive',
  props: {
    text: {
      type: 'string',
      default: 'Click me',
    },
    variant: {
      type: 'select',
      options: ['primary', 'secondary', 'ghost'],
      default: 'primary',
    },
  },
});

CraftButton.craft = {
  props: {
    text: 'Click me',
    variant: 'primary',
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true,
  },
  related: {
    settings: ButtonSettings, // 属性面板组件
  },
};
```

### 属性面板设计

```typescript
// 属性面板组件
import { useNode } from '@craftjs/core';
import { Input, Select, Checkbox } from '@flexihub/ui';

interface ButtonSettingsProps {
  text: string;
  variant: 'primary' | 'secondary' | 'ghost';
  disabled: boolean;
}

export const ButtonSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ButtonSettingsProps,
  }));

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="block text-sm font-medium mb-2">按钮文本</label>
        <Input
          value={props.text}
          onChange={(e) => setProp((props: ButtonSettingsProps) => {
            props.text = e.target.value;
          })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">按钮样式</label>
        <Select
          value={props.variant}
          onValueChange={(value) => setProp((props: ButtonSettingsProps) => {
            props.variant = value as any;
          })}
        >
          <SelectItem value="primary">主要</SelectItem>
          <SelectItem value="secondary">次要</SelectItem>
          <SelectItem value="ghost">幽灵</SelectItem>
        </Select>
      </div>

      <div>
        <Checkbox
          checked={props.disabled}
          onCheckedChange={(checked) => setProp((props: ButtonSettingsProps) => {
            props.disabled = !!checked;
          })}
        />
        <label className="ml-2 text-sm">禁用状态</label>
      </div>
    </div>
  );
};
```

## Next.js SSR渲染优化

### 页面渲染器设计

```typescript
// 服务端页面渲染器
import { GetServerSideProps } from 'next';
import { ComponentRenderer } from '@flexihub/website-components';

interface PageProps {
  pageData: {
    id: string;
    title: string;
    content: any; // Craft.js序列化数据
    metadata: {
      description: string;
      keywords: string[];
    };
  };
  website: {
    id: string;
    name: string;
    domain: string;
    settings: any;
  };
}

export default function Page({ pageData, website }: PageProps) {
  return (
    <>
      <Head>
        <title>{pageData.title} - {website.name}</title>
        <meta name="description" content={pageData.metadata.description} />
        <meta name="keywords" content={pageData.metadata.keywords.join(', ')} />
        {/* 更多SEO meta标签 */}
      </Head>

      <ComponentRenderer
        content={pageData.content}
        mode="render" // 渲染模式，非编辑模式
      />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { domain, slug } = context.params!;

  try {
    // 1. 根据域名获取网站信息
    const website = await getWebsiteByDomain(domain as string);
    if (!website) {
      return { notFound: true };
    }

    // 2. 获取页面数据
    const pageData = await getPageBySlug(website.id, slug as string);
    if (!pageData || pageData.status !== 'published') {
      return { notFound: true };
    }

    return {
      props: {
        pageData,
        website,
      },
    };
  } catch (error) {
    console.error('Error fetching page data:', error);
    return { notFound: true };
  }
};
```

### 性能优化策略

```typescript
// 缓存策略配置
export const revalidate = 3600; // ISR: 1小时重新验证

// 组件懒加载
const LazyGallery = dynamic(() => import('@flexihub/website-components/Gallery'), {
  loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded" />,
  ssr: false, // 复杂组件客户端渲染
});

// 图片优化
import Image from 'next/image';

const OptimizedImage: React.FC<ImageProps> = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      {...props}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,..." // 模糊占位图
      priority={props.priority}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
};
```

## 多租户支持方案

### 域名路由配置

```typescript
// next.config.js
module.exports = {
  async rewrites() {
    return [
      // 自定义域名重写
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: '(?!localhost|127.0.0.1|.*\\.vercel\\.app$).*',
          },
        ],
        destination: '/sites/:path*',
      },
    ];
  },

  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

// 中间件: 域名解析
import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const hostname = request.headers.get('host');

  // 检查是否是自定义域名
  if (hostname && !isSystemDomain(hostname)) {
    // 重写到租户页面
    const url = request.nextUrl.clone();
    url.pathname = `/[domain]${url.pathname}`;
    url.searchParams.set('domain', hostname);

    return NextResponse.rewrite(url);
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next|_static|favicon.ico).*)'],
};
```

## 开发工具和CLI

### 组件生成器

```typescript
// CLI工具: 生成新组件
import { Command } from 'commander';
import fs from 'fs-extra';
import path from 'path';

const program = new Command();

program
  .command('component <name>')
  .description('创建新的网站组件')
  .option('-c, --category <category>', '组件分类', 'content')
  .action(async (name, options) => {
    const componentPath = path.join(
      process.cwd(),
      'packages/website-components/src/components',
      options.category,
      `${name}.tsx`,
    );

    const componentTemplate = `
import React from 'react';
import { useNode } from '@craftjs/core';
import { withCraftWrapper } from '../utils/craft-wrapper';

interface ${name}Props {
  // 定义组件属性
}

const ${name}Component: React.FC<${name}Props> = (props) => {
  return (
    <div>
      {/* 组件实现 */}
    </div>
  );
};

export const ${name} = withCraftWrapper(${name}Component, {
  name: '${name}',
  category: '${options.category}',
  props: {
    // 属性定义
  },
});

${name}.craft = {
  props: {
    // 默认属性值
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
  },
};
`;

    await fs.outputFile(componentPath, componentTemplate);
    console.log(`✅ 组件 ${name} 创建成功: ${componentPath}`);
  });

program.parse();
```

这个技术实施方案涵盖了：

1. **完整的用户编辑流程**
2. **权限验证系统设计**
3. **API接口规范**
4. **数据库设计**
5. **Craft.js集成方案**
6. **Next.js SSR优化**
7. **多租户支持**
8. **开发工具和CLI**

每个部分都提供了具体的代码实现示例，确保开发团队可以按照这个方案进行实施。
