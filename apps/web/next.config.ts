import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // 图片配置
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // 重定向配置
  async redirects() {
    return [
      // 根路径重定向到默认语言
      {
        source: '/',
        destination: '/zh-CN',
        permanent: false,
      },
    ];
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
  },

  // 编译选项
  typescript: {
    // !! WARN !!
    // 危险：允许生产构建成功完成即使项目有类型错误
    // 在开发环境中不建议这样做
    ignoreBuildErrors: false,
  },

  // ESLint配置
  eslint: {
    ignoreDuringBuilds: false,
  },

  // PWA支持（可选）
  // 如果需要PWA功能，可以取消注释下面的配置
  /*
  async headers() {
    return [
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  */
};

export default nextConfig;
