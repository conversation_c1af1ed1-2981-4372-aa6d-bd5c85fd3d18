import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';

// 支持的语言列表
const locales = ['zh-CN', 'en-US', 'zh-TW', 'ja-JP'];
const defaultLocale = 'zh-CN';

// 创建国际化中间件
const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  // 自动检测用户语言偏好
  localeDetection: true,
  // 当访问根路径时，重定向到默认语言
  pathnames: {
    '/': '/',
    '/about': {
      'zh-CN': '/about',
      'en-US': '/about',
      'zh-TW': '/about',
      'ja-JP': '/about'
    }
  }
});

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 处理多租户域名解析
  const hostname = request.headers.get('host') || '';

  // 排除 Next.js 内部路径
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // 检查是否为自定义域名（多租户）
  const isCustomDomain = !hostname.includes('localhost') &&
                          !hostname.includes('vercel.app') &&
                          !hostname.includes('flexihub.com'); // 假设这是主域名

  if (isCustomDomain) {
    // 自定义域名处理 - 重写到多租户路由
    const domainPath = `/[domain]${pathname}`;
    const url = request.nextUrl.clone();
    url.pathname = domainPath;

    // 添加域名信息到请求头
    const response = NextResponse.rewrite(url);
    response.headers.set('x-custom-domain', hostname);

    return response;
  }

  // 应用国际化中间件
  return intlMiddleware(request);
}

export const config = {
  // 匹配除了 API 路由和静态文件外的所有路径
  matcher: [
    // 启用所有国际化路径
    '/',
    '/(zh-CN|en-US|zh-TW|ja-JP)/:path*',
    // 启用根级路径（多租户域名）
    '/((?!_next|api|favicon.ico).*)'
  ]
};
