import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// 支持的语言列表
export const locales = ['zh-CN', 'en-US', 'zh-TW', 'ja-JP'] as const;
export const defaultLocale = 'zh-CN' as const;

export type Locale = typeof locales[number];

export default getRequestConfig(async ({ locale }) => {
  // 验证传入的语言是否支持
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  return {
    messages: (await import(`./messages/${locale}.json`)).default,
    timeZone: 'Asia/Shanghai',
    now: new Date()
  };
});
