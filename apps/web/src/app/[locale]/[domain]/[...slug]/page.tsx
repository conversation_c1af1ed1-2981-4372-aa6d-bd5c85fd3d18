import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { headers } from 'next/headers';
import PageRenderer from '../../../../components/renderers/PageRenderer';

interface SitePageProps {
  params: {
    locale: string;
    domain: string;
    slug: string[];
  };
}

// 模拟API调用 - 根据域名和路径获取页面数据
async function getPageData(domain: string, slug: string[]) {
  // 这里应该调用实际的API
  // const response = await fetch(`/api/sites/${domain}/pages/${slug.join('/')}`);

  // 模拟数据
  const mockPageData = {
    id: '1',
    title: `${domain} - ${slug.join('/')}`,
    slug: slug.join('/') || 'home',
    status: 'published' as const,
    content: JSON.stringify({
      ROOT: {
        type: 'Container',
        props: {},
        nodes: ['text-1']
      },
      'text-1': {
        type: 'Text',
        props: {
          content: `欢迎访问 ${domain}`,
          tag: 'h1',
          fontSize: '3xl',
          fontWeight: 'bold',
          textAlign: 'center'
        },
        nodes: []
      }
    }),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    publishedAt: new Date().toISOString()
  };

  return mockPageData;
}

// 根据域名获取网站信息
async function getSiteInfo(domain: string) {
  // 模拟API调用
  return {
    id: '1',
    name: domain,
    title: `${domain} 官网`,
    description: `欢迎访问 ${domain}`,
    theme: {
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b'
      }
    }
  };
}

export async function generateMetadata({
  params: { domain, slug }
}: SitePageProps): Promise<Metadata> {
  try {
    const pageData = await getPageData(domain, slug);
    const siteInfo = await getSiteInfo(domain);

    return {
      title: `${pageData.title} - ${siteInfo.title}`,
      description: siteInfo.description,
      openGraph: {
        title: pageData.title,
        description: siteInfo.description,
        siteName: siteInfo.title,
      },
    };
  } catch (error) {
    console.error('生成元数据失败:', error);
    return {
      title: '页面未找到',
      description: '请求的页面不存在'
    };
  }
}

export default async function SitePage({
  params: { domain, slug, locale }
}: SitePageProps) {
  // 获取自定义域名信息
  const headersList = await headers();
  const customDomain = headersList.get('x-custom-domain') || domain;

  try {
    // 获取页面数据
    const pageData = await getPageData(customDomain, slug);
    const siteInfo = await getSiteInfo(customDomain);

    if (!pageData) {
      notFound();
    }

    return (
      <div
        className="min-h-screen"
        style={{
          '--primary-color': siteInfo.theme.colors.primary,
          '--secondary-color': siteInfo.theme.colors.secondary,
        } as React.CSSProperties}
      >
        {/* 页面内容渲染器 */}
        <PageRenderer
          pageData={pageData}
          className="w-full"
        />

        {/* 页脚 - FlexiHub 技术支持标识 */}
        <footer className="border-t border-gray-200 py-4 text-center text-sm text-gray-500">
          <p>
            {locale === 'zh-CN' ? '技术支持：' :
             locale === 'en-US' ? 'Powered by ' :
             locale === 'zh-TW' ? '技術支援：' :
             'Powered by '}
            <a
              href="https://flexihub.com"
              className="text-blue-600 hover:text-blue-800"
              target="_blank"
              rel="noopener noreferrer"
            >
              FlexiHub
            </a>
          </p>
        </footer>
      </div>
    );
  } catch (error) {
    console.error('页面渲染错误:', error);

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {locale === 'zh-CN' ? '页面加载失败' :
             locale === 'en-US' ? 'Page Load Failed' :
             locale === 'zh-TW' ? '頁面載入失敗' :
             'ページの読み込みに失敗しました'}
          </h1>
          <p className="text-gray-600">
            {locale === 'zh-CN' ? '请稍后重试或联系网站管理员' :
             locale === 'en-US' ? 'Please try again later or contact the site administrator' :
             locale === 'zh-TW' ? '請稍後重試或聯絡網站管理員' :
             '後でもう一度試すか、サイト管理者にお問い合わせください'}
          </p>
        </div>
      </div>
    );
  }
}
