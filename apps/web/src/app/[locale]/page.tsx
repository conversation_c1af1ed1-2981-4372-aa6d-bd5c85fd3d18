import { Metadata } from 'next';

interface HomePageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({
  params: { locale }
}: HomePageProps): Promise<Metadata> {
  const titles = {
    'zh-CN': 'FlexiHub - 专业网站建设平台',
    'en-US': 'FlexiHub - Professional Website Builder',
    'zh-TW': 'FlexiHub - 專業網站建設平台',
    'ja-JP': 'FlexiHub - プロフェッショナルウェブサイトビルダー'
  };

  const descriptions = {
    'zh-CN': 'FlexiHub为您提供专业的网站建设服务，支持拖拽式编辑，让网站制作变得简单高效。',
    'en-US': 'FlexiHub provides professional website building services with drag-and-drop editing to make website creation simple and efficient.',
    'zh-TW': 'FlexiHub為您提供專業的網站建設服務，支援拖拽式編輯，讓網站製作變得簡單高效。',
    'ja-JP': 'FlexiHubはドラッグアンドドロップ編集でウェブサイト作成を簡単かつ効率的にするプロフェッショナルなウェブサイト構築サービスを提供します。'
  };

  return {
    title: titles[locale as keyof typeof titles] || titles['zh-CN'],
    description: descriptions[locale as keyof typeof descriptions] || descriptions['zh-CN'],
  };
}

export default function HomePage({ params: { locale } }: HomePageProps) {
  const content = {
    'zh-CN': {
      title: '欢迎使用 FlexiHub',
      subtitle: '专业的网站建设平台',
      description: '通过拖拽式编辑器，轻松创建美观、专业的网站',
    },
    'en-US': {
      title: 'Welcome to FlexiHub',
      subtitle: 'Professional Website Builder',
      description: 'Create beautiful, professional websites with our drag-and-drop editor',
    },
    'zh-TW': {
      title: '歡迎使用 FlexiHub',
      subtitle: '專業的網站建設平台',
      description: '通過拖拽式編輯器，輕鬆創建美觀、專業的網站',
    },
    'ja-JP': {
      title: 'FlexiHub へようこそ',
      subtitle: 'プロフェッショナルなウェブサイトビルダー',
      description: 'ドラッグアンドドロップエディターで美しくプロフェッショナルなウェブサイトを作成',
    }
  };

  const { title, subtitle, description } = content[locale as keyof typeof content] || content['zh-CN'];

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            {title}
          </h1>
          <h2 className="text-2xl text-gray-600 mb-8">
            {subtitle}
          </h2>
          <p className="text-lg text-gray-500 max-w-2xl mx-auto mb-12">
            {description}
          </p>

          <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              {locale === 'zh-CN' ? '示例网站' :
               locale === 'en-US' ? 'Sample Website' :
               locale === 'zh-TW' ? '示例網站' :
               'サンプルウェブサイト'}
            </h3>
            <p className="text-gray-600">
              {locale === 'zh-CN' ? '这是一个用户端页面示例。在实际使用中，这里会显示通过编辑器创建的网站内容。' :
               locale === 'en-US' ? 'This is an example of a user-facing page. In actual use, this would display website content created through the editor.' :
               locale === 'zh-TW' ? '這是一個用戶端頁面示例。在實際使用中，這裡會顯示通過編輯器創建的網站內容。' :
               'これはユーザー向けページの例です。実際の使用では、エディターで作成されたウェブサイトコンテンツが表示されます。'}
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
