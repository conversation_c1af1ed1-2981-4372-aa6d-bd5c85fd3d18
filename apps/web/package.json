{"name": "@vben/web", "version": "0.1.0", "description": "FlexiHub 用户端应用 - Next.js SSR网站渲染器", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@flexihub/shared": "workspace:*", "@flexihub/website-components": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.5", "next-intl": "^3.21.1", "axios": "^1.7.7", "js-cookie": "^3.0.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/js-cookie": "^3.0.6", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}