{"name": "example-landing", "version": "0.2.0", "private": true, "scripts": {"start": "next dev -p 3001", "build": "next build", "clean": "rimraf lib .next out dist"}, "dependencies": {"@craftjs/core": "workspace:*", "@craftjs/layers": "workspace:*", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.3", "@mui/material": "^6.4.3", "autoprefixer": "latest", "classnames": "2.2.6", "cssnano": "4.1.10", "debounce": "1.2.0", "lzutf8": "0.5.5", "next": "15.1.6", "next-seo": "6.6.0", "postcss": "latest", "re-resizable": "6.10.3", "react": "19.0.0", "react-color": "2.17.3", "react-contenteditable": "3.3.2", "react-dom": "19.0.0", "react-loading": "2.0.3", "react-rnd": "10.1.1", "react-youtube": "7.9.0", "styled-components": "6.1.15"}, "devDependencies": {"@babel/core": "7.7.5", "@fullhuman/postcss-purgecss": "1.3.0", "@types/classnames": "2.2.9", "@types/node": "12.12.5", "@types/react": "19.0.8", "@types/react-color": "3.0.13", "@types/react-dom": "19.0.3", "babel-plugin-inline-react-svg": "2.0.1", "cross-env": "6.0.3", "postcss-import": "12.0.1", "postcss-preset-env": "6.7.0", "tailwindcss": "3.4.17", "typescript": "5.7.3"}}