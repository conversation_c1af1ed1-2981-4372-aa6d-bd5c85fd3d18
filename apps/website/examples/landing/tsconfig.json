{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "preserve", "lib": ["dom", "es2017"], "baseUrl": ".", "moduleResolution": "node", "strict": true, "allowJs": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": true, "isolatedModules": true, "removeComments": false, "preserveConstEnums": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitAny": false, "strictNullChecks": false, "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}