import { Editor, Element, Frame } from '@craftjs/core';
import { createTheme, ThemeProvider } from '@mui/material';
import { NextSeo } from 'next-seo';
import React from 'react';

import { RenderNode, Viewport } from '../components/editor';
import { Container, Text } from '../components/selectors';
import { Button } from '../components/selectors/Button';
import { Custom1, OnlyButtons } from '../components/selectors/Custom1';
import { Custom2, Custom2VideoDrop } from '../components/selectors/Custom2';
import { Custom3, Custom3BtnDrop } from '../components/selectors/Custom3';
import { Video } from '../components/selectors/Video';

const theme = createTheme({
  typography: {
    fontFamily: [
      'acumin-pro',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <div className="h-full h-screen">
        <NextSeo
          canonical="https://craft.js.org/"
          description="A React framework for building drag-n-drop page editors."
          title="Craft.js"
          twitter={{
            site: 'craft.js.org',
            cardType: 'summary_large_image',
          }}
        />
        <Editor
          enabled={false}
          onRender={RenderNode}
          resolver={{
            Container,
            Text,
            Custom1,
            Custom2,
            Custom2VideoDrop,
            Custom3,
            Custom3BtnDrop,
            OnlyButtons,
            Button,
            Video,
          }}
        >
          <Viewport>
            <Frame>
              <Element
                background={{ r: 255, g: 255, b: 255, a: 1 }}
                canvas
                custom={{ displayName: 'App' }}
                height="auto"
                is={Container}
                padding={['40', '40', '40', '40']}
                width="800px"
              >
                <Element
                  canvas
                  custom={{ displayName: 'Introduction' }}
                  flexDirection="row"
                  height="auto"
                  is={Container}
                  margin={['0', '0', '40', '0']}
                  padding={['40', '40', '40', '40']}
                  width="100%"
                >
                  <Element
                    canvas
                    custom={{ displayName: 'Heading' }}
                    height="100%"
                    is={Container}
                    padding={['0', '20', '0', '20']}
                    width="40%"
                  >
                    <Text
                      fontSize="23"
                      fontWeight="400"
                      text="Craft.js is a React framework for building powerful &amp; feature-rich drag-n-drop page editors."
                    ></Text>
                  </Element>
                  <Element
                    canvas
                    custom={{ displayName: 'Description' }}
                    height="100%"
                    is={Container}
                    padding={['0', '20', '0', '20']}
                    width="60%"
                  >
                    <Text
                      fontSize="14"
                      fontWeight="400"
                      text="Everything you see here, including the editor, itself is made of React components. Craft.js comes only with the building blocks for a page editor; it provides a drag-n-drop system and handles the way user components should be rendered, updated and moved, among other things. <br /> <br /> You control the way your editor looks and behave."
                    ></Text>
                  </Element>
                </Element>

                <Element
                  background={{ r: 39, g: 41, b: 41, a: 1 }}
                  canvas
                  custom={{ displayName: 'ComplexSection' }}
                  flexDirection="column"
                  height="auto"
                  is={Container}
                  margin={['0', '0', '40', '0']}
                  padding={['40', '40', '40', '40']}
                  width="100%"
                >
                  <Element
                    alignItems="center"
                    background={{
                      r: 76,
                      g: 78,
                      b: 78,
                      a: 0,
                    }}
                    canvas
                    custom={{ displayName: 'Wrapper' }}
                    flexDirection="row"
                    height="auto"
                    is={Container}
                    margin={['0', '0', '0', '0']}
                    width="100%"
                  >
                    <Element
                      alignItems="center"
                      background={{
                        r: 0,
                        g: 0,
                        b: 0,
                        a: 0,
                      }}
                      canvas
                      custom={{ displayName: 'Square' }}
                      flexDirection="row"
                      height="250px"
                      is={Container}
                      padding={['0', '0', '0', '0']}
                      width="350px"
                    >
                      <Element
                        alignItems="center"
                        background={{
                          r: 76,
                          g: 78,
                          b: 78,
                          a: 1,
                        }}
                        canvas
                        custom={{ displayName: 'Outer' }}
                        height="90%"
                        is={Container}
                        justifyContent="center"
                        padding={['10', '20', '10', '20']}
                        shadow={25}
                        width="90%"
                      >
                        <Element
                          alignItems="center"
                          background={{
                            r: 76,
                            g: 78,
                            b: 78,
                            a: 1,
                          }}
                          canvas
                          custom={{ displayName: 'Middle' }}
                          height="80%"
                          is={Container}
                          justifyContent="center"
                          padding={['10', '20', '10', '20']}
                          shadow={50}
                          width="80%"
                        >
                          <Element
                            alignItems="center"
                            background={{
                              r: 76,
                              g: 78,
                              b: 78,
                              a: 1,
                            }}
                            canvas
                            custom={{ displayName: 'Inner' }}
                            height="60%"
                            is={Container}
                            justifyContent="center"
                            padding={['10', '20', '10', '20']}
                            shadow={50}
                            width="60%"
                          />
                        </Element>
                      </Element>
                    </Element>
                    <Element
                      background={{
                        r: 0,
                        g: 0,
                        b: 0,
                        a: 0,
                      }}
                      canvas
                      custom={{ displayName: 'Content' }}
                      fillSpace="yes"
                      flexDirection="column"
                      height="100%"
                      is={Container}
                      padding={['0', '0', '0', '20']}
                      width="55%"
                    >
                      <Text
                        color={{
                          r: '255',
                          g: '255',
                          b: '255',
                          a: '1',
                        }}
                        fontSize="20"
                        margin={['0', '0', '18', '0']}
                        text="Design complex components"
                      ></Text>
                      <Text
                        color={{
                          r: '255',
                          g: '255',
                          b: '255',
                          a: '0.8',
                        }}
                        fontSize="14"
                        fontWeight="400"
                        text="You can define areas within your React component which users can drop other components into. <br/><br />You can even design how the component should be edited — content editable, drag to resize, have inputs on toolbars — anything really."
                      ></Text>
                    </Element>
                  </Element>
                </Element>
                <Element
                  background={{
                    r: 234,
                    g: 245,
                    b: 245,
                    a: 1,
                  }}
                  canvas
                  custom={{ displayName: 'Programmatic' }}
                  flexDirection="column"
                  height="auto"
                  is={Container}
                  margin={['0', '0', '40', '0']}
                  padding={['40', '40', '40', '40']}
                  width="100%"
                >
                  <Element
                    background={{
                      r: 76,
                      g: 78,
                      b: 78,
                      a: 0,
                    }}
                    canvas
                    custom={{ displayName: 'Heading' }}
                    flexDirection="column"
                    height="auto"
                    is={Container}
                    margin={['0,', '0', '20', '0']}
                    width="100%"
                  >
                    <Text
                      color={{
                        r: '46',
                        g: '47',
                        b: '47',
                        a: '1',
                      }}
                      fontSize="23"
                      text="Programmatic drag &amp; drop"
                    ></Text>
                    <Text
                      fontSize="14"
                      fontWeight="400"
                      text="Govern what goes in and out of your components"
                    ></Text>
                  </Element>
                  <Element
                    background={{
                      r: 76,
                      g: 78,
                      b: 78,
                      a: 0,
                    }}
                    canvas
                    custom={{ displayName: 'Content' }}
                    flexDirection="row"
                    height="auto"
                    is={Container}
                    margin={['30', '0', '0', '0']}
                    width="100%"
                  >
                    <Element
                      background={{
                        r: 0,
                        g: 0,
                        b: 0,
                        a: 0,
                      }}
                      canvas
                      custom={{ displayName: 'Left' }}
                      flexDirection="row"
                      is={Container}
                      padding={['0', '20', '0', '0']}
                      width="45%"
                    >
                      <Custom1
                        background={{
                          r: 119,
                          g: 219,
                          b: 165,
                          a: 1,
                        }}
                        height="auto"
                        margin={['0', '0', '0', '0']}
                        padding={['20', '20', '20', '20']}
                        shadow={40}
                        width="100%"
                      />
                    </Element>
                    <Element
                      background={{
                        r: 0,
                        g: 0,
                        b: 0,
                        a: 0,
                      }}
                      canvas
                      custom={{ displayName: 'Right' }}
                      flexDirection="column"
                      is={Container}
                      padding={['0', '0', '0', '20']}
                      width="55%"
                    >
                      <Custom2
                        alignItems="center"
                        background={{
                          r: 108,
                          g: 126,
                          b: 131,
                          a: 1,
                        }}
                        flexDirection="row"
                        height="125px"
                        margin={['0', '0', '0', '0']}
                        padding={['0', '0', '0', '20']}
                        shadow={40}
                        width="100%"
                      />
                      <Custom3
                        background={{
                          r: 134,
                          g: 187,
                          b: 201,
                          a: 1,
                        }}
                        flexDirection="column"
                        height="auto"
                        margin={['20', '0', '0', '0']}
                        padding={['20', '20', '20', '20']}
                        shadow={40}
                        width="100%"
                      />
                    </Element>
                  </Element>
                </Element>
              </Element>
            </Frame>
          </Viewport>
        </Editor>
      </div>
    </ThemeProvider>
  );
}

export default App;
