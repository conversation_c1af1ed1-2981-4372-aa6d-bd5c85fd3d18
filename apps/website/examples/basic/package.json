{"name": "example-basic", "version": "0.2.0", "private": true, "dependencies": {"@craftjs/core": "workspace:*", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.3", "clsx": "latest", "copy-to-clipboard": "3.2.0", "lzutf8": "0.5.5", "next": "15.1.6", "prop-types": "latest", "react": "19.0.0", "react-colorful": "^5.6.1", "react-contenteditable": "3.3.3", "react-dom": "19.0.0"}, "scripts": {"start": "next dev -p 3002", "build": "next build", "clean": "rimraf lib .next out dist"}, "devDependencies": {"@types/react": "19.0.8", "@types/react-dom": "19.0.3", "cross-env": "6.0.3"}}