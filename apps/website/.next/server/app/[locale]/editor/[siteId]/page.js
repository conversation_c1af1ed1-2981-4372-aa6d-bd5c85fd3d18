/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/editor/[siteId]/page";
exports.ids = ["app/[locale]/editor/[siteId]/page"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&page=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx&appDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&page=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx&appDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?6ac0\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/editor/[siteId]/page.tsx */ \"(rsc)/./src/app/[locale]/editor/[siteId]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'editor',\n        {\n        children: [\n        '[siteId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/editor/[siteId]/page\",\n        pathname: \"/[locale]/editor/[siteId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&page=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx&appDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/editor/[siteId]/page.tsx */ \"(rsc)/./src/app/[locale]/editor/[siteId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy4zX0BiYWJlbCtjb3JlQDcuMjYuMTBfQHBsYXl3cmlnaHQrdGVzdEAxLjUyLjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMV9zYXNzQDEuODcuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeWFuZ3dlbnFpYW5nJTJGZGV2JTJGYWklMkZGbGV4aUh1YiUyRnZ1ZS12YmVuLWFkbWluJTJGYXBwcyUyRndlYnNpdGUlMkZzcmMlMkZhcHAlMkYlNUJsb2NhbGUlNUQlMkZlZGl0b3IlMkYlNUJzaXRlSWQlNUQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMveWFuZ3dlbnFpYW5nL2Rldi9haS9GbGV4aUh1Yi92dWUtdmJlbi1hZG1pbi9hcHBzL3dlYnNpdGUvc3JjL2FwcC9bbG9jYWxlXS9lZGl0b3IvW3NpdGVJZF0vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy4zX0BiYWJlbCtjb3JlQDcuMjYuMTBfQHBsYXl3cmlnaHQrdGVzdEAxLjUyLjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMV9zYXNzQDEuODcuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeWFuZ3dlbnFpYW5nJTJGZGV2JTJGYWklMkZGbGV4aUh1YiUyRnZ1ZS12YmVuLWFkbWluJTJGYXBwcyUyRndlYnNpdGUlMkZzcmMlMkZhcHAlMkYlNUJsb2NhbGUlNUQlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/[locale]/editor/[siteId]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/[locale]/editor/[siteId]/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f1d32316f93\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMveWFuZ3dlbnFpYW5nL2Rldi9haS9GbGV4aUh1Yi92dWUtdmJlbi1hZG1pbi9hcHBzL3dlYnNpdGUvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFmMWQzMjMxNmY5M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'FlexiHub - 智能网站构建平台',\n    description: '专业的拖拽式网站构建平台，支持可视化编辑和多语言渲染'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFNTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsiL1VzZXJzL3lhbmd3ZW5xaWFuZy9kZXYvYWkvRmxleGlIdWIvdnVlLXZiZW4tYWRtaW4vYXBwcy93ZWJzaXRlL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5cbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSc7XG5cbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRmxleGlIdWIgLSDmmbrog73nvZHnq5nmnoTlu7rlubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+S4k+S4mueahOaLluaLveW8j+e9keermeaehOW7uuW5s+WPsO+8jOaUr+aMgeWPr+inhuWMlue8lui+keWSjOWkmuivreiogOa4suafkycsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/editor/[siteId]/page.tsx */ \"(ssr)/./src/app/[locale]/editor/[siteId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy4zX0BiYWJlbCtjb3JlQDcuMjYuMTBfQHBsYXl3cmlnaHQrdGVzdEAxLjUyLjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMV9zYXNzQDEuODcuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeWFuZ3dlbnFpYW5nJTJGZGV2JTJGYWklMkZGbGV4aUh1YiUyRnZ1ZS12YmVuLWFkbWluJTJGYXBwcyUyRndlYnNpdGUlMkZzcmMlMkZhcHAlMkYlNUJsb2NhbGUlNUQlMkZlZGl0b3IlMkYlNUJzaXRlSWQlNUQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMveWFuZ3dlbnFpYW5nL2Rldi9haS9GbGV4aUh1Yi92dWUtdmJlbi1hZG1pbi9hcHBzL3dlYnNpdGUvc3JjL2FwcC9bbG9jYWxlXS9lZGl0b3IvW3NpdGVJZF0vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(ssr)/./src/app/[locale]/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy4zX0BiYWJlbCtjb3JlQDcuMjYuMTBfQHBsYXl3cmlnaHQrdGVzdEAxLjUyLjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMV9zYXNzQDEuODcuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeWFuZ3dlbnFpYW5nJTJGZGV2JTJGYWklMkZGbGV4aUh1YiUyRnZ1ZS12YmVuLWFkbWluJTJGYXBwcyUyRndlYnNpdGUlMkZzcmMlMkZhcHAlMkYlNUJsb2NhbGUlNUQlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2F%5Blocale%5D%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fnode_modules%2F.pnpm%2Fnext%4015.3.3_%40babel%2Bcore%407.26.10_%40playwright%2Btest%401.52.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1_sass%401.87.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/[locale]/editor/[siteId]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/[locale]/editor/[siteId]/page.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_editor_properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/editor/properties/PropertyPanel */ \"(ssr)/./src/components/editor/properties/PropertyPanel.tsx\");\n/* harmony import */ var _components_editor_toolbox_ToolboxPanel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/editor/toolbox/ToolboxPanel */ \"(ssr)/./src/components/editor/toolbox/ToolboxPanel.tsx\");\n/* harmony import */ var _components_materials_registry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/materials/registry */ \"(ssr)/./src/components/materials/registry/index.ts\");\n/* harmony import */ var _components_materials_registry_basic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/materials/registry/basic */ \"(ssr)/./src/components/materials/registry/basic.ts\");\n/* harmony import */ var _components_materials_registry_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/materials/registry/layout */ \"(ssr)/./src/components/materials/registry/layout.ts\");\n/* harmony import */ var _components_StagewiseProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/StagewiseProvider */ \"(ssr)/./src/components/StagewiseProvider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction EditorPage({ params }) {\n    const { locale: _locale, siteId } = react__WEBPACK_IMPORTED_MODULE_9___default().use(params);\n    const [enabled, setEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(true);\n    const [resolver, setResolver] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({});\n    // 初始化物料组件\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // 注册所有物料组件\n            _components_materials_registry__WEBPACK_IMPORTED_MODULE_3__.materialRegistry.clear(); // 清空之前的注册\n            _components_materials_registry__WEBPACK_IMPORTED_MODULE_3__.materialRegistry.registerBatch([\n                ..._components_materials_registry_basic__WEBPACK_IMPORTED_MODULE_4__.basicComponents,\n                ..._components_materials_registry_layout__WEBPACK_IMPORTED_MODULE_5__.layoutComponents\n            ]);\n            // 获取CraftJS resolver\n            const craftResolver = _components_materials_registry__WEBPACK_IMPORTED_MODULE_3__.materialRegistry.getCraftResolver();\n            setResolver(craftResolver);\n        }\n    }[\"EditorPage.useEffect\"], []);\n    const handleSerialize = ()=>{\n        // eslint-disable-next-line no-console\n        console.log('序列化功能将在这里实现');\n    };\n    const handlePreview = ()=>{\n        setEnabled(!enabled);\n    };\n    // 获取物料组件 (将来用于动态内容)\n    // const MaterialText = resolver.Text;\n    // const MaterialButton = resolver.Button;\n    // const MaterialContainer = resolver.Container;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between border-b border-gray-200 bg-white px-6 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"FlexiHub 页面编辑器\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"站点ID: \",\n                                    siteId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        checked: enabled,\n                                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\",\n                                        onChange: (e)=>setEnabled(e.target.checked),\n                                        type: \"checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handlePreview,\n                                size: \"sm\",\n                                variant: \"outline\",\n                                children: enabled ? '预览' : '编辑'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleSerialize,\n                                size: \"sm\",\n                                variant: \"default\",\n                                children: \"保存\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_craftjs_core__WEBPACK_IMPORTED_MODULE_8__.Editor, {\n                enabled: enabled,\n                resolver: resolver,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 overflow-y-auto border-r border-gray-200 bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_toolbox_ToolboxPanel__WEBPACK_IMPORTED_MODULE_2__.ToolboxPanel, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto bg-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-[600px] rounded-lg border bg-white shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_craftjs_core__WEBPACK_IMPORTED_MODULE_8__.Frame, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: '#ffffff',\n                                                padding: '40px',\n                                                minHeight: '400px',\n                                                textAlign: 'center',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '20px',\n                                                alignItems: 'center',\n                                                justifyContent: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: '600',\n                                                        color: '#1f2937',\n                                                        margin: 0\n                                                    },\n                                                    children: \"欢迎使用 FlexiHub 页面编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: '16px',\n                                                        color: '#6b7280',\n                                                        margin: 0\n                                                    },\n                                                    children: \"你可以从左侧工具箱拖拽组件到这里开始创建你的页面。\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        background: '#f9fafb',\n                                                        padding: '20px',\n                                                        borderRadius: '8px',\n                                                        display: 'flex',\n                                                        gap: '16px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700\",\n                                                            children: \"开始创建\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50\",\n                                                            children: \"查看模板\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 overflow-y-auto border-l border-gray-200 bg-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_1__.PropertyPanel, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StagewiseProvider__WEBPACK_IMPORTED_MODULE_6__.StagewiseProvider, {}, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/editor/[siteId]/page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/editor/[siteId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layouts_SiteLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layouts/SiteLayout */ \"(ssr)/./src/components/layouts/SiteLayout.tsx\");\n/* harmony import */ var _hooks_useAppMode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAppMode */ \"(ssr)/./src/hooks/useAppMode.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LocaleLayout({ children, params }) {\n    const { locale } = react__WEBPACK_IMPORTED_MODULE_3___default().use(params);\n    const { isEditor } = (0,_hooks_useAppMode__WEBPACK_IMPORTED_MODULE_2__.useAppMode)();\n    // 对于编辑器页面，直接渲染children，不使用额外的布局包装\n    if (isEditor) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_SiteLayout__WEBPACK_IMPORTED_MODULE_1__.SiteLayout, {\n        locale: locale,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/app/[locale]/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFNkQ7QUFDYjtBQUN0QjtBQVNYLFNBQVNHLGFBQWEsRUFBRUMsUUFBUSxFQUFFQyxNQUFNLEVBQXFCO0lBQzFFLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdKLGdEQUFTLENBQUNHO0lBQzdCLE1BQU0sRUFBRUcsUUFBUSxFQUFFLEdBQUdQLDZEQUFVQTtJQUUvQixrQ0FBa0M7SUFDbEMsSUFBSU8sVUFBVTtRQUNaLHFCQUFPO3NCQUFHSjs7SUFDWjtJQUVBLHFCQUFPLDhEQUFDSixzRUFBVUE7UUFBQ00sUUFBUUE7a0JBQVNGOzs7Ozs7QUFDdEMiLCJzb3VyY2VzIjpbIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTaXRlTGF5b3V0IH0gZnJvbSAnQC9jb21wb25lbnRzL2xheW91dHMvU2l0ZUxheW91dCc7XG5pbXBvcnQgeyB1c2VBcHBNb2RlIH0gZnJvbSAnQC9ob29rcy91c2VBcHBNb2RlJztcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBMb2NhbGVMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHBhcmFtczogUHJvbWlzZTx7XG4gICAgbG9jYWxlOiBzdHJpbmc7XG4gIH0+O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2NhbGVMYXlvdXQoeyBjaGlsZHJlbiwgcGFyYW1zIH06IExvY2FsZUxheW91dFByb3BzKSB7XG4gIGNvbnN0IHsgbG9jYWxlIH0gPSBSZWFjdC51c2UocGFyYW1zKTtcbiAgY29uc3QgeyBpc0VkaXRvciB9ID0gdXNlQXBwTW9kZSgpO1xuXG4gIC8vIOWvueS6jue8lui+keWZqOmhtemdou+8jOebtOaOpea4suafk2NoaWxkcmVu77yM5LiN5L2/55So6aKd5aSW55qE5biD5bGA5YyF6KOFXG4gIGlmIChpc0VkaXRvcikge1xuICAgIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG4gIH1cblxuICByZXR1cm4gPFNpdGVMYXlvdXQgbG9jYWxlPXtsb2NhbGV9PntjaGlsZHJlbn08L1NpdGVMYXlvdXQ+O1xufVxuIl0sIm5hbWVzIjpbIlNpdGVMYXlvdXQiLCJ1c2VBcHBNb2RlIiwiUmVhY3QiLCJMb2NhbGVMYXlvdXQiLCJjaGlsZHJlbiIsInBhcmFtcyIsImxvY2FsZSIsInVzZSIsImlzRWRpdG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StagewiseProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/StagewiseProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StagewiseProvider: () => (/* binding */ StagewiseProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ StagewiseProvider auto */ \n\n\n// 动态导入Stagewise工具栏\nconst StagewiseToolbar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components/StagewiseProvider.tsx -> \" + \"@stagewise/toolbar-next\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n// Stagewise配置\nconst stagewiseConfig = {\n    plugins: [],\n    theme: 'auto',\n    position: 'bottom-right'\n};\nfunction StagewiseProvider() {\n    const [isDevelopment, setIsDevelopment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StagewiseProvider.useEffect\": ()=>{\n            // 在客户端检查开发模式\n            setIsDevelopment(globalThis.process?.env?.NODE_ENV !== 'production');\n        }\n    }[\"StagewiseProvider.useEffect\"], []);\n    // 仅在开发模式下渲染\n    if (!isDevelopment) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StagewiseToolbar, {\n        config: stagewiseConfig\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/StagewiseProvider.tsx\",\n        lineNumber: 41,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StagewiseProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/editor/properties/PropertyPanel.tsx":
/*!************************************************************!*\
  !*** ./src/components/editor/properties/PropertyPanel.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyPanel: () => (/* binding */ PropertyPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ PropertyPanel auto */ \n\n\nconst PropertyPanel = ()=>{\n    const { selected } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_1__.useEditor)({\n        \"PropertyPanel.useEditor\": (state)=>({\n                selected: state.events.selected\n            })\n    }[\"PropertyPanel.useEditor\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"属性面板\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"编辑选中组件的属性\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: selected.size > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PropertyContent, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 text-center text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-300\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"请选择一个组件来编辑其属性\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\nconst PropertyContent = ()=>{\n    const { selected } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_1__.useEditor)({\n        \"PropertyContent.useEditor\": (state)=>({\n                selected: state.events.selected\n            })\n    }[\"PropertyContent.useEditor\"]);\n    // 如果没有选中组件，显示提示\n    if (selected.size === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-gray-900\",\n                            children: \"未选中组件\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-700\",\n                            children: \"请在画布中选择一个组件来编辑其属性。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 返回选中组件的设置界面\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg border border-blue-200 bg-blue-50 p-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-blue-900\",\n                            children: \"组件已选中\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-blue-700\",\n                            children: \"物料组件系统已集成，组件设置面板将在这里显示。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: \"选中组件数量:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        \" \",\n                        selected.size\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/properties/PropertyPanel.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/editor/properties/PropertyPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/editor/toolbox/ToolboxPanel.tsx":
/*!********************************************************!*\
  !*** ./src/components/editor/toolbox/ToolboxPanel.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolboxPanel: () => (/* binding */ ToolboxPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_materials_registry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/materials/registry */ \"(ssr)/./src/components/materials/registry/index.ts\");\n/* harmony import */ var _components_materials_registry_basic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/materials/registry/basic */ \"(ssr)/./src/components/materials/registry/basic.ts\");\n/* harmony import */ var _components_materials_registry_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/materials/registry/layout */ \"(ssr)/./src/components/materials/registry/layout.ts\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ ToolboxPanel auto */ \n\n\n\n\n\nconst ToolboxPanel = ()=>{\n    const { connectors } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_4__.useEditor)();\n    // 注册所有物料组件\n    react__WEBPACK_IMPORTED_MODULE_5___default().useEffect({\n        \"ToolboxPanel.useEffect\": ()=>{\n            _components_materials_registry__WEBPACK_IMPORTED_MODULE_1__.materialRegistry.registerBatch([\n                ..._components_materials_registry_basic__WEBPACK_IMPORTED_MODULE_2__.basicComponents,\n                ..._components_materials_registry_layout__WEBPACK_IMPORTED_MODULE_3__.layoutComponents\n            ]);\n        }\n    }[\"ToolboxPanel.useEffect\"], []);\n    // 获取分类后的工具箱数据\n    const toolboxData = _components_materials_registry__WEBPACK_IMPORTED_MODULE_1__.materialRegistry.getToolboxData();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"组件工具箱\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"拖拽组件到画布中开始设计\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-4\",\n                children: [\n                    toolboxData.map((categoryData)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium uppercase tracking-wide text-gray-700\",\n                                    children: [\n                                        categoryData.category === 'basic' && '基础组件',\n                                        categoryData.category === 'layout' && '布局组件',\n                                        categoryData.category === 'advanced' && '高级组件',\n                                        categoryData.category === 'custom' && '自定义组件'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: categoryData.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex h-16 cursor-grab flex-col items-center justify-center rounded-lg border border-gray-200 bg-white transition-all duration-200 hover:border-gray-300 hover:bg-gray-50 active:cursor-grabbing\",\n                                            ref: (ref)=>{\n                                                if (ref) {\n                                                    connectors.create(ref, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_5___default().createElement(item.component, item.props));\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mb-1 text-lg\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-center text-xs text-gray-600 group-hover:text-gray-800\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, categoryData.category, true, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 border-t border-gray-200 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"已加载 \",\n                                _components_materials_registry__WEBPACK_IMPORTED_MODULE_1__.materialRegistry.size(),\n                                \" 个组件\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/editor/toolbox/ToolboxPanel.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/editor/toolbox/ToolboxPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layouts/SiteLayout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layouts/SiteLayout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteLayout: () => (/* binding */ SiteLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SiteLayout auto */ \n\nfunction SiteLayout({ children, locale: _locale }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"site-mode min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"w-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/layouts/SiteLayout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"mt-auto py-4 text-center text-xs text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Powered by FlexiHub\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/layouts/SiteLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/layouts/SiteLayout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/layouts/SiteLayout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL1NpdGVMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUUwQjtBQU9uQixTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsUUFBUUMsT0FBTyxFQUFtQjtJQUN2RSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFLRCxXQUFVOzBCQUFVSjs7Ozs7OzBCQUcxQiw4REFBQ007Z0JBQU9GLFdBQVU7MEJBQ2hCLDRFQUFDRDs4QkFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJYiIsInNvdXJjZXMiOlsiL1VzZXJzL3lhbmd3ZW5xaWFuZy9kZXYvYWkvRmxleGlIdWIvdnVlLXZiZW4tYWRtaW4vYXBwcy93ZWJzaXRlL3NyYy9jb21wb25lbnRzL2xheW91dHMvU2l0ZUxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgU2l0ZUxheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgbG9jYWxlOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBTaXRlTGF5b3V0KHsgY2hpbGRyZW4sIGxvY2FsZTogX2xvY2FsZSB9OiBTaXRlTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNpdGUtbW9kZSBtaW4taC1zY3JlZW5cIj5cbiAgICAgIHsvKiDnlKjmiLfnvZHnq5nlhoXlrrkgKi99XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj57Y2hpbGRyZW59PC9tYWluPlxuXG4gICAgICB7Lyog5Y+v6YCJ77ya572R56uZ5bqV6YOo5L+h5oGvICovfVxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJtdC1hdXRvIHB5LTQgdGV4dC1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgIDxkaXY+UG93ZXJlZCBieSBGbGV4aUh1YjwvZGl2PlxuICAgICAgPC9mb290ZXI+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTaXRlTGF5b3V0IiwiY2hpbGRyZW4iLCJsb2NhbGUiLCJfbG9jYWxlIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiIsImZvb3RlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layouts/SiteLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Button/index.tsx":
/*!********************************************************************!*\
  !*** ./src/components/materials/components/basic/Button/index.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonPreview: () => (/* binding */ ButtonPreview),\n/* harmony export */   MaterialButton: () => (/* binding */ MaterialButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ MaterialButton,ButtonPreview auto */ \n\n\n\n/**\n * 用于编辑器的Button组件\n */ const MaterialButton = ({ text = '按钮', variant = 'default', size = 'default', disabled = false, className = '', onClick })=>{\n    const { connectors: { connect, drag }, selected } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_2__.useNode)({\n        \"MaterialButton.useNode\": (state)=>({\n                selected: state.events.selected\n            })\n    }[\"MaterialButton.useNode\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        className: ` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} transition-all duration-200`,\n        disabled: disabled,\n        onClick: onClick,\n        ref: (ref)=>{\n            if (ref) {\n                connect(drag(ref));\n            }\n            return undefined;\n        },\n        size: size,\n        variant: variant,\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/index.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/**\n * 用于用户网站的纯Button组件（预览组件）\n */ const ButtonPreview = ({ text = '按钮', variant = 'default', size = 'default', disabled = false, className = '', onClick })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        className: className,\n        disabled: disabled,\n        onClick: onClick,\n        size: size,\n        variant: variant,\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/index.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n// CraftJS配置\nMaterialButton.craft = {\n    props: {\n        text: '按钮',\n        variant: 'default',\n        size: 'default',\n        disabled: false,\n        className: ''\n    },\n    related: {\n        settings: ()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./settings */ \"(ssr)/./src/components/materials/components/basic/Button/settings.tsx\")).then((comp)=>comp.ButtonSettings)\n    },\n    rules: {\n        canDrag: true,\n        canDrop: false,\n        canMoveIn: false,\n        canMoveOut: true\n    },\n    custom: {\n        displayName: '按钮'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Button/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Button/schema.ts":
/*!********************************************************************!*\
  !*** ./src/components/materials/components/basic/Button/schema.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buttonSchema: () => (/* binding */ buttonSchema)\n/* harmony export */ });\n/**\n * Button组件的配置Schema\n */ const buttonSchema = {\n    type: 'object',\n    properties: {\n        text: {\n            type: 'string',\n            title: '按钮文本',\n            description: '显示在按钮上的文本内容',\n            default: '按钮'\n        },\n        variant: {\n            type: 'select',\n            title: '按钮样式',\n            description: '选择按钮的视觉样式',\n            default: 'default',\n            enum: [\n                {\n                    label: '默认',\n                    value: 'default'\n                },\n                {\n                    label: '危险',\n                    value: 'destructive'\n                },\n                {\n                    label: '边框',\n                    value: 'outline'\n                },\n                {\n                    label: '次要',\n                    value: 'secondary'\n                },\n                {\n                    label: '幽灵',\n                    value: 'ghost'\n                },\n                {\n                    label: '链接',\n                    value: 'link'\n                }\n            ]\n        },\n        size: {\n            type: 'select',\n            title: '按钮大小',\n            description: '选择按钮的尺寸',\n            default: 'default',\n            enum: [\n                {\n                    label: '小',\n                    value: 'sm'\n                },\n                {\n                    label: '默认',\n                    value: 'default'\n                },\n                {\n                    label: '大',\n                    value: 'lg'\n                }\n            ]\n        },\n        disabled: {\n            type: 'boolean',\n            title: '禁用状态',\n            description: '是否禁用按钮',\n            default: false\n        },\n        className: {\n            type: 'string',\n            title: '自定义样式类名',\n            description: '添加自定义的CSS类名',\n            default: ''\n        }\n    },\n    required: [\n        'text'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvY29tcG9uZW50cy9iYXNpYy9CdXR0b24vc2NoZW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFQTs7Q0FFQyxHQUNNLE1BQU1BLGVBQWdDO0lBQzNDQyxNQUFNO0lBQ05DLFlBQVk7UUFDVkMsTUFBTTtZQUNKRixNQUFNO1lBQ05HLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxTQUFTO1FBQ1g7UUFDQUMsU0FBUztZQUNQTixNQUFNO1lBQ05HLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxTQUFTO1lBQ1RFLE1BQU07Z0JBQ0o7b0JBQUVDLE9BQU87b0JBQU1DLE9BQU87Z0JBQVU7Z0JBQ2hDO29CQUFFRCxPQUFPO29CQUFNQyxPQUFPO2dCQUFjO2dCQUNwQztvQkFBRUQsT0FBTztvQkFBTUMsT0FBTztnQkFBVTtnQkFDaEM7b0JBQUVELE9BQU87b0JBQU1DLE9BQU87Z0JBQVk7Z0JBQ2xDO29CQUFFRCxPQUFPO29CQUFNQyxPQUFPO2dCQUFRO2dCQUM5QjtvQkFBRUQsT0FBTztvQkFBTUMsT0FBTztnQkFBTzthQUM5QjtRQUNIO1FBQ0FDLE1BQU07WUFDSlYsTUFBTTtZQUNORyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsU0FBUztZQUNURSxNQUFNO2dCQUNKO29CQUFFQyxPQUFPO29CQUFLQyxPQUFPO2dCQUFLO2dCQUMxQjtvQkFBRUQsT0FBTztvQkFBTUMsT0FBTztnQkFBVTtnQkFDaEM7b0JBQUVELE9BQU87b0JBQUtDLE9BQU87Z0JBQUs7YUFDM0I7UUFDSDtRQUNBRSxVQUFVO1lBQ1JYLE1BQU07WUFDTkcsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFNBQVM7UUFDWDtRQUNBTyxXQUFXO1lBQ1RaLE1BQU07WUFDTkcsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFNBQVM7UUFDWDtJQUNGO0lBQ0FRLFVBQVU7UUFBQztLQUFPO0FBQ3BCLEVBQUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvY29tcG9uZW50cy9iYXNpYy9CdXR0b24vc2NoZW1hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENvbXBvbmVudFNjaGVtYSB9IGZyb20gJy4uLy4uLy4uL3R5cGVzL2NvbXBvbmVudCc7XG5cbi8qKlxuICogQnV0dG9u57uE5Lu255qE6YWN572uU2NoZW1hXG4gKi9cbmV4cG9ydCBjb25zdCBidXR0b25TY2hlbWE6IENvbXBvbmVudFNjaGVtYSA9IHtcbiAgdHlwZTogJ29iamVjdCcsXG4gIHByb3BlcnRpZXM6IHtcbiAgICB0ZXh0OiB7XG4gICAgICB0eXBlOiAnc3RyaW5nJyxcbiAgICAgIHRpdGxlOiAn5oyJ6ZKu5paH5pysJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn5pi+56S65Zyo5oyJ6ZKu5LiK55qE5paH5pys5YaF5a65JyxcbiAgICAgIGRlZmF1bHQ6ICfmjInpkq4nLFxuICAgIH0sXG4gICAgdmFyaWFudDoge1xuICAgICAgdHlwZTogJ3NlbGVjdCcsXG4gICAgICB0aXRsZTogJ+aMiemSruagt+W8jycsXG4gICAgICBkZXNjcmlwdGlvbjogJ+mAieaLqeaMiemSrueahOinhuinieagt+W8jycsXG4gICAgICBkZWZhdWx0OiAnZGVmYXVsdCcsXG4gICAgICBlbnVtOiBbXG4gICAgICAgIHsgbGFiZWw6ICfpu5jorqQnLCB2YWx1ZTogJ2RlZmF1bHQnIH0sXG4gICAgICAgIHsgbGFiZWw6ICfljbHpmaknLCB2YWx1ZTogJ2Rlc3RydWN0aXZlJyB9LFxuICAgICAgICB7IGxhYmVsOiAn6L655qGGJywgdmFsdWU6ICdvdXRsaW5lJyB9LFxuICAgICAgICB7IGxhYmVsOiAn5qyh6KaBJywgdmFsdWU6ICdzZWNvbmRhcnknIH0sXG4gICAgICAgIHsgbGFiZWw6ICflub3ngbUnLCB2YWx1ZTogJ2dob3N0JyB9LFxuICAgICAgICB7IGxhYmVsOiAn6ZO+5o6lJywgdmFsdWU6ICdsaW5rJyB9LFxuICAgICAgXSxcbiAgICB9LFxuICAgIHNpemU6IHtcbiAgICAgIHR5cGU6ICdzZWxlY3QnLFxuICAgICAgdGl0bGU6ICfmjInpkq7lpKflsI8nLFxuICAgICAgZGVzY3JpcHRpb246ICfpgInmi6nmjInpkq7nmoTlsLrlr7gnLFxuICAgICAgZGVmYXVsdDogJ2RlZmF1bHQnLFxuICAgICAgZW51bTogW1xuICAgICAgICB7IGxhYmVsOiAn5bCPJywgdmFsdWU6ICdzbScgfSxcbiAgICAgICAgeyBsYWJlbDogJ+m7mOiupCcsIHZhbHVlOiAnZGVmYXVsdCcgfSxcbiAgICAgICAgeyBsYWJlbDogJ+WkpycsIHZhbHVlOiAnbGcnIH0sXG4gICAgICBdLFxuICAgIH0sXG4gICAgZGlzYWJsZWQ6IHtcbiAgICAgIHR5cGU6ICdib29sZWFuJyxcbiAgICAgIHRpdGxlOiAn56aB55So54q25oCBJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn5piv5ZCm56aB55So5oyJ6ZKuJyxcbiAgICAgIGRlZmF1bHQ6IGZhbHNlLFxuICAgIH0sXG4gICAgY2xhc3NOYW1lOiB7XG4gICAgICB0eXBlOiAnc3RyaW5nJyxcbiAgICAgIHRpdGxlOiAn6Ieq5a6a5LmJ5qC35byP57G75ZCNJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn5re75Yqg6Ieq5a6a5LmJ55qEQ1NT57G75ZCNJyxcbiAgICAgIGRlZmF1bHQ6ICcnLFxuICAgIH0sXG4gIH0sXG4gIHJlcXVpcmVkOiBbJ3RleHQnXSxcbn07XG4iXSwibmFtZXMiOlsiYnV0dG9uU2NoZW1hIiwidHlwZSIsInByb3BlcnRpZXMiLCJ0ZXh0IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImRlZmF1bHQiLCJ2YXJpYW50IiwiZW51bSIsImxhYmVsIiwidmFsdWUiLCJzaXplIiwiZGlzYWJsZWQiLCJjbGFzc05hbWUiLCJyZXF1aXJlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Button/schema.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Button/settings.tsx":
/*!***********************************************************************!*\
  !*** ./src/components/materials/components/basic/Button/settings.tsx ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonSettings: () => (/* binding */ ButtonSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./src/components/ui/select.tsx\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ ButtonSettings auto */ \n\n\n\n\nconst ButtonSettings = ()=>{\n    const { actions: { setProp }, props } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_3__.useNode)({\n        \"ButtonSettings.useNode\": (node)=>({\n                props: node.data.props\n            })\n    }[\"ButtonSettings.useNode\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"按钮设置\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"配置按钮的属性和样式\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: [\n                                    \"按钮文本 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500\",\n                                        children: \"*\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 18\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.text = e.target.value;\n                                    }),\n                                placeholder: \"请输入按钮文本\",\n                                value: props.text || ''\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"按钮样式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                onValueChange: (value)=>setProp((props)=>{\n                                        props.variant = value;\n                                    }),\n                                value: props.variant || 'default',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"选择按钮样式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"default\",\n                                                children: \"默认\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"destructive\",\n                                                children: \"危险\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"outline\",\n                                                children: \"边框\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"secondary\",\n                                                children: \"次要\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"ghost\",\n                                                children: \"幽灵\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"link\",\n                                                children: \"链接\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"按钮大小\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                onValueChange: (value)=>setProp((props)=>{\n                                        props.size = value;\n                                    }),\n                                value: props.size || 'default',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"选择按钮大小\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"sm\",\n                                                children: \"小\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"default\",\n                                                children: \"默认\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"lg\",\n                                                children: \"大\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    checked: props.disabled || false,\n                                    className: \"rounded border-gray-300\",\n                                    onChange: (e)=>setProp((props)=>{\n                                            props.disabled = e.target.checked;\n                                        }),\n                                    type: \"checkbox\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"禁用按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"自定义样式类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.className = e.target.value;\n                                    }),\n                                placeholder: \"例如: my-custom-button-class\",\n                                value: props.className || ''\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"可以添加自定义的CSS类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Button/settings.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Button/settings.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Text/index.tsx":
/*!******************************************************************!*\
  !*** ./src/components/materials/components/basic/Text/index.tsx ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaterialText: () => (/* binding */ MaterialText),\n/* harmony export */   TextPreview: () => (/* binding */ TextPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ MaterialText,TextPreview auto */ \n\n\n/**\n * 用于编辑器的Text组件\n */ const MaterialText = ({ text = '文本内容', fontSize = 16, fontWeight = '400', color = '#000000', textAlign = 'left', className = '' })=>{\n    const { connectors: { connect, drag }, selected } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_1__.useNode)({\n        \"MaterialText.useNode\": (state)=>({\n                selected: state.events.selected\n            })\n    }[\"MaterialText.useNode\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: ` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} cursor-move transition-all duration-200`,\n        ref: (ref)=>{\n            if (ref) {\n                connect(drag(ref));\n            }\n            return undefined;\n        },\n        style: {\n            fontSize: `${fontSize}px`,\n            fontWeight,\n            color,\n            textAlign\n        },\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/index.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n/**\n * 用于用户网站的纯Text组件（预览组件）\n */ const TextPreview = ({ text = '文本内容', fontSize = 16, fontWeight = '400', color = '#000000', textAlign = 'left', className = '' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        style: {\n            fontSize: `${fontSize}px`,\n            fontWeight,\n            color,\n            textAlign\n        },\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/index.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n// CraftJS配置\nMaterialText.craft = {\n    props: {\n        text: '文本内容',\n        fontSize: 16,\n        fontWeight: '400',\n        color: '#000000',\n        textAlign: 'left',\n        className: ''\n    },\n    related: {\n        settings: ()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./settings */ \"(ssr)/./src/components/materials/components/basic/Text/settings.tsx\")).then((comp)=>comp.TextSettings)\n    },\n    rules: {\n        canDrag: true,\n        canDrop: false,\n        canMoveIn: false,\n        canMoveOut: true\n    },\n    custom: {\n        displayName: '文本'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Text/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Text/schema.ts":
/*!******************************************************************!*\
  !*** ./src/components/materials/components/basic/Text/schema.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textSchema: () => (/* binding */ textSchema)\n/* harmony export */ });\n/**\n * Text组件的配置Schema\n */ const textSchema = {\n    type: 'object',\n    properties: {\n        text: {\n            type: 'string',\n            title: '文本内容',\n            description: '显示的文本内容',\n            default: '文本内容'\n        },\n        fontSize: {\n            type: 'number',\n            title: '字体大小',\n            description: '字体大小，单位：像素',\n            default: 16,\n            min: 8,\n            max: 72\n        },\n        fontWeight: {\n            type: 'select',\n            title: '字体粗细',\n            description: '选择字体的粗细程度',\n            default: '400',\n            enum: [\n                {\n                    label: '细',\n                    value: '300'\n                },\n                {\n                    label: '普通',\n                    value: '400'\n                },\n                {\n                    label: '中等',\n                    value: '500'\n                },\n                {\n                    label: '半粗',\n                    value: '600'\n                },\n                {\n                    label: '粗',\n                    value: '700'\n                },\n                {\n                    label: '特粗',\n                    value: '800'\n                }\n            ]\n        },\n        color: {\n            type: 'color',\n            title: '文本颜色',\n            description: '文本的颜色',\n            default: '#000000'\n        },\n        textAlign: {\n            type: 'select',\n            title: '文本对齐',\n            description: '文本的对齐方式',\n            default: 'left',\n            enum: [\n                {\n                    label: '左对齐',\n                    value: 'left'\n                },\n                {\n                    label: '居中',\n                    value: 'center'\n                },\n                {\n                    label: '右对齐',\n                    value: 'right'\n                }\n            ]\n        },\n        className: {\n            type: 'string',\n            title: '自定义样式类名',\n            description: '添加自定义的CSS类名',\n            default: ''\n        }\n    },\n    required: [\n        'text'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Text/schema.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/basic/Text/settings.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/materials/components/basic/Text/settings.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextSettings: () => (/* binding */ TextSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./src/components/ui/select.tsx\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ TextSettings auto */ \n\n\n\n\nconst TextSettings = ()=>{\n    const { actions: { setProp }, props } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_3__.useNode)({\n        \"TextSettings.useNode\": (node)=>({\n                props: node.data.props\n            })\n    }[\"TextSettings.useNode\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"文本设置\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"配置文本的内容和样式\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: [\n                                    \"文本内容 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500\",\n                                        children: \"*\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 18\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.text = e.target.value;\n                                    }),\n                                placeholder: \"请输入文本内容\",\n                                value: props.text || ''\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"字体大小 (px)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                max: 72,\n                                min: 8,\n                                onChange: (e)=>setProp((props)=>{\n                                        props.fontSize = Number(e.target.value);\n                                    }),\n                                type: \"number\",\n                                value: props.fontSize || 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"字体粗细\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                onValueChange: (value)=>setProp((props)=>{\n                                        props.fontWeight = value;\n                                    }),\n                                value: props.fontWeight || '400',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"选择字体粗细\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"300\",\n                                                children: \"细\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"400\",\n                                                children: \"普通\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"500\",\n                                                children: \"中等\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"600\",\n                                                children: \"半粗\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"700\",\n                                                children: \"粗\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"800\",\n                                                children: \"特粗\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"文本颜色\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        className: \"h-8 w-8 rounded border\",\n                                        onChange: (e)=>setProp((props)=>{\n                                                props.color = e.target.value;\n                                            }),\n                                        type: \"color\",\n                                        value: props.color || '#000000'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        onChange: (e)=>setProp((props)=>{\n                                                props.color = e.target.value;\n                                            }),\n                                        placeholder: \"#000000\",\n                                        value: props.color || '#000000'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"文本对齐\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                onValueChange: (value)=>setProp((props)=>{\n                                        props.textAlign = value;\n                                    }),\n                                value: props.textAlign || 'left',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"选择对齐方式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"left\",\n                                                children: \"左对齐\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"center\",\n                                                children: \"居中\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"right\",\n                                                children: \"右对齐\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"自定义样式类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.className = e.target.value;\n                                    }),\n                                placeholder: \"例如: my-custom-text-class\",\n                                value: props.className || ''\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"可以添加自定义的CSS类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/basic/Text/settings.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvY29tcG9uZW50cy9iYXNpYy9UZXh0L3NldHRpbmdzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRThDO0FBT2Q7QUFDUTtBQUNkO0FBSW5CLE1BQU1RLGVBQXlCO0lBQ3BDLE1BQU0sRUFDSkMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFDcEJDLEtBQUssRUFDTixHQUFHTCxzREFBT0E7Z0NBQUMsQ0FBQ00sT0FBVTtnQkFDckJELE9BQU9DLEtBQUtDLElBQUksQ0FBQ0YsS0FBSztZQUN4Qjs7SUFFQSxxQkFDRSw4REFBQ0c7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQW9DOzs7Ozs7a0NBQ2xELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBd0I7Ozs7Ozs7Ozs7OzswQkFHdkMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FBTUgsV0FBVTs7b0NBQTBDO2tEQUNwRCw4REFBQ0k7d0NBQUtKLFdBQVU7a0RBQWU7Ozs7Ozs7Ozs7OzswQ0FFdEMsOERBQUNmLHVEQUFLQTtnQ0FDSm9CLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQzt3Q0FDUEEsTUFBTVcsSUFBSSxHQUFHRCxFQUFFRSxNQUFNLENBQUNDLEtBQUs7b0NBQzdCO2dDQUVGQyxhQUFZO2dDQUNaRCxPQUFPYixNQUFNVyxJQUFJLElBQUk7Ozs7Ozs7Ozs7OztrQ0FLekIsOERBQUNSO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDZix1REFBS0E7Z0NBQ0owQixLQUFLO2dDQUNMQyxLQUFLO2dDQUNMUCxVQUFVLENBQUNDLElBQ1RYLFFBQVEsQ0FBQ0M7d0NBQ1BBLE1BQU1pQixRQUFRLEdBQUdDLE9BQU9SLEVBQUVFLE1BQU0sQ0FBQ0MsS0FBSztvQ0FDeEM7Z0NBRUZNLE1BQUs7Z0NBQ0xOLE9BQU9iLE1BQU1pQixRQUFRLElBQUk7Ozs7Ozs7Ozs7OztrQ0FLN0IsOERBQUNkO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDZCx5REFBTUE7Z0NBQ0w4QixlQUFlLENBQUNQLFFBQ2RkLFFBQVEsQ0FBQ0M7d0NBQ1BBLE1BQU1xQixVQUFVLEdBQUdSO29DQUNyQjtnQ0FFRkEsT0FBT2IsTUFBTXFCLFVBQVUsSUFBSTs7a0RBRTNCLDhEQUFDNUIsZ0VBQWFBO2tEQUNaLDRFQUFDQyw4REFBV0E7NENBQUNvQixhQUFZOzs7Ozs7Ozs7OztrREFFM0IsOERBQUN2QixnRUFBYUE7OzBEQUNaLDhEQUFDQyw2REFBVUE7Z0RBQUNxQixPQUFNOzBEQUFNOzs7Ozs7MERBQ3hCLDhEQUFDckIsNkRBQVVBO2dEQUFDcUIsT0FBTTswREFBTTs7Ozs7OzBEQUN4Qiw4REFBQ3JCLDZEQUFVQTtnREFBQ3FCLE9BQU07MERBQU07Ozs7OzswREFDeEIsOERBQUNyQiw2REFBVUE7Z0RBQUNxQixPQUFNOzBEQUFNOzs7Ozs7MERBQ3hCLDhEQUFDckIsNkRBQVVBO2dEQUFDcUIsT0FBTTswREFBTTs7Ozs7OzBEQUN4Qiw4REFBQ3JCLDZEQUFVQTtnREFBQ3FCLE9BQU07MERBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNOUIsOERBQUNWO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNrQjt3Q0FDQ2xCLFdBQVU7d0NBQ1ZLLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQztnREFDUEEsTUFBTXVCLEtBQUssR0FBR2IsRUFBRUUsTUFBTSxDQUFDQyxLQUFLOzRDQUM5Qjt3Q0FFRk0sTUFBSzt3Q0FDTE4sT0FBT2IsTUFBTXVCLEtBQUssSUFBSTs7Ozs7O2tEQUV4Qiw4REFBQ2xDLHVEQUFLQTt3Q0FDSm9CLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQztnREFDUEEsTUFBTXVCLEtBQUssR0FBR2IsRUFBRUUsTUFBTSxDQUFDQyxLQUFLOzRDQUM5Qjt3Q0FFRkMsYUFBWTt3Q0FDWkQsT0FBT2IsTUFBTXVCLEtBQUssSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU01Qiw4REFBQ3BCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDZCx5REFBTUE7Z0NBQ0w4QixlQUFlLENBQUNQLFFBQ2RkLFFBQVEsQ0FBQ0M7d0NBQ1BBLE1BQU13QixTQUFTLEdBQUdYO29DQUNwQjtnQ0FFRkEsT0FBT2IsTUFBTXdCLFNBQVMsSUFBSTs7a0RBRTFCLDhEQUFDL0IsZ0VBQWFBO2tEQUNaLDRFQUFDQyw4REFBV0E7NENBQUNvQixhQUFZOzs7Ozs7Ozs7OztrREFFM0IsOERBQUN2QixnRUFBYUE7OzBEQUNaLDhEQUFDQyw2REFBVUE7Z0RBQUNxQixPQUFNOzBEQUFPOzs7Ozs7MERBQ3pCLDhEQUFDckIsNkRBQVVBO2dEQUFDcUIsT0FBTTswREFBUzs7Ozs7OzBEQUMzQiw4REFBQ3JCLDZEQUFVQTtnREFBQ3FCLE9BQU07MERBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNaEMsOERBQUNWO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDZix1REFBS0E7Z0NBQ0pvQixVQUFVLENBQUNDLElBQ1RYLFFBQVEsQ0FBQ0M7d0NBQ1BBLE1BQU1JLFNBQVMsR0FBR00sRUFBRUUsTUFBTSxDQUFDQyxLQUFLO29DQUNsQztnQ0FFRkMsYUFBWTtnQ0FDWkQsT0FBT2IsTUFBTUksU0FBUyxJQUFJOzs7Ozs7MENBRTVCLDhEQUFDRTtnQ0FBRUYsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsvQyxFQUFFIiwic291cmNlcyI6WyIvVXNlcnMveWFuZ3dlbnFpYW5nL2Rldi9haS9GbGV4aUh1Yi92dWUtdmJlbi1hZG1pbi9hcHBzL3dlYnNpdGUvc3JjL2NvbXBvbmVudHMvbWF0ZXJpYWxzL2NvbXBvbmVudHMvYmFzaWMvVGV4dC9zZXR0aW5ncy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XG5pbXBvcnQge1xuICBTZWxlY3QsXG4gIFNlbGVjdENvbnRlbnQsXG4gIFNlbGVjdEl0ZW0sXG4gIFNlbGVjdFRyaWdnZXIsXG4gIFNlbGVjdFZhbHVlLFxufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VsZWN0JztcbmltcG9ydCB7IHVzZU5vZGUgfSBmcm9tICdAY3JhZnRqcy9jb3JlJztcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCB7IFRleHRQcm9wcyB9IGZyb20gJy4vaW5kZXgnO1xuXG5leHBvcnQgY29uc3QgVGV4dFNldHRpbmdzOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3Qge1xuICAgIGFjdGlvbnM6IHsgc2V0UHJvcCB9LFxuICAgIHByb3BzLFxuICB9ID0gdXNlTm9kZSgobm9kZSkgPT4gKHtcbiAgICBwcm9wczogbm9kZS5kYXRhLnByb3BzIGFzIFRleHRQcm9wcyxcbiAgfSkpO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgcC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBwYi0yXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj7mlofmnKzorr7nva48L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7phY3nva7mlofmnKznmoTlhoXlrrnlkozmoLflvI88L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgey8qIOaWh+acrOWGheWuuSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDmlofmnKzlhoXlrrkgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogVGV4dFByb3BzKSA9PiB7XG4gICAgICAgICAgICAgICAgcHJvcHMudGV4dCA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXmlofmnKzlhoXlrrlcIlxuICAgICAgICAgICAgdmFsdWU9e3Byb3BzLnRleHQgfHwgJyd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWtl+S9k+Wkp+WwjyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDlrZfkvZPlpKflsI8gKHB4KVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBtYXg9ezcyfVxuICAgICAgICAgICAgbWluPXs4fVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogVGV4dFByb3BzKSA9PiB7XG4gICAgICAgICAgICAgICAgcHJvcHMuZm9udFNpemUgPSBOdW1iZXIoZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICB2YWx1ZT17cHJvcHMuZm9udFNpemUgfHwgMTZ9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWtl+S9k+eyl+e7hiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDlrZfkvZPnspfnu4ZcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT5cbiAgICAgICAgICAgICAgc2V0UHJvcCgocHJvcHM6IFRleHRQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgIHByb3BzLmZvbnRXZWlnaHQgPSB2YWx1ZTtcbiAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5mb250V2VpZ2h0IHx8ICc0MDAnfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCLpgInmi6nlrZfkvZPnspfnu4ZcIiAvPlxuICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiMzAwXCI+57uGPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIjQwMFwiPuaZrumAmjwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCI1MDBcIj7kuK3nrYk8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiNjAwXCI+5Y2K57KXPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIjcwMFwiPueylzwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCI4MDBcIj7nibnnspc8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDmlofmnKzpopzoibIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAg5paH5pys6aKc6ImyXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZCBib3JkZXJcIlxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgc2V0UHJvcCgocHJvcHM6IFRleHRQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgICAgcHJvcHMuY29sb3IgPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIHR5cGU9XCJjb2xvclwiXG4gICAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5jb2xvciB8fCAnIzAwMDAwMCd9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogVGV4dFByb3BzKSA9PiB7XG4gICAgICAgICAgICAgICAgICBwcm9wcy5jb2xvciA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIjMDAwMDAwXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3Byb3BzLmNvbG9yIHx8ICcjMDAwMDAwJ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDmlofmnKzlr7npvZAgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAg5paH5pys5a+56b2QXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+XG4gICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBUZXh0UHJvcHMpID0+IHtcbiAgICAgICAgICAgICAgICBwcm9wcy50ZXh0QWxpZ24gPSB2YWx1ZSBhcyBUZXh0UHJvcHNbJ3RleHRBbGlnbiddO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFsdWU9e3Byb3BzLnRleHRBbGlnbiB8fCAnbGVmdCd9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIumAieaLqeWvuem9kOaWueW8j1wiIC8+XG4gICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJsZWZ0XCI+5bem5a+56b2QPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImNlbnRlclwiPuWxheS4rTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJyaWdodFwiPuWPs+Wvuem9kDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOiHquWumuS5iUNTU+exu+WQjSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDoh6rlrprkuYnmoLflvI/nsbvlkI1cbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogVGV4dFByb3BzKSA9PiB7XG4gICAgICAgICAgICAgICAgcHJvcHMuY2xhc3NOYW1lID0gZS50YXJnZXQudmFsdWU7XG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuS+i+WmgjogbXktY3VzdG9tLXRleHQtY2xhc3NcIlxuICAgICAgICAgICAgdmFsdWU9e3Byb3BzLmNsYXNzTmFtZSB8fCAnJ31cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPuWPr+S7pea3u+WKoOiHquWumuS5ieeahENTU+exu+WQjTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiSW5wdXQiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsInVzZU5vZGUiLCJSZWFjdCIsIlRleHRTZXR0aW5ncyIsImFjdGlvbnMiLCJzZXRQcm9wIiwicHJvcHMiLCJub2RlIiwiZGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicCIsImxhYmVsIiwic3BhbiIsIm9uQ2hhbmdlIiwiZSIsInRleHQiLCJ0YXJnZXQiLCJ2YWx1ZSIsInBsYWNlaG9sZGVyIiwibWF4IiwibWluIiwiZm9udFNpemUiLCJOdW1iZXIiLCJ0eXBlIiwib25WYWx1ZUNoYW5nZSIsImZvbnRXZWlnaHQiLCJpbnB1dCIsImNvbG9yIiwidGV4dEFsaWduIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/basic/Text/settings.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/layout/Container/index.tsx":
/*!************************************************************************!*\
  !*** ./src/components/materials/components/layout/Container/index.tsx ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContainerPreview: () => (/* binding */ ContainerPreview),\n/* harmony export */   MaterialContainer: () => (/* binding */ MaterialContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ MaterialContainer,ContainerPreview auto */ \n\n\n/**\n * 用于编辑器的Container组件\n */ const MaterialContainer = ({ background = '#ffffff', padding = 20, margin = 0, borderRadius = 0, border = 'none', minHeight = 100, className = '', children })=>{\n    const { connectors: { connect, drag }, selected, isHover } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_1__.useNode)({\n        \"MaterialContainer.useNode\": (state)=>({\n                selected: state.events.selected,\n                isHover: state.events.hovered\n            })\n    }[\"MaterialContainer.useNode\"]);\n    const { enabled } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_1__.useEditor)({\n        \"MaterialContainer.useEditor\": (state)=>({\n                enabled: state.options.enabled\n            })\n    }[\"MaterialContainer.useEditor\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: ` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} ${isHover ? 'ring-1 ring-blue-300' : ''} ${enabled ? 'cursor-move' : ''} relative transition-all duration-200`,\n        ref: (ref)=>{\n            if (ref) {\n                connect(drag(ref));\n            }\n            return undefined;\n        },\n        style: {\n            background,\n            padding: `${padding}px`,\n            margin: `${margin}px`,\n            borderRadius: `${borderRadius}px`,\n            border,\n            minHeight: `${minHeight}px`\n        },\n        children: [\n            enabled && (selected || isHover) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-0 -mt-6 rounded bg-blue-500 px-2 py-1 text-xs text-white\",\n                children: \"容器\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/index.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/index.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n/**\n * 用于用户网站的纯Container组件（预览组件）\n */ const ContainerPreview = ({ background = '#ffffff', padding = 20, margin = 0, borderRadius = 0, border = 'none', minHeight = 100, className = '', children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            background,\n            padding: `${padding}px`,\n            margin: `${margin}px`,\n            borderRadius: `${borderRadius}px`,\n            border,\n            minHeight: `${minHeight}px`\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/index.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n// CraftJS配置\nMaterialContainer.craft = {\n    props: {\n        background: '#ffffff',\n        padding: 20,\n        margin: 0,\n        borderRadius: 0,\n        border: 'none',\n        minHeight: 100,\n        className: ''\n    },\n    related: {\n        settings: ()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./settings */ \"(ssr)/./src/components/materials/components/layout/Container/settings.tsx\")).then((comp)=>comp.ContainerSettings)\n    },\n    rules: {\n        canDrag: true,\n        canDrop: ()=>true,\n        canMoveIn: ()=>true,\n        canMoveOut: true\n    },\n    custom: {\n        displayName: '容器'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/layout/Container/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/layout/Container/schema.ts":
/*!************************************************************************!*\
  !*** ./src/components/materials/components/layout/Container/schema.ts ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerSchema: () => (/* binding */ containerSchema)\n/* harmony export */ });\n/**\n * Container组件的配置Schema\n */ const containerSchema = {\n    type: 'object',\n    properties: {\n        background: {\n            type: 'color',\n            title: '背景颜色',\n            description: '容器的背景颜色',\n            default: '#ffffff'\n        },\n        padding: {\n            type: 'number',\n            title: '内边距',\n            description: '容器内部的间距，单位：像素',\n            default: 20,\n            min: 0,\n            max: 100\n        },\n        margin: {\n            type: 'number',\n            title: '外边距',\n            description: '容器外部的间距，单位：像素',\n            default: 0,\n            min: 0,\n            max: 100\n        },\n        borderRadius: {\n            type: 'number',\n            title: '圆角',\n            description: '容器的圆角大小，单位：像素',\n            default: 0,\n            min: 0,\n            max: 50\n        },\n        border: {\n            type: 'string',\n            title: '边框',\n            description: 'CSS边框样式，例如：1px solid #ccc',\n            default: 'none'\n        },\n        minHeight: {\n            type: 'number',\n            title: '最小高度',\n            description: '容器的最小高度，单位：像素',\n            default: 100,\n            min: 50,\n            max: 1000\n        },\n        className: {\n            type: 'string',\n            title: '自定义样式类名',\n            description: '添加自定义的CSS类名',\n            default: ''\n        }\n    },\n    required: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/layout/Container/schema.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/components/layout/Container/settings.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/materials/components/layout/Container/settings.tsx ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContainerSettings: () => (/* binding */ ContainerSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @craftjs/core */ \"@craftjs/core\");\n/* harmony import */ var _craftjs_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_craftjs_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ContainerSettings auto */ \n\n\n\nconst ContainerSettings = ()=>{\n    const { actions: { setProp }, props } = (0,_craftjs_core__WEBPACK_IMPORTED_MODULE_2__.useNode)({\n        \"ContainerSettings.useNode\": (node)=>({\n                props: node.data.props\n            })\n    }[\"ContainerSettings.useNode\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"容器设置\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"配置容器的样式和布局\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"背景颜色\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        className: \"h-8 w-8 rounded border\",\n                                        onChange: (e)=>setProp((props)=>{\n                                                props.background = e.target.value;\n                                            }),\n                                        type: \"color\",\n                                        value: props.background || '#ffffff'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        onChange: (e)=>setProp((props)=>{\n                                                props.background = e.target.value;\n                                            }),\n                                        placeholder: \"#ffffff\",\n                                        value: props.background || '#ffffff'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"内边距 (px)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                max: 100,\n                                min: 0,\n                                onChange: (e)=>setProp((props)=>{\n                                        props.padding = Number(e.target.value);\n                                    }),\n                                type: \"number\",\n                                value: props.padding || 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"外边距 (px)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                max: 100,\n                                min: 0,\n                                onChange: (e)=>setProp((props)=>{\n                                        props.margin = Number(e.target.value);\n                                    }),\n                                type: \"number\",\n                                value: props.margin || 0\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"圆角 (px)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                max: 50,\n                                min: 0,\n                                onChange: (e)=>setProp((props)=>{\n                                        props.borderRadius = Number(e.target.value);\n                                    }),\n                                type: \"number\",\n                                value: props.borderRadius || 0\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"边框\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.border = e.target.value;\n                                    }),\n                                placeholder: \"例如: 1px solid #ccc\",\n                                value: props.border || 'none'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"使用CSS边框语法，例如：1px solid #ccc\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"最小高度 (px)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                max: 1000,\n                                min: 50,\n                                onChange: (e)=>setProp((props)=>{\n                                        props.minHeight = Number(e.target.value);\n                                    }),\n                                type: \"number\",\n                                value: props.minHeight || 100\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"自定义样式类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                onChange: (e)=>setProp((props)=>{\n                                        props.className = e.target.value;\n                                    }),\n                                placeholder: \"例如: my-custom-container-class\",\n                                value: props.className || ''\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"可以添加自定义的CSS类名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/materials/components/layout/Container/settings.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvY29tcG9uZW50cy9sYXlvdXQvQ29udGFpbmVyL3NldHRpbmdzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEM7QUFDTjtBQUNkO0FBSW5CLE1BQU1HLG9CQUE4QjtJQUN6QyxNQUFNLEVBQ0pDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQ3BCQyxLQUFLLEVBQ04sR0FBR0wsc0RBQU9BO3FDQUFDLENBQUNNLE9BQVU7Z0JBQ3JCRCxPQUFPQyxLQUFLQyxJQUFJLENBQUNGLEtBQUs7WUFDeEI7O0lBRUEscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFvQzs7Ozs7O2tDQUNsRCw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7MEJBR3ZDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNJO3dDQUNDSixXQUFVO3dDQUNWSyxVQUFVLENBQUNDLElBQ1RYLFFBQVEsQ0FBQ0M7Z0RBQ1BBLE1BQU1XLFVBQVUsR0FBR0QsRUFBRUUsTUFBTSxDQUFDQyxLQUFLOzRDQUNuQzt3Q0FFRkMsTUFBSzt3Q0FDTEQsT0FBT2IsTUFBTVcsVUFBVSxJQUFJOzs7Ozs7a0RBRTdCLDhEQUFDakIsdURBQUtBO3dDQUNKZSxVQUFVLENBQUNDLElBQ1RYLFFBQVEsQ0FBQ0M7Z0RBQ1BBLE1BQU1XLFVBQVUsR0FBR0QsRUFBRUUsTUFBTSxDQUFDQyxLQUFLOzRDQUNuQzt3Q0FFRkUsYUFBWTt3Q0FDWkYsT0FBT2IsTUFBTVcsVUFBVSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWpDLDhEQUFDUjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUFNSCxXQUFVOzBDQUEwQzs7Ozs7OzBDQUczRCw4REFBQ1YsdURBQUtBO2dDQUNKc0IsS0FBSztnQ0FDTEMsS0FBSztnQ0FDTFIsVUFBVSxDQUFDQyxJQUNUWCxRQUFRLENBQUNDO3dDQUNQQSxNQUFNa0IsT0FBTyxHQUFHQyxPQUFPVCxFQUFFRSxNQUFNLENBQUNDLEtBQUs7b0NBQ3ZDO2dDQUVGQyxNQUFLO2dDQUNMRCxPQUFPYixNQUFNa0IsT0FBTyxJQUFJOzs7Ozs7Ozs7Ozs7a0NBSzVCLDhEQUFDZjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUFNSCxXQUFVOzBDQUEwQzs7Ozs7OzBDQUczRCw4REFBQ1YsdURBQUtBO2dDQUNKc0IsS0FBSztnQ0FDTEMsS0FBSztnQ0FDTFIsVUFBVSxDQUFDQyxJQUNUWCxRQUFRLENBQUNDO3dDQUNQQSxNQUFNb0IsTUFBTSxHQUFHRCxPQUFPVCxFQUFFRSxNQUFNLENBQUNDLEtBQUs7b0NBQ3RDO2dDQUVGQyxNQUFLO2dDQUNMRCxPQUFPYixNQUFNb0IsTUFBTSxJQUFJOzs7Ozs7Ozs7Ozs7a0NBSzNCLDhEQUFDakI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FBTUgsV0FBVTswQ0FBMEM7Ozs7OzswQ0FHM0QsOERBQUNWLHVEQUFLQTtnQ0FDSnNCLEtBQUs7Z0NBQ0xDLEtBQUs7Z0NBQ0xSLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQzt3Q0FDUEEsTUFBTXFCLFlBQVksR0FBR0YsT0FBT1QsRUFBRUUsTUFBTSxDQUFDQyxLQUFLO29DQUM1QztnQ0FFRkMsTUFBSztnQ0FDTEQsT0FBT2IsTUFBTXFCLFlBQVksSUFBSTs7Ozs7Ozs7Ozs7O2tDQUtqQyw4REFBQ2xCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDVix1REFBS0E7Z0NBQ0plLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQzt3Q0FDUEEsTUFBTXNCLE1BQU0sR0FBR1osRUFBRUUsTUFBTSxDQUFDQyxLQUFLO29DQUMvQjtnQ0FFRkUsYUFBWTtnQ0FDWkYsT0FBT2IsTUFBTXNCLE1BQU0sSUFBSTs7Ozs7OzBDQUV6Qiw4REFBQ2hCO2dDQUFFRixXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7O2tDQU12Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FBTUgsV0FBVTswQ0FBMEM7Ozs7OzswQ0FHM0QsOERBQUNWLHVEQUFLQTtnQ0FDSnNCLEtBQUs7Z0NBQ0xDLEtBQUs7Z0NBQ0xSLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQzt3Q0FDUEEsTUFBTXVCLFNBQVMsR0FBR0osT0FBT1QsRUFBRUUsTUFBTSxDQUFDQyxLQUFLO29DQUN6QztnQ0FFRkMsTUFBSztnQ0FDTEQsT0FBT2IsTUFBTXVCLFNBQVMsSUFBSTs7Ozs7Ozs7Ozs7O2tDQUs5Qiw4REFBQ3BCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQU1ILFdBQVU7MENBQTBDOzs7Ozs7MENBRzNELDhEQUFDVix1REFBS0E7Z0NBQ0plLFVBQVUsQ0FBQ0MsSUFDVFgsUUFBUSxDQUFDQzt3Q0FDUEEsTUFBTUksU0FBUyxHQUFHTSxFQUFFRSxNQUFNLENBQUNDLEtBQUs7b0NBQ2xDO2dDQUVGRSxhQUFZO2dDQUNaRixPQUFPYixNQUFNSSxTQUFTLElBQUk7Ozs7OzswQ0FFNUIsOERBQUNFO2dDQUFFRixXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSy9DLEVBQUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvY29tcG9uZW50cy9sYXlvdXQvQ29udGFpbmVyL3NldHRpbmdzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0JztcbmltcG9ydCB7IHVzZU5vZGUgfSBmcm9tICdAY3JhZnRqcy9jb3JlJztcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCB7IENvbnRhaW5lclByb3BzIH0gZnJvbSAnLi9pbmRleCc7XG5cbmV4cG9ydCBjb25zdCBDb250YWluZXJTZXR0aW5nczogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IHtcbiAgICBhY3Rpb25zOiB7IHNldFByb3AgfSxcbiAgICBwcm9wcyxcbiAgfSA9IHVzZU5vZGUoKG5vZGUpID0+ICh7XG4gICAgcHJvcHM6IG5vZGUuZGF0YS5wcm9wcyBhcyBDb250YWluZXJQcm9wcyxcbiAgfSkpO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgcC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBwYi0yXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj7lrrnlmajorr7nva48L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7phY3nva7lrrnlmajnmoTmoLflvI/lkozluIPlsYA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgey8qIOiDjOaZr+minOiJsiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDog4zmma/popzoibJcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCByb3VuZGVkIGJvcmRlclwiXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogQ29udGFpbmVyUHJvcHMpID0+IHtcbiAgICAgICAgICAgICAgICAgIHByb3BzLmJhY2tncm91bmQgPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIHR5cGU9XCJjb2xvclwiXG4gICAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5iYWNrZ3JvdW5kIHx8ICcjZmZmZmZmJ31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBDb250YWluZXJQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgICAgcHJvcHMuYmFja2dyb3VuZCA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIjZmZmZmZmXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3Byb3BzLmJhY2tncm91bmQgfHwgJyNmZmZmZmYnfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWGhei+uei3nSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDlhoXovrnot50gKHB4KVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBtYXg9ezEwMH1cbiAgICAgICAgICAgIG1pbj17MH1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgc2V0UHJvcCgocHJvcHM6IENvbnRhaW5lclByb3BzKSA9PiB7XG4gICAgICAgICAgICAgICAgcHJvcHMucGFkZGluZyA9IE51bWJlcihlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5wYWRkaW5nIHx8IDIwfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDlpJbovrnot50gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAg5aSW6L656LedIChweClcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgbWF4PXsxMDB9XG4gICAgICAgICAgICBtaW49ezB9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBDb250YWluZXJQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgIHByb3BzLm1hcmdpbiA9IE51bWJlcihlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5tYXJnaW4gfHwgMH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5ZyG6KeSICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgIOWchuinkiAocHgpXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIG1heD17NTB9XG4gICAgICAgICAgICBtaW49ezB9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBDb250YWluZXJQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgIHByb3BzLmJvcmRlclJhZGl1cyA9IE51bWJlcihlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5ib3JkZXJSYWRpdXMgfHwgMH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog6L655qGGICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgIOi+ueahhlxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBDb250YWluZXJQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgIHByb3BzLmJvcmRlciA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLkvovlpoI6IDFweCBzb2xpZCAjY2NjXCJcbiAgICAgICAgICAgIHZhbHVlPXtwcm9wcy5ib3JkZXIgfHwgJ25vbmUnfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICDkvb/nlKhDU1PovrnmoYbor63ms5XvvIzkvovlpoLvvJoxcHggc29saWQgI2NjY1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOacgOWwj+mrmOW6piAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDmnIDlsI/pq5jluqYgKHB4KVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBtYXg9ezEwMDB9XG4gICAgICAgICAgICBtaW49ezUwfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICBzZXRQcm9wKChwcm9wczogQ29udGFpbmVyUHJvcHMpID0+IHtcbiAgICAgICAgICAgICAgICBwcm9wcy5taW5IZWlnaHQgPSBOdW1iZXIoZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICB2YWx1ZT17cHJvcHMubWluSGVpZ2h0IHx8IDEwMH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog6Ieq5a6a5LmJQ1NT57G75ZCNICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgIOiHquWumuS5ieagt+W8j+exu+WQjVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgIHNldFByb3AoKHByb3BzOiBDb250YWluZXJQcm9wcykgPT4ge1xuICAgICAgICAgICAgICAgIHByb3BzLmNsYXNzTmFtZSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLkvovlpoI6IG15LWN1c3RvbS1jb250YWluZXItY2xhc3NcIlxuICAgICAgICAgICAgdmFsdWU9e3Byb3BzLmNsYXNzTmFtZSB8fCAnJ31cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPuWPr+S7pea3u+WKoOiHquWumuS5ieeahENTU+exu+WQjTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiSW5wdXQiLCJ1c2VOb2RlIiwiUmVhY3QiLCJDb250YWluZXJTZXR0aW5ncyIsImFjdGlvbnMiLCJzZXRQcm9wIiwicHJvcHMiLCJub2RlIiwiZGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicCIsImxhYmVsIiwiaW5wdXQiLCJvbkNoYW5nZSIsImUiLCJiYWNrZ3JvdW5kIiwidGFyZ2V0IiwidmFsdWUiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJtYXgiLCJtaW4iLCJwYWRkaW5nIiwiTnVtYmVyIiwibWFyZ2luIiwiYm9yZGVyUmFkaXVzIiwiYm9yZGVyIiwibWluSGVpZ2h0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/components/layout/Container/settings.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/registry/basic.ts":
/*!****************************************************!*\
  !*** ./src/components/materials/registry/basic.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicComponents: () => (/* binding */ basicComponents),\n/* harmony export */   buttonMaterial: () => (/* binding */ buttonMaterial),\n/* harmony export */   textMaterial: () => (/* binding */ textMaterial)\n/* harmony export */ });\n/* harmony import */ var _components_basic_Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/basic/Button */ \"(ssr)/./src/components/materials/components/basic/Button/index.tsx\");\n/* harmony import */ var _components_basic_Button_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/basic/Button/schema */ \"(ssr)/./src/components/materials/components/basic/Button/schema.ts\");\n/* harmony import */ var _components_basic_Button_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/basic/Button/settings */ \"(ssr)/./src/components/materials/components/basic/Button/settings.tsx\");\n/* harmony import */ var _components_basic_Text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/basic/Text */ \"(ssr)/./src/components/materials/components/basic/Text/index.tsx\");\n/* harmony import */ var _components_basic_Text_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/basic/Text/schema */ \"(ssr)/./src/components/materials/components/basic/Text/schema.ts\");\n/* harmony import */ var _components_basic_Text_settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/basic/Text/settings */ \"(ssr)/./src/components/materials/components/basic/Text/settings.tsx\");\n\n\n\n\n\n\n/**\n * Button物料组件定义\n */ const buttonMaterial = {\n    id: 'Button',\n    name: '按钮',\n    category: 'basic',\n    icon: '🔘',\n    description: '可点击的按钮组件，支持多种样式和尺寸',\n    version: '1.0.0',\n    component: _components_basic_Button__WEBPACK_IMPORTED_MODULE_0__.MaterialButton,\n    preview: _components_basic_Button__WEBPACK_IMPORTED_MODULE_0__.ButtonPreview,\n    schema: _components_basic_Button_schema__WEBPACK_IMPORTED_MODULE_1__.buttonSchema,\n    craft: {\n        props: {\n            text: '按钮',\n            variant: 'default',\n            size: 'default',\n            disabled: false,\n            className: ''\n        },\n        related: {\n            settings: _components_basic_Button_settings__WEBPACK_IMPORTED_MODULE_2__.ButtonSettings\n        },\n        rules: {\n            canDrag: true,\n            canDrop: false,\n            canMoveIn: false,\n            canMoveOut: true\n        }\n    }\n};\n/**\n * Text物料组件定义\n */ const textMaterial = {\n    id: 'Text',\n    name: '文本',\n    category: 'basic',\n    icon: '📝',\n    description: '可编辑的文本组件，支持字体、颜色、对齐等样式设置',\n    version: '1.0.0',\n    component: _components_basic_Text__WEBPACK_IMPORTED_MODULE_3__.MaterialText,\n    preview: _components_basic_Text__WEBPACK_IMPORTED_MODULE_3__.TextPreview,\n    schema: _components_basic_Text_schema__WEBPACK_IMPORTED_MODULE_4__.textSchema,\n    craft: {\n        props: {\n            text: '文本内容',\n            fontSize: 16,\n            fontWeight: '400',\n            color: '#000000',\n            textAlign: 'left',\n            className: ''\n        },\n        related: {\n            settings: _components_basic_Text_settings__WEBPACK_IMPORTED_MODULE_5__.TextSettings\n        },\n        rules: {\n            canDrag: true,\n            canDrop: false,\n            canMoveIn: false,\n            canMoveOut: true\n        }\n    }\n};\n/**\n * 所有基础组件\n */ const basicComponents = [\n    buttonMaterial,\n    textMaterial\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9tYXRlcmlhbHMvcmVnaXN0cnkvYmFzaWMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTJFO0FBQ1Y7QUFDSTtBQUNBO0FBQ1I7QUFDSTtBQUdqRTs7Q0FFQyxHQUNNLE1BQU1RLGlCQUFvQztJQUMvQ0MsSUFBSTtJQUNKQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsTUFBTTtJQUNOQyxhQUFhO0lBQ2JDLFNBQVM7SUFDVEMsV0FBV2Qsb0VBQWNBO0lBQ3pCZSxTQUFTaEIsbUVBQWFBO0lBQ3RCaUIsUUFBUWYseUVBQVlBO0lBQ3BCZ0IsT0FBTztRQUNMQyxPQUFPO1lBQ0xDLE1BQU07WUFDTkMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsV0FBVztRQUNiO1FBQ0FDLFNBQVM7WUFDUEMsVUFBVXZCLDZFQUFjQTtRQUMxQjtRQUNBd0IsT0FBTztZQUNMQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7SUFDRjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1DLGVBQWtDO0lBQzdDdkIsSUFBSTtJQUNKQyxNQUFNO0lBQ05DLFVBQVU7SUFDVkMsTUFBTTtJQUNOQyxhQUFhO0lBQ2JDLFNBQVM7SUFDVEMsV0FBV1gsZ0VBQVlBO0lBQ3ZCWSxTQUFTWCwrREFBV0E7SUFDcEJZLFFBQVFYLHFFQUFVQTtJQUNsQlksT0FBTztRQUNMQyxPQUFPO1lBQ0xDLE1BQU07WUFDTmEsVUFBVTtZQUNWQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsV0FBVztZQUNYWixXQUFXO1FBQ2I7UUFDQUMsU0FBUztZQUNQQyxVQUFVbkIseUVBQVlBO1FBQ3hCO1FBQ0FvQixPQUFPO1lBQ0xDLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLFlBQVk7UUFDZDtJQUNGO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTU0sa0JBQXVDO0lBQ2xEN0I7SUFDQXdCO0NBRUQsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3lhbmd3ZW5xaWFuZy9kZXYvYWkvRmxleGlIdWIvdnVlLXZiZW4tYWRtaW4vYXBwcy93ZWJzaXRlL3NyYy9jb21wb25lbnRzL21hdGVyaWFscy9yZWdpc3RyeS9iYXNpYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCdXR0b25QcmV2aWV3LCBNYXRlcmlhbEJ1dHRvbiB9IGZyb20gJy4uL2NvbXBvbmVudHMvYmFzaWMvQnV0dG9uJztcbmltcG9ydCB7IGJ1dHRvblNjaGVtYSB9IGZyb20gJy4uL2NvbXBvbmVudHMvYmFzaWMvQnV0dG9uL3NjaGVtYSc7XG5pbXBvcnQgeyBCdXR0b25TZXR0aW5ncyB9IGZyb20gJy4uL2NvbXBvbmVudHMvYmFzaWMvQnV0dG9uL3NldHRpbmdzJztcbmltcG9ydCB7IE1hdGVyaWFsVGV4dCwgVGV4dFByZXZpZXcgfSBmcm9tICcuLi9jb21wb25lbnRzL2Jhc2ljL1RleHQnO1xuaW1wb3J0IHsgdGV4dFNjaGVtYSB9IGZyb20gJy4uL2NvbXBvbmVudHMvYmFzaWMvVGV4dC9zY2hlbWEnO1xuaW1wb3J0IHsgVGV4dFNldHRpbmdzIH0gZnJvbSAnLi4vY29tcG9uZW50cy9iYXNpYy9UZXh0L3NldHRpbmdzJztcbmltcG9ydCB7IE1hdGVyaWFsQ29tcG9uZW50IH0gZnJvbSAnLi4vdHlwZXMvY29tcG9uZW50JztcblxuLyoqXG4gKiBCdXR0b27nianmlpnnu4Tku7blrprkuYlcbiAqL1xuZXhwb3J0IGNvbnN0IGJ1dHRvbk1hdGVyaWFsOiBNYXRlcmlhbENvbXBvbmVudCA9IHtcbiAgaWQ6ICdCdXR0b24nLFxuICBuYW1lOiAn5oyJ6ZKuJyxcbiAgY2F0ZWdvcnk6ICdiYXNpYycsXG4gIGljb246ICfwn5SYJyxcbiAgZGVzY3JpcHRpb246ICflj6/ngrnlh7vnmoTmjInpkq7nu4Tku7bvvIzmlK/mjIHlpJrnp43moLflvI/lkozlsLrlr7gnLFxuICB2ZXJzaW9uOiAnMS4wLjAnLFxuICBjb21wb25lbnQ6IE1hdGVyaWFsQnV0dG9uLFxuICBwcmV2aWV3OiBCdXR0b25QcmV2aWV3LFxuICBzY2hlbWE6IGJ1dHRvblNjaGVtYSxcbiAgY3JhZnQ6IHtcbiAgICBwcm9wczoge1xuICAgICAgdGV4dDogJ+aMiemSricsXG4gICAgICB2YXJpYW50OiAnZGVmYXVsdCcsXG4gICAgICBzaXplOiAnZGVmYXVsdCcsXG4gICAgICBkaXNhYmxlZDogZmFsc2UsXG4gICAgICBjbGFzc05hbWU6ICcnLFxuICAgIH0sXG4gICAgcmVsYXRlZDoge1xuICAgICAgc2V0dGluZ3M6IEJ1dHRvblNldHRpbmdzLFxuICAgIH0sXG4gICAgcnVsZXM6IHtcbiAgICAgIGNhbkRyYWc6IHRydWUsXG4gICAgICBjYW5Ecm9wOiBmYWxzZSxcbiAgICAgIGNhbk1vdmVJbjogZmFsc2UsXG4gICAgICBjYW5Nb3ZlT3V0OiB0cnVlLFxuICAgIH0sXG4gIH0sXG59O1xuXG4vKipcbiAqIFRleHTnianmlpnnu4Tku7blrprkuYlcbiAqL1xuZXhwb3J0IGNvbnN0IHRleHRNYXRlcmlhbDogTWF0ZXJpYWxDb21wb25lbnQgPSB7XG4gIGlkOiAnVGV4dCcsXG4gIG5hbWU6ICfmlofmnKwnLFxuICBjYXRlZ29yeTogJ2Jhc2ljJyxcbiAgaWNvbjogJ/Cfk50nLFxuICBkZXNjcmlwdGlvbjogJ+WPr+e8lui+keeahOaWh+acrOe7hOS7tu+8jOaUr+aMgeWtl+S9k+OAgeminOiJsuOAgeWvuem9kOetieagt+W8j+iuvue9ricsXG4gIHZlcnNpb246ICcxLjAuMCcsXG4gIGNvbXBvbmVudDogTWF0ZXJpYWxUZXh0LFxuICBwcmV2aWV3OiBUZXh0UHJldmlldyxcbiAgc2NoZW1hOiB0ZXh0U2NoZW1hLFxuICBjcmFmdDoge1xuICAgIHByb3BzOiB7XG4gICAgICB0ZXh0OiAn5paH5pys5YaF5a65JyxcbiAgICAgIGZvbnRTaXplOiAxNixcbiAgICAgIGZvbnRXZWlnaHQ6ICc0MDAnLFxuICAgICAgY29sb3I6ICcjMDAwMDAwJyxcbiAgICAgIHRleHRBbGlnbjogJ2xlZnQnLFxuICAgICAgY2xhc3NOYW1lOiAnJyxcbiAgICB9LFxuICAgIHJlbGF0ZWQ6IHtcbiAgICAgIHNldHRpbmdzOiBUZXh0U2V0dGluZ3MsXG4gICAgfSxcbiAgICBydWxlczoge1xuICAgICAgY2FuRHJhZzogdHJ1ZSxcbiAgICAgIGNhbkRyb3A6IGZhbHNlLFxuICAgICAgY2FuTW92ZUluOiBmYWxzZSxcbiAgICAgIGNhbk1vdmVPdXQ6IHRydWUsXG4gICAgfSxcbiAgfSxcbn07XG5cbi8qKlxuICog5omA5pyJ5Z+656GA57uE5Lu2XG4gKi9cbmV4cG9ydCBjb25zdCBiYXNpY0NvbXBvbmVudHM6IE1hdGVyaWFsQ29tcG9uZW50W10gPSBbXG4gIGJ1dHRvbk1hdGVyaWFsLFxuICB0ZXh0TWF0ZXJpYWwsXG4gIC8vIOWFtuS7luWfuuehgOe7hOS7tuWwhuWcqOi/memHjOa3u+WKoFxuXTtcbiJdLCJuYW1lcyI6WyJCdXR0b25QcmV2aWV3IiwiTWF0ZXJpYWxCdXR0b24iLCJidXR0b25TY2hlbWEiLCJCdXR0b25TZXR0aW5ncyIsIk1hdGVyaWFsVGV4dCIsIlRleHRQcmV2aWV3IiwidGV4dFNjaGVtYSIsIlRleHRTZXR0aW5ncyIsImJ1dHRvbk1hdGVyaWFsIiwiaWQiLCJuYW1lIiwiY2F0ZWdvcnkiLCJpY29uIiwiZGVzY3JpcHRpb24iLCJ2ZXJzaW9uIiwiY29tcG9uZW50IiwicHJldmlldyIsInNjaGVtYSIsImNyYWZ0IiwicHJvcHMiLCJ0ZXh0IiwidmFyaWFudCIsInNpemUiLCJkaXNhYmxlZCIsImNsYXNzTmFtZSIsInJlbGF0ZWQiLCJzZXR0aW5ncyIsInJ1bGVzIiwiY2FuRHJhZyIsImNhbkRyb3AiLCJjYW5Nb3ZlSW4iLCJjYW5Nb3ZlT3V0IiwidGV4dE1hdGVyaWFsIiwiZm9udFNpemUiLCJmb250V2VpZ2h0IiwiY29sb3IiLCJ0ZXh0QWxpZ24iLCJiYXNpY0NvbXBvbmVudHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/registry/basic.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/registry/index.ts":
/*!****************************************************!*\
  !*** ./src/components/materials/registry/index.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaterialRegistry: () => (/* binding */ MaterialRegistry),\n/* harmony export */   materialRegistry: () => (/* binding */ materialRegistry)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * 物料组件注册表类\n */ class MaterialRegistry {\n    constructor(config){\n        this.components = new Map();\n        this.config = {\n            autoRegister: true,\n            devMode: false,\n            validation: {\n                required: [\n                    'id',\n                    'name',\n                    'component',\n                    'schema'\n                ],\n                optional: [\n                    'icon',\n                    'description',\n                    'version',\n                    'category'\n                ]\n            },\n            ...config\n        };\n    }\n    /**\n   * 清空注册表\n   */ clear() {\n        this.components.clear();\n    }\n    /**\n   * 获取组件\n   */ get(id) {\n        return this.components.get(id);\n    }\n    /**\n   * 获取所有组件\n   */ getAll() {\n        const results = [];\n        this.components.forEach((comp)=>{\n            results.push(comp);\n        });\n        return results;\n    }\n    /**\n   * 按分类获取组件\n   */ getByCategory(category) {\n        const results = [];\n        this.components.forEach((comp)=>{\n            if (comp.category === category) {\n                results.push(comp);\n            }\n        });\n        return results;\n    }\n    /**\n   * 获取CraftJS resolver对象\n   */ getCraftResolver() {\n        const resolver = {};\n        this.components.forEach((component, id)=>{\n            resolver[id] = component.component;\n        });\n        return resolver;\n    }\n    /**\n   * 获取统计信息\n   */ getStats() {\n        const all = this.getAll();\n        const byCategory = {\n            basic: 0,\n            layout: 0,\n            advanced: 0,\n            custom: 0\n        };\n        for (const comp of all){\n            byCategory[comp.category]++;\n        }\n        return {\n            total: all.length,\n            byCategory,\n            lastUpdated: new Date()\n        };\n    }\n    /**\n   * 获取工具箱数据\n   */ getToolboxData() {\n        const categories = [\n            'basic',\n            'layout',\n            'advanced',\n            'custom'\n        ];\n        return categories.map((category)=>({\n                category,\n                items: this.getByCategory(category).map((comp)=>({\n                        id: comp.id,\n                        name: comp.name,\n                        icon: comp.icon,\n                        component: comp.component,\n                        props: comp.craft.props\n                    }))\n            }));\n    }\n    /**\n   * 注册单个组件\n   */ register(component) {\n        const validation = this.validateComponent(component);\n        if (!validation.success) {\n            return validation;\n        }\n        this.components.set(component.id, component);\n        return {\n            success: true,\n            component,\n            message: `Component \"${component.name}\" registered successfully`\n        };\n    }\n    /**\n   * 批量注册组件\n   */ registerBatch(components) {\n        const results = [];\n        for (const component of components){\n            results.push(this.register(component));\n        }\n        return results;\n    }\n    /**\n   * 搜索组件\n   */ search(query) {\n        const allComponents = [];\n        this.components.forEach((comp)=>{\n            allComponents.push(comp);\n        });\n        let results = allComponents;\n        // 按分类过滤\n        if (query.category) {\n            results = results.filter((comp)=>comp.category === query.category);\n        }\n        // 按关键字搜索\n        if (query.search) {\n            const searchLower = query.search.toLowerCase();\n            results = results.filter((comp)=>comp.name.toLowerCase().includes(searchLower) || comp.description.toLowerCase().includes(searchLower));\n        }\n        // 按版本过滤\n        if (query.version) {\n            results = results.filter((comp)=>comp.version === query.version);\n        }\n        return results;\n    }\n    /**\n   * 获取注册表大小\n   */ size() {\n        return this.components.size;\n    }\n    /**\n   * 删除组件\n   */ unregister(id) {\n        return this.components.delete(id);\n    }\n    /**\n   * 验证组件配置\n   */ validateComponent(component) {\n        const { required } = this.config.validation;\n        for (const field of required){\n            if (!component[field]) {\n                return {\n                    success: false,\n                    message: `Missing required field: ${field}`\n                };\n            }\n        }\n        // 检查ID是否已存在\n        if (this.components.has(component.id)) {\n            return {\n                success: false,\n                message: `Component with id \"${component.id}\" already exists`\n            };\n        }\n        return {\n            success: true\n        };\n    }\n}\n// 全局注册表实例\nconst materialRegistry = new MaterialRegistry({\n    devMode:  false && 0\n});\n// 导出注册表类供外部使用\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/registry/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/materials/registry/layout.ts":
/*!*****************************************************!*\
  !*** ./src/components/materials/registry/layout.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerMaterial: () => (/* binding */ containerMaterial),\n/* harmony export */   layoutComponents: () => (/* binding */ layoutComponents)\n/* harmony export */ });\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/layout/Container */ \"(ssr)/./src/components/materials/components/layout/Container/index.tsx\");\n/* harmony import */ var _components_layout_Container_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/layout/Container/schema */ \"(ssr)/./src/components/materials/components/layout/Container/schema.ts\");\n/* harmony import */ var _components_layout_Container_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/layout/Container/settings */ \"(ssr)/./src/components/materials/components/layout/Container/settings.tsx\");\n\n\n\n/**\n * Container物料组件定义\n */ const containerMaterial = {\n    id: 'Container',\n    name: '容器',\n    category: 'layout',\n    icon: '📦',\n    description: '可包含其他组件的容器，支持背景、边距、边框等样式设置',\n    version: '1.0.0',\n    component: _components_layout_Container__WEBPACK_IMPORTED_MODULE_0__.MaterialContainer,\n    preview: _components_layout_Container__WEBPACK_IMPORTED_MODULE_0__.ContainerPreview,\n    schema: _components_layout_Container_schema__WEBPACK_IMPORTED_MODULE_1__.containerSchema,\n    craft: {\n        props: {\n            background: '#ffffff',\n            padding: 20,\n            margin: 0,\n            borderRadius: 0,\n            border: 'none',\n            minHeight: 100,\n            className: ''\n        },\n        related: {\n            settings: _components_layout_Container_settings__WEBPACK_IMPORTED_MODULE_2__.ContainerSettings\n        },\n        rules: {\n            canDrag: true,\n            canDrop: true,\n            canMoveIn: true,\n            canMoveOut: true\n        }\n    }\n};\n/**\n * 所有布局组件\n */ const layoutComponents = [\n    containerMaterial\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/materials/registry/layout.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.460.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.460.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.460.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectContent,SelectGroup,SelectItem,SelectLabel,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue auto */ \n\n\n\n\nfunction Select({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"select\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\nfunction SelectGroup({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        \"data-slot\": \"select-group\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\nfunction SelectValue({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value, {\n        \"data-slot\": \"select-value\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 24,\n        columnNumber: 10\n    }, this);\n}\nfunction SelectTrigger({ className, size = \"default\", children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"select-trigger\",\n        \"data-size\": size,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"size-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectContent({ className, children, position = \"popper\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-slot\": \"select-content\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectLabel({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        \"data-slot\": \"select-label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground px-2 py-1.5 text-xs\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectItem({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"select-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute right-2 flex size-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"size-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectSeparator({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        \"data-slot\": \"select-separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectScrollUpButton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        \"data-slot\": \"select-scroll-up-button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"size-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\nfunction SelectScrollDownButton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        \"data-slot\": \"select-scroll-down-button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"size-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/ai/FlexiHub/vue-vben-admin/apps/website/src/components/ui/select.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zZWxlY3QudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRThCO0FBQzJCO0FBQ2U7QUFFeEM7QUFFaEMsU0FBU00sT0FBTyxFQUNkLEdBQUdDLE9BQytDO0lBQ2xELHFCQUFPLDhEQUFDTix3REFBb0I7UUFBQ1EsYUFBVTtRQUFVLEdBQUdGLEtBQUs7Ozs7OztBQUMzRDtBQUVBLFNBQVNHLFlBQVksRUFDbkIsR0FBR0gsT0FDZ0Q7SUFDbkQscUJBQU8sOERBQUNOLHlEQUFxQjtRQUFDUSxhQUFVO1FBQWdCLEdBQUdGLEtBQUs7Ozs7OztBQUNsRTtBQUVBLFNBQVNLLFlBQVksRUFDbkIsR0FBR0wsT0FDZ0Q7SUFDbkQscUJBQU8sOERBQUNOLHlEQUFxQjtRQUFDUSxhQUFVO1FBQWdCLEdBQUdGLEtBQUs7Ozs7OztBQUNsRTtBQUVBLFNBQVNPLGNBQWMsRUFDckJDLFNBQVMsRUFDVEMsT0FBTyxTQUFTLEVBQ2hCQyxRQUFRLEVBQ1IsR0FBR1YsT0FHSjtJQUNDLHFCQUNFLDhEQUFDTiwyREFBdUI7UUFDdEJRLGFBQVU7UUFDVlUsYUFBV0g7UUFDWEQsV0FBV1YsOENBQUVBLENBQ1gsZ3pCQUNBVTtRQUVELEdBQUdSLEtBQUs7O1lBRVJVOzBCQUNELDhEQUFDaEIsd0RBQW9CO2dCQUFDb0IsT0FBTzswQkFDM0IsNEVBQUNsQixtSEFBZUE7b0JBQUNZLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5DO0FBRUEsU0FBU08sY0FBYyxFQUNyQlAsU0FBUyxFQUNURSxRQUFRLEVBQ1JNLFdBQVcsUUFBUSxFQUNuQixHQUFHaEIsT0FDa0Q7SUFDckQscUJBQ0UsOERBQUNOLDBEQUFzQjtrQkFDckIsNEVBQUNBLDJEQUF1QjtZQUN0QlEsYUFBVTtZQUNWTSxXQUFXViw4Q0FBRUEsQ0FDWCxpakJBQ0FrQixhQUFhLFlBQ1gsbUlBQ0ZSO1lBRUZRLFVBQVVBO1lBQ1QsR0FBR2hCLEtBQUs7OzhCQUVULDhEQUFDbUI7Ozs7OzhCQUNELDhEQUFDekIsNERBQXdCO29CQUN2QmMsV0FBV1YsOENBQUVBLENBQ1gsT0FDQWtCLGFBQWEsWUFDWDs4QkFHSE47Ozs7Ozs4QkFFSCw4REFBQ1c7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJVDtBQUVBLFNBQVNDLFlBQVksRUFDbkJkLFNBQVMsRUFDVCxHQUFHUixPQUNnRDtJQUNuRCxxQkFDRSw4REFBQ04seURBQXFCO1FBQ3BCUSxhQUFVO1FBQ1ZNLFdBQVdWLDhDQUFFQSxDQUFDLDZDQUE2Q1U7UUFDMUQsR0FBR1IsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTd0IsV0FBVyxFQUNsQmhCLFNBQVMsRUFDVEUsUUFBUSxFQUNSLEdBQUdWLE9BQytDO0lBQ2xELHFCQUNFLDhEQUFDTix3REFBb0I7UUFDbkJRLGFBQVU7UUFDVk0sV0FBV1YsOENBQUVBLENBQ1gsNmFBQ0FVO1FBRUQsR0FBR1IsS0FBSzs7MEJBRVQsOERBQUMwQjtnQkFBS2xCLFdBQVU7MEJBQ2QsNEVBQUNkLGlFQUE2Qjs4QkFDNUIsNEVBQUNDLG1IQUFTQTt3QkFBQ2EsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHekIsOERBQUNkLDREQUF3QjswQkFBRWdCOzs7Ozs7Ozs7Ozs7QUFHakM7QUFFQSxTQUFTbUIsZ0JBQWdCLEVBQ3ZCckIsU0FBUyxFQUNULEdBQUdSLE9BQ29EO0lBQ3ZELHFCQUNFLDhEQUFDTiw2REFBeUI7UUFDeEJRLGFBQVU7UUFDVk0sV0FBV1YsOENBQUVBLENBQUMsaURBQWlEVTtRQUM5RCxHQUFHUixLQUFLOzs7Ozs7QUFHZjtBQUVBLFNBQVNtQixxQkFBcUIsRUFDNUJYLFNBQVMsRUFDVCxHQUFHUixPQUN5RDtJQUM1RCxxQkFDRSw4REFBQ04sa0VBQThCO1FBQzdCUSxhQUFVO1FBQ1ZNLFdBQVdWLDhDQUFFQSxDQUNYLHdEQUNBVTtRQUVELEdBQUdSLEtBQUs7a0JBRVQsNEVBQUNILG1IQUFhQTtZQUFDVyxXQUFVOzs7Ozs7Ozs7OztBQUcvQjtBQUVBLFNBQVNhLHVCQUF1QixFQUM5QmIsU0FBUyxFQUNULEdBQUdSLE9BQzJEO0lBQzlELHFCQUNFLDhEQUFDTixvRUFBZ0M7UUFDL0JRLGFBQVU7UUFDVk0sV0FBV1YsOENBQUVBLENBQ1gsd0RBQ0FVO1FBRUQsR0FBR1IsS0FBSztrQkFFVCw0RUFBQ0osbUhBQWVBO1lBQUNZLFdBQVU7Ozs7Ozs7Ozs7O0FBR2pDO0FBYUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy95YW5nd2VucWlhbmcvZGV2L2FpL0ZsZXhpSHViL3Z1ZS12YmVuLWFkbWluL2FwcHMvd2Vic2l0ZS9zcmMvY29tcG9uZW50cy91aS9zZWxlY3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBTZWxlY3RQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zZWxlY3RcIlxuaW1wb3J0IHsgQ2hlY2tJY29uLCBDaGV2cm9uRG93bkljb24sIENoZXZyb25VcEljb24gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTZWxlY3Qoe1xuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gPFNlbGVjdFByaW1pdGl2ZS5Sb290IGRhdGEtc2xvdD1cInNlbGVjdFwiIHsuLi5wcm9wc30gLz5cbn1cblxuZnVuY3Rpb24gU2VsZWN0R3JvdXAoe1xuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5Hcm91cD4pIHtcbiAgcmV0dXJuIDxTZWxlY3RQcmltaXRpdmUuR3JvdXAgZGF0YS1zbG90PVwic2VsZWN0LWdyb3VwXCIgey4uLnByb3BzfSAvPlxufVxuXG5mdW5jdGlvbiBTZWxlY3RWYWx1ZSh7XG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczx0eXBlb2YgU2VsZWN0UHJpbWl0aXZlLlZhbHVlPikge1xuICByZXR1cm4gPFNlbGVjdFByaW1pdGl2ZS5WYWx1ZSBkYXRhLXNsb3Q9XCJzZWxlY3QtdmFsdWVcIiB7Li4ucHJvcHN9IC8+XG59XG5cbmZ1bmN0aW9uIFNlbGVjdFRyaWdnZXIoe1xuICBjbGFzc05hbWUsXG4gIHNpemUgPSBcImRlZmF1bHRcIixcbiAgY2hpbGRyZW4sXG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczx0eXBlb2YgU2VsZWN0UHJpbWl0aXZlLlRyaWdnZXI+ICYge1xuICBzaXplPzogXCJzbVwiIHwgXCJkZWZhdWx0XCJcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8U2VsZWN0UHJpbWl0aXZlLlRyaWdnZXJcbiAgICAgIGRhdGEtc2xvdD1cInNlbGVjdC10cmlnZ2VyXCJcbiAgICAgIGRhdGEtc2l6ZT17c2l6ZX1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiYm9yZGVyLWlucHV0IGRhdGEtW3BsYWNlaG9sZGVyXTp0ZXh0LW11dGVkLWZvcmVncm91bmQgWyZfc3ZnOm5vdChbY2xhc3MqPSd0ZXh0LSddKV06dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgYXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazphcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS80MCBhcmlhLWludmFsaWQ6Ym9yZGVyLWRlc3RydWN0aXZlIGRhcms6YmctaW5wdXQvMzAgZGFyazpob3ZlcjpiZy1pbnB1dC81MCBmbGV4IHctZml0IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTIgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0yIHRleHQtc20gd2hpdGVzcGFjZS1ub3dyYXAgc2hhZG93LXhzIHRyYW5zaXRpb24tW2NvbG9yLGJveC1zaGFkb3ddIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctWzNweF0gZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bc2l6ZT1kZWZhdWx0XTpoLTkgZGF0YS1bc2l6ZT1zbV06aC04ICo6ZGF0YS1bc2xvdD1zZWxlY3QtdmFsdWVdOmxpbmUtY2xhbXAtMSAqOmRhdGEtW3Nsb3Q9c2VsZWN0LXZhbHVlXTpmbGV4ICo6ZGF0YS1bc2xvdD1zZWxlY3QtdmFsdWVdOml0ZW1zLWNlbnRlciAqOmRhdGEtW3Nsb3Q9c2VsZWN0LXZhbHVlXTpnYXAtMiBbJl9zdmddOnBvaW50ZXItZXZlbnRzLW5vbmUgWyZfc3ZnXTpzaHJpbmstMCBbJl9zdmc6bm90KFtjbGFzcyo9J3NpemUtJ10pXTpzaXplLTRcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxTZWxlY3RQcmltaXRpdmUuSWNvbiBhc0NoaWxkPlxuICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cInNpemUtNCBvcGFjaXR5LTUwXCIgLz5cbiAgICAgIDwvU2VsZWN0UHJpbWl0aXZlLkljb24+XG4gICAgPC9TZWxlY3RQcmltaXRpdmUuVHJpZ2dlcj5cbiAgKVxufVxuXG5mdW5jdGlvbiBTZWxlY3RDb250ZW50KHtcbiAgY2xhc3NOYW1lLFxuICBjaGlsZHJlbixcbiAgcG9zaXRpb24gPSBcInBvcHBlclwiLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5Db250ZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxTZWxlY3RQcmltaXRpdmUuUG9ydGFsPlxuICAgICAgPFNlbGVjdFByaW1pdGl2ZS5Db250ZW50XG4gICAgICAgIGRhdGEtc2xvdD1cInNlbGVjdC1jb250ZW50XCJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImJnLXBvcG92ZXIgdGV4dC1wb3BvdmVyLWZvcmVncm91bmQgZGF0YS1bc3RhdGU9b3Blbl06YW5pbWF0ZS1pbiBkYXRhLVtzdGF0ZT1jbG9zZWRdOmFuaW1hdGUtb3V0IGRhdGEtW3N0YXRlPWNsb3NlZF06ZmFkZS1vdXQtMCBkYXRhLVtzdGF0ZT1vcGVuXTpmYWRlLWluLTAgZGF0YS1bc3RhdGU9Y2xvc2VkXTp6b29tLW91dC05NSBkYXRhLVtzdGF0ZT1vcGVuXTp6b29tLWluLTk1IGRhdGEtW3NpZGU9Ym90dG9tXTpzbGlkZS1pbi1mcm9tLXRvcC0yIGRhdGEtW3NpZGU9bGVmdF06c2xpZGUtaW4tZnJvbS1yaWdodC0yIGRhdGEtW3NpZGU9cmlnaHRdOnNsaWRlLWluLWZyb20tbGVmdC0yIGRhdGEtW3NpZGU9dG9wXTpzbGlkZS1pbi1mcm9tLWJvdHRvbS0yIHJlbGF0aXZlIHotNTAgbWF4LWgtKC0tcmFkaXgtc2VsZWN0LWNvbnRlbnQtYXZhaWxhYmxlLWhlaWdodCkgbWluLXctWzhyZW1dIG9yaWdpbi0oLS1yYWRpeC1zZWxlY3QtY29udGVudC10cmFuc2Zvcm0tb3JpZ2luKSBvdmVyZmxvdy14LWhpZGRlbiBvdmVyZmxvdy15LWF1dG8gcm91bmRlZC1tZCBib3JkZXIgc2hhZG93LW1kXCIsXG4gICAgICAgICAgcG9zaXRpb24gPT09IFwicG9wcGVyXCIgJiZcbiAgICAgICAgICAgIFwiZGF0YS1bc2lkZT1ib3R0b21dOnRyYW5zbGF0ZS15LTEgZGF0YS1bc2lkZT1sZWZ0XTotdHJhbnNsYXRlLXgtMSBkYXRhLVtzaWRlPXJpZ2h0XTp0cmFuc2xhdGUteC0xIGRhdGEtW3NpZGU9dG9wXTotdHJhbnNsYXRlLXktMVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICBwb3NpdGlvbj17cG9zaXRpb259XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgID5cbiAgICAgICAgPFNlbGVjdFNjcm9sbFVwQnV0dG9uIC8+XG4gICAgICAgIDxTZWxlY3RQcmltaXRpdmUuVmlld3BvcnRcbiAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJwLTFcIixcbiAgICAgICAgICAgIHBvc2l0aW9uID09PSBcInBvcHBlclwiICYmXG4gICAgICAgICAgICAgIFwiaC1bdmFyKC0tcmFkaXgtc2VsZWN0LXRyaWdnZXItaGVpZ2h0KV0gdy1mdWxsIG1pbi13LVt2YXIoLS1yYWRpeC1zZWxlY3QtdHJpZ2dlci13aWR0aCldIHNjcm9sbC1teS0xXCJcbiAgICAgICAgICApfVxuICAgICAgICA+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1NlbGVjdFByaW1pdGl2ZS5WaWV3cG9ydD5cbiAgICAgICAgPFNlbGVjdFNjcm9sbERvd25CdXR0b24gLz5cbiAgICAgIDwvU2VsZWN0UHJpbWl0aXZlLkNvbnRlbnQ+XG4gICAgPC9TZWxlY3RQcmltaXRpdmUuUG9ydGFsPlxuICApXG59XG5cbmZ1bmN0aW9uIFNlbGVjdExhYmVsKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5MYWJlbD4pIHtcbiAgcmV0dXJuIChcbiAgICA8U2VsZWN0UHJpbWl0aXZlLkxhYmVsXG4gICAgICBkYXRhLXNsb3Q9XCJzZWxlY3QtbGFiZWxcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBweC0yIHB5LTEuNSB0ZXh0LXhzXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5mdW5jdGlvbiBTZWxlY3RJdGVtKHtcbiAgY2xhc3NOYW1lLFxuICBjaGlsZHJlbixcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBTZWxlY3RQcmltaXRpdmUuSXRlbT4pIHtcbiAgcmV0dXJuIChcbiAgICA8U2VsZWN0UHJpbWl0aXZlLkl0ZW1cbiAgICAgIGRhdGEtc2xvdD1cInNlbGVjdC1pdGVtXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZm9jdXM6YmctYWNjZW50IGZvY3VzOnRleHQtYWNjZW50LWZvcmVncm91bmQgWyZfc3ZnOm5vdChbY2xhc3MqPSd0ZXh0LSddKV06dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHJlbGF0aXZlIGZsZXggdy1mdWxsIGN1cnNvci1kZWZhdWx0IGl0ZW1zLWNlbnRlciBnYXAtMiByb3VuZGVkLXNtIHB5LTEuNSBwci04IHBsLTIgdGV4dC1zbSBvdXRsaW5lLWhpZGRlbiBzZWxlY3Qtbm9uZSBkYXRhLVtkaXNhYmxlZF06cG9pbnRlci1ldmVudHMtbm9uZSBkYXRhLVtkaXNhYmxlZF06b3BhY2l0eS01MCBbJl9zdmddOnBvaW50ZXItZXZlbnRzLW5vbmUgWyZfc3ZnXTpzaHJpbmstMCBbJl9zdmc6bm90KFtjbGFzcyo9J3NpemUtJ10pXTpzaXplLTQgKjpbc3Bhbl06bGFzdDpmbGV4ICo6W3NwYW5dOmxhc3Q6aXRlbXMtY2VudGVyICo6W3NwYW5dOmxhc3Q6Z2FwLTJcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTIgZmxleCBzaXplLTMuNSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPFNlbGVjdFByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICAgICAgPC9TZWxlY3RQcmltaXRpdmUuSXRlbUluZGljYXRvcj5cbiAgICAgIDwvc3Bhbj5cbiAgICAgIDxTZWxlY3RQcmltaXRpdmUuSXRlbVRleHQ+e2NoaWxkcmVufTwvU2VsZWN0UHJpbWl0aXZlLkl0ZW1UZXh0PlxuICAgIDwvU2VsZWN0UHJpbWl0aXZlLkl0ZW0+XG4gIClcbn1cblxuZnVuY3Rpb24gU2VsZWN0U2VwYXJhdG9yKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5TZXBhcmF0b3I+KSB7XG4gIHJldHVybiAoXG4gICAgPFNlbGVjdFByaW1pdGl2ZS5TZXBhcmF0b3JcbiAgICAgIGRhdGEtc2xvdD1cInNlbGVjdC1zZXBhcmF0b3JcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImJnLWJvcmRlciBwb2ludGVyLWV2ZW50cy1ub25lIC1teC0xIG15LTEgaC1weFwiLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZnVuY3Rpb24gU2VsZWN0U2Nyb2xsVXBCdXR0b24oe1xuICBjbGFzc05hbWUsXG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczx0eXBlb2YgU2VsZWN0UHJpbWl0aXZlLlNjcm9sbFVwQnV0dG9uPikge1xuICByZXR1cm4gKFxuICAgIDxTZWxlY3RQcmltaXRpdmUuU2Nyb2xsVXBCdXR0b25cbiAgICAgIGRhdGEtc2xvdD1cInNlbGVjdC1zY3JvbGwtdXAtYnV0dG9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZmxleCBjdXJzb3ItZGVmYXVsdCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktMVwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgPlxuICAgICAgPENoZXZyb25VcEljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICA8L1NlbGVjdFByaW1pdGl2ZS5TY3JvbGxVcEJ1dHRvbj5cbiAgKVxufVxuXG5mdW5jdGlvbiBTZWxlY3RTY3JvbGxEb3duQnV0dG9uKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlbGVjdFByaW1pdGl2ZS5TY3JvbGxEb3duQnV0dG9uPikge1xuICByZXR1cm4gKFxuICAgIDxTZWxlY3RQcmltaXRpdmUuU2Nyb2xsRG93bkJ1dHRvblxuICAgICAgZGF0YS1zbG90PVwic2VsZWN0LXNjcm9sbC1kb3duLWJ1dHRvblwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggY3Vyc29yLWRlZmF1bHQgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTFcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIDxDaGV2cm9uRG93bkljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICA8L1NlbGVjdFByaW1pdGl2ZS5TY3JvbGxEb3duQnV0dG9uPlxuICApXG59XG5cbmV4cG9ydCB7XG4gIFNlbGVjdCxcbiAgU2VsZWN0Q29udGVudCxcbiAgU2VsZWN0R3JvdXAsXG4gIFNlbGVjdEl0ZW0sXG4gIFNlbGVjdExhYmVsLFxuICBTZWxlY3RTY3JvbGxEb3duQnV0dG9uLFxuICBTZWxlY3RTY3JvbGxVcEJ1dHRvbixcbiAgU2VsZWN0U2VwYXJhdG9yLFxuICBTZWxlY3RUcmlnZ2VyLFxuICBTZWxlY3RWYWx1ZSxcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlbGVjdFByaW1pdGl2ZSIsIkNoZWNrSWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkNoZXZyb25VcEljb24iLCJjbiIsIlNlbGVjdCIsInByb3BzIiwiUm9vdCIsImRhdGEtc2xvdCIsIlNlbGVjdEdyb3VwIiwiR3JvdXAiLCJTZWxlY3RWYWx1ZSIsIlZhbHVlIiwiU2VsZWN0VHJpZ2dlciIsImNsYXNzTmFtZSIsInNpemUiLCJjaGlsZHJlbiIsIlRyaWdnZXIiLCJkYXRhLXNpemUiLCJJY29uIiwiYXNDaGlsZCIsIlNlbGVjdENvbnRlbnQiLCJwb3NpdGlvbiIsIlBvcnRhbCIsIkNvbnRlbnQiLCJTZWxlY3RTY3JvbGxVcEJ1dHRvbiIsIlZpZXdwb3J0IiwiU2VsZWN0U2Nyb2xsRG93bkJ1dHRvbiIsIlNlbGVjdExhYmVsIiwiTGFiZWwiLCJTZWxlY3RJdGVtIiwiSXRlbSIsInNwYW4iLCJJdGVtSW5kaWNhdG9yIiwiSXRlbVRleHQiLCJTZWxlY3RTZXBhcmF0b3IiLCJTZXBhcmF0b3IiLCJTY3JvbGxVcEJ1dHRvbiIsIlNjcm9sbERvd25CdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAppMode.ts":
/*!*********************************!*\
  !*** ./src/hooks/useAppMode.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateEditorPath: () => (/* binding */ generateEditorPath),\n/* harmony export */   generatePreviewPath: () => (/* binding */ generatePreviewPath),\n/* harmony export */   generateSitePath: () => (/* binding */ generateSitePath),\n/* harmony export */   useAppMode: () => (/* binding */ useAppMode)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAppMode,generateEditorPath,generateSitePath,generatePreviewPath auto */ \n\n/**\n * 应用模式检测Hook\n * 用于判断当前是编辑器模式还是用户网站渲染模式\n */ function useAppMode() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"useAppMode.useMemo\": ()=>{\n            // 解析路径\n            const pathSegments = pathname.split('/').filter(Boolean);\n            const locale = pathSegments[0];\n            // 检查是否为编辑器模式\n            const isEditor = pathname.includes('/editor/');\n            const isPreview = pathname.includes('/preview');\n            const isUserSite = !isEditor;\n            // 提取网站ID（编辑器模式）\n            let siteId;\n            if (isEditor) {\n                const editorMatch = pathname.match(/\\/editor\\/([^/]+)/);\n                siteId = editorMatch?.[1];\n            }\n            // 提取域名（用户网站模式）\n            let domain;\n            if (isUserSite && pathSegments.length >= 2) {\n                domain = pathSegments[1]; // 第二个路径段是域名\n            }\n            // 确定模式\n            let mode;\n            if (isPreview) {\n                mode = 'preview';\n            } else if (isEditor) {\n                mode = 'editor';\n            } else {\n                mode = 'site';\n            }\n            return {\n                isEditor,\n                isUserSite,\n                isPreview,\n                mode,\n                siteId,\n                domain,\n                locale\n            };\n        }\n    }[\"useAppMode.useMemo\"], [\n        pathname\n    ]);\n}\n/**\n * 生成编辑器路径\n */ function generateEditorPath(siteId, locale = 'zh-CN') {\n    return `/${locale}/editor/${siteId}`;\n}\n/**\n * 生成用户网站路径\n */ function generateSitePath(domain, locale = 'zh-CN', path = '') {\n    const cleanPath = path.startsWith('/') ? path : `/${path}`;\n    return `/${locale}/${domain}${cleanPath}`;\n}\n/**\n * 生成预览路径\n */ function generatePreviewPath(siteId, locale = 'zh-CN') {\n    return `/${locale}/editor/${siteId}/preview`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAppMode.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL3lhbmd3ZW5xaWFuZy9kZXYvYWkvRmxleGlIdWIvdnVlLXZiZW4tYWRtaW4vYXBwcy93ZWJzaXRlL3NyYy9saWIvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@craftjs/core":
/*!********************************!*\
  !*** external "@craftjs/core" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@craftjs/core");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0","vendor-chunks/react-remove-scroll@2.7.0_@types+react@18.3.23_react@18.3.1","vendor-chunks/lucide-react@0.460.0_react@18.3.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/react-style-singleton@2.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@18.3.23_react@18.3.1","vendor-chunks/use-callback-ref@1.3.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/@floating-ui+utils@0.2.9","vendor-chunks/use-sidecar@1.1.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/tslib@2.8.1","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/clsx@2.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23__@ty_bd769e2c7ddceeff6e63be21c84dfac7","vendor-chunks/@radix-ui+react-use-size@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-effect-event@0.0.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf","vendor-chunks/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3","vendor-chunks/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053","vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57","vendor-chunks/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f","vendor-chunks/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e","vendor-chunks/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5","vendor-chunks/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb","vendor-chunks/@radix-ui+primitive@1.1.2","vendor-chunks/@radix-ui+number@1.1.1","vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@floating-ui+dom@1.6.13","vendor-chunks/@floating-ui+core@1.6.9","vendor-chunks/get-nonce@1.0.1","vendor-chunks/aria-hidden@1.2.4"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@15.3.3_@babel+core@7.26.10_@playwright+test@1.52.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&page=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Feditor%2F%5BsiteId%5D%2Fpage.tsx&appDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyangwenqiang%2Fdev%2Fai%2FFlexiHub%2Fvue-vben-admin%2Fapps%2Fwebsite&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();