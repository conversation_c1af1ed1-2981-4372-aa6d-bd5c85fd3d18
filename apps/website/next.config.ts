import type { NextConfig } from 'next';

import createNextIntlPlugin from 'next-intl/plugin';

// 创建next-intl插件
const withNextIntl = createNextIntlPlugin('./src/lib/i18n.ts');

// 获取环境变量的安全方法
const getEnvVar = (key: string, defaultValue: string = '') => {
  const process = require('node:process');
  return process.env[key] || defaultValue;
};

const nextConfig: NextConfig = {
  // 图片配置
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // 路由重写 - 支持编辑器、预览和用户网站统一路由
  async rewrites() {
    return [
      // 编辑器路由 - 保持原样
      {
        source: '/:locale/editor/:siteId*',
        destination: '/:locale/editor/:siteId*',
      },
      // 预览路由 - 保持原样
      {
        source: '/:locale/editor/:siteId/preview',
        destination: '/:locale/editor/:siteId/preview',
      },
      // 用户网站路由 - 重写到site目录（内部访问）
      {
        source: '/:locale/site/:domain*',
        destination: '/:locale/site/:domain*',
      },
      // 用户网站路由 - 直接域名访问（生产环境）
      {
        source: '/:locale/:domain([^/]+)/:path*',
        destination: '/:locale/site/:domain/:path*',
      },
    ];
  },

  // 重定向配置
  async redirects() {
    return [
      // 根路径重定向到默认语言（由国际化中间件处理）
      {
        source: '/',
        destination: '/zh-CN',
        permanent: false,
      },
    ];
  },

  // 自定义域名支持
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Accept-Language',
            value: 'zh-CN,en-US,ja-JP',
          },
        ],
      },
    ];
  },

  // 环境变量
  env: {
    APP_MODE: getEnvVar('APP_MODE', 'unified'),
    EDITOR_MODE: getEnvVar('EDITOR_MODE', 'false'),
    // 支持自定义域名检测
    CUSTOM_DOMAIN_MODE: getEnvVar('CUSTOM_DOMAIN_MODE', 'false'),
    // 国际化配置
    DEFAULT_LOCALE: getEnvVar('DEFAULT_LOCALE', 'zh-CN'),
    SUPPORTED_LOCALES: getEnvVar('SUPPORTED_LOCALES', 'zh-CN,en-US,ja-JP'),
  },

  // 编译选项
  typescript: {
    ignoreBuildErrors: false,
  },

  // ESLint配置
  eslint: {
    ignoreDuringBuilds: false,
  },

  // 实验性功能
  experimental: {
    // 启用服务端组件
    serverComponentsExternalPackages: ['@craftjs/core'],
  },

  // 国际化配置（由next-intl处理）
  // 不需要在这里配置i18n，因为使用了next-intl中间件
};

// 使用next-intl插件包装配置
export default withNextIntl(nextConfig);
