'use client';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNode } from '@craftjs/core';
import React from 'react';

import { ComponentSchema, PropertySchema } from '../types/component';

/**
 * 根据属性类型渲染对应的输入组件
 */
const renderPropertyInput = (
  key: string,
  schema: PropertySchema,
  value: any,
  onChange: (key: string, value: any) => void,
) => {
  const handleChange = (newValue: any) => {
    onChange(key, newValue);
  };

  switch (schema.type) {
    case 'boolean': {
      return (
        <label className="flex items-center space-x-2">
          <input
            checked={value === undefined ? schema.default : value}
            className="rounded border-gray-300"
            onChange={(e) => handleChange(e.target.checked)}
            type="checkbox"
          />
          <span className="text-sm">{schema.description}</span>
        </label>
      );
    }

    case 'color': {
      return (
        <div className="flex items-center space-x-2">
          <input
            className="h-8 w-8 rounded border"
            onChange={(e) => handleChange(e.target.value)}
            type="color"
            value={value || schema.default || '#000000'}
          />
          <Input
            onChange={(e) => handleChange(e.target.value)}
            placeholder="#000000"
            value={value || schema.default || '#000000'}
          />
        </div>
      );
    }

    case 'number': {
      return (
        <Input
          max={schema.max}
          min={schema.min}
          onChange={(e) => handleChange(Number(e.target.value))}
          step={schema.step}
          type="number"
          value={value || schema.default || 0}
        />
      );
    }

    case 'select': {
      return (
        <Select onValueChange={handleChange} value={value || schema.default}>
          <SelectTrigger>
            <SelectValue placeholder={`选择${schema.title}`} />
          </SelectTrigger>
          <SelectContent>
            {schema.enum?.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    case 'slider': {
      return (
        <div className="space-y-2">
          <input
            className="w-full"
            max={schema.max || 100}
            min={schema.min || 0}
            onChange={(e) => handleChange(Number(e.target.value))}
            step={schema.step || 1}
            type="range"
            value={value || schema.default || 0}
          />
          <div className="text-center text-sm text-gray-500">
            {value || schema.default || 0}
          </div>
        </div>
      );
    }

    case 'string': {
      return (
        <Input
          onChange={(e) => handleChange(e.target.value)}
          placeholder={schema.description}
          value={value || schema.default || ''}
        />
      );
    }

    default: {
      return (
        <Input
          onChange={(e) => handleChange(e.target.value)}
          value={value || schema.default || ''}
        />
      );
    }
  }
};

/**
 * 自动生成设置面板组件
 */
export const generateSettings = (schema: ComponentSchema) => {
  return function AutoGeneratedSettings() {
    const {
      actions: { setProp },
      props,
    } = useNode((node) => ({
      props: node.data.props,
    }));

    const handlePropertyChange = (key: string, value: any) => {
      setProp((props: any) => {
        props[key] = value;
      });
    };

    return (
      <div className="space-y-6 p-4">
        <div className="border-b border-gray-200 pb-2">
          <h3 className="text-lg font-medium text-gray-900">组件设置</h3>
          <p className="text-sm text-gray-500">配置组件的属性和样式</p>
        </div>

        <div className="space-y-4">
          {Object.entries(schema.properties).map(([key, propertySchema]) => (
            <div className="space-y-2" key={key}>
              <label className="block text-sm font-medium text-gray-700">
                {propertySchema.title}
                {schema.required?.includes(key) && (
                  <span className="ml-1 text-red-500">*</span>
                )}
              </label>
              {propertySchema.description && (
                <p className="text-xs text-gray-500">
                  {propertySchema.description}
                </p>
              )}
              {renderPropertyInput(
                key,
                propertySchema,
                props[key],
                handlePropertyChange,
              )}
            </div>
          ))}
        </div>

        {typeof window !== 'undefined' &&
          window.location.hostname === 'localhost' && (
            <div className="mt-6 border-t border-gray-200 pt-4">
              <details className="space-y-2">
                <summary className="cursor-pointer text-sm font-medium">
                  调试信息
                </summary>
                <pre className="overflow-auto rounded bg-gray-100 p-2 text-xs">
                  {JSON.stringify(props, null, 2)}
                </pre>
              </details>
            </div>
          )}
      </div>
    );
  };
};

/**
 * 快速创建简单的设置面板
 */
export const createSimpleSettings = (
  fields: Array<{
    default?: any;
    key: string;
    label: string;
    options?: PropertySchema['enum'];
    type: PropertySchema['type'];
  }>,
) => {
  const properties: Record<string, PropertySchema> = {};
  for (const field of fields) {
    properties[field.key] = {
      type: field.type,
      title: field.label,
      default: field.default,
      enum: field.options,
    };
  }

  const schema: ComponentSchema = {
    type: 'object',
    properties,
  };

  return generateSettings(schema);
};
