import { ButtonPreview, MaterialButton } from '../components/basic/Button';
import { buttonSchema } from '../components/basic/Button/schema';
import { ButtonSettings } from '../components/basic/Button/settings';
import { MaterialText, TextPreview } from '../components/basic/Text';
import { textSchema } from '../components/basic/Text/schema';
import { TextSettings } from '../components/basic/Text/settings';
import { MaterialComponent } from '../types/component';

/**
 * Button物料组件定义
 */
export const buttonMaterial: MaterialComponent = {
  id: 'Button',
  name: '按钮',
  category: 'basic',
  icon: '🔘',
  description: '可点击的按钮组件，支持多种样式和尺寸',
  version: '1.0.0',
  component: MaterialButton,
  preview: ButtonPreview,
  schema: buttonSchema,
  craft: {
    props: {
      text: '按钮',
      variant: 'default',
      size: 'default',
      disabled: false,
      className: '',
    },
    related: {
      settings: ButtonSettings,
    },
    rules: {
      canDrag: true,
      canDrop: false,
      canMoveIn: false,
      canMoveOut: true,
    },
  },
};

/**
 * Text物料组件定义
 */
export const textMaterial: MaterialComponent = {
  id: 'Text',
  name: '文本',
  category: 'basic',
  icon: '📝',
  description: '可编辑的文本组件，支持字体、颜色、对齐等样式设置',
  version: '1.0.0',
  component: MaterialText,
  preview: TextPreview,
  schema: textSchema,
  craft: {
    props: {
      text: '文本内容',
      fontSize: 16,
      fontWeight: '400',
      color: '#000000',
      textAlign: 'left',
      className: '',
    },
    related: {
      settings: TextSettings,
    },
    rules: {
      canDrag: true,
      canDrop: false,
      canMoveIn: false,
      canMoveOut: true,
    },
  },
};

/**
 * 所有基础组件
 */
export const basicComponents: MaterialComponent[] = [
  buttonMaterial,
  textMaterial,
  // 其他基础组件将在这里添加
];
