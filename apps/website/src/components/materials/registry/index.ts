import React from 'react';

import {
  ComponentCategory,
  MaterialComponent,
  ToolboxItem,
} from '../types/component';
import {
  ComponentQuery,
  RegistryConfig,
  RegistryResult,
  RegistryStats,
} from '../types/registry';

/**
 * 物料组件注册表类
 */
class MaterialRegistry {
  private components = new Map<string, MaterialComponent>();
  private config: RegistryConfig;

  constructor(config?: Partial<RegistryConfig>) {
    this.config = {
      autoRegister: true,
      devMode: false,
      validation: {
        required: ['id', 'name', 'component', 'schema'],
        optional: ['icon', 'description', 'version', 'category'],
      },
      ...config,
    };
  }

  /**
   * 清空注册表
   */
  clear(): void {
    this.components.clear();
  }

  /**
   * 获取组件
   */
  get(id: string): MaterialComponent | undefined {
    return this.components.get(id);
  }

  /**
   * 获取所有组件
   */
  getAll(): MaterialComponent[] {
    const results: MaterialComponent[] = [];
    this.components.forEach((comp) => {
      results.push(comp);
    });
    return results;
  }

  /**
   * 按分类获取组件
   */
  getByCategory(category: ComponentCategory): MaterialComponent[] {
    const results: MaterialComponent[] = [];
    this.components.forEach((comp) => {
      if (comp.category === category) {
        results.push(comp);
      }
    });
    return results;
  }

  /**
   * 获取CraftJS resolver对象
   */
  getCraftResolver(): Record<string, React.ComponentType> {
    const resolver: Record<string, React.ComponentType> = {};
    this.components.forEach((component, id) => {
      resolver[id] = component.component;
    });
    return resolver;
  }

  /**
   * 获取统计信息
   */
  getStats(): RegistryStats {
    const all = this.getAll();
    const byCategory: Record<ComponentCategory, number> = {
      basic: 0,
      layout: 0,
      advanced: 0,
      custom: 0,
    };

    for (const comp of all) {
      byCategory[comp.category]++;
    }

    return {
      total: all.length,
      byCategory,
      lastUpdated: new Date(),
    };
  }

  /**
   * 获取工具箱数据
   */
  getToolboxData(): Array<{
    category: ComponentCategory;
    items: ToolboxItem[];
  }> {
    const categories: ComponentCategory[] = [
      'basic',
      'layout',
      'advanced',
      'custom',
    ];

    return categories.map((category) => ({
      category,
      items: this.getByCategory(category).map((comp) => ({
        id: comp.id,
        name: comp.name,
        icon: comp.icon,
        component: comp.component,
        props: comp.craft.props,
      })),
    }));
  }

  /**
   * 注册单个组件
   */
  register(component: MaterialComponent): RegistryResult {
    const validation = this.validateComponent(component);
    if (!validation.success) {
      return validation;
    }

    this.components.set(component.id, component);

    return {
      success: true,
      component,
      message: `Component "${component.name}" registered successfully`,
    };
  }

  /**
   * 批量注册组件
   */
  registerBatch(components: MaterialComponent[]): RegistryResult[] {
    const results: RegistryResult[] = [];

    for (const component of components) {
      results.push(this.register(component));
    }

    return results;
  }

  /**
   * 搜索组件
   */
  search(query: ComponentQuery): MaterialComponent[] {
    const allComponents: MaterialComponent[] = [];
    this.components.forEach((comp) => {
      allComponents.push(comp);
    });

    let results = allComponents;

    // 按分类过滤
    if (query.category) {
      results = results.filter((comp) => comp.category === query.category);
    }

    // 按关键字搜索
    if (query.search) {
      const searchLower = query.search.toLowerCase();
      results = results.filter(
        (comp) =>
          comp.name.toLowerCase().includes(searchLower) ||
          comp.description.toLowerCase().includes(searchLower),
      );
    }

    // 按版本过滤
    if (query.version) {
      results = results.filter((comp) => comp.version === query.version);
    }

    return results;
  }

  /**
   * 获取注册表大小
   */
  size(): number {
    return this.components.size;
  }

  /**
   * 删除组件
   */
  unregister(id: string): boolean {
    return this.components.delete(id);
  }

  /**
   * 验证组件配置
   */
  private validateComponent(component: MaterialComponent): RegistryResult {
    const { required } = this.config.validation;

    for (const field of required) {
      if (!component[field as keyof MaterialComponent]) {
        return {
          success: false,
          message: `Missing required field: ${field}`,
        };
      }
    }

    // 检查ID是否已存在
    if (this.components.has(component.id)) {
      return {
        success: false,
        message: `Component with id "${component.id}" already exists`,
      };
    }

    return { success: true };
  }
}

// 全局注册表实例
export const materialRegistry = new MaterialRegistry({
  devMode:
    typeof window !== 'undefined' && window.location.hostname === 'localhost',
});

// 导出注册表类供外部使用
export { MaterialRegistry };
