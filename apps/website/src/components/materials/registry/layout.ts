import {
  ContainerPreview,
  MaterialContainer,
} from '../components/layout/Container';
import { containerSchema } from '../components/layout/Container/schema';
import { ContainerSettings } from '../components/layout/Container/settings';
import { MaterialComponent } from '../types/component';

/**
 * Container物料组件定义
 */
export const containerMaterial: MaterialComponent = {
  id: 'Container',
  name: '容器',
  category: 'layout',
  icon: '📦',
  description: '可包含其他组件的容器，支持背景、边距、边框等样式设置',
  version: '1.0.0',
  component: MaterialContainer,
  preview: ContainerPreview,
  schema: containerSchema,
  craft: {
    props: {
      background: '#ffffff',
      padding: 20,
      margin: 0,
      borderRadius: 0,
      border: 'none',
      minHeight: 100,
      className: '',
    },
    related: {
      settings: ContainerSettings,
    },
    rules: {
      canDrag: true,
      canDrop: true,
      canMoveIn: true,
      canMoveOut: true,
    },
  },
};

/**
 * 所有布局组件
 */
export const layoutComponents: MaterialComponent[] = [
  containerMaterial,
  // 其他布局组件将在这里添加
];
