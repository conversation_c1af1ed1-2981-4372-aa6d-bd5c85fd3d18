import { ComponentCategory, MaterialComponent } from './component';

/**
 * 注册表配置
 */
export interface RegistryConfig {
  // 是否启用自动注册
  autoRegister: boolean;
  // 是否启用开发模式
  devMode: boolean;
  // 组件验证规则
  validation: {
    optional: string[];
    required: string[];
  };
}

/**
 * 注册结果
 */
export interface RegistryResult {
  success: boolean;
  message?: string;
  component?: MaterialComponent;
}

/**
 * 组件查询条件
 */
export interface ComponentQuery {
  category?: ComponentCategory;
  search?: string;
  tags?: string[];
  version?: string;
}

/**
 * 注册表统计信息
 */
export interface RegistryStats {
  total: number;
  byCategory: Record<ComponentCategory, number>;
  lastUpdated: Date;
}

/**
 * 组件导入/导出格式
 */
export interface ComponentExport {
  version: string;
  components: MaterialComponent[];
  metadata: {
    description?: string;
    exportedAt: Date;
    exportedBy: string;
  };
}
