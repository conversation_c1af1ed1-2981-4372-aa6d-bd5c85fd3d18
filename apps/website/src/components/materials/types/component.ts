import React from 'react';

/**
 * 属性Schema定义
 */
export interface PropertySchema {
  type:
    | 'boolean'
    | 'color'
    | 'image'
    | 'number'
    | 'select'
    | 'slider'
    | 'string';
  title: string;
  description?: string;
  default?: any;
  enum?: Array<{ label: string; value: any }>;
  min?: number;
  max?: number;
  step?: number;
}

/**
 * 组件Schema定义
 */
export interface ComponentSchema {
  type: 'object';
  properties: Record<string, PropertySchema>;
  required?: string[];
}

/**
 * 物料组件接口
 */
export interface MaterialComponent {
  // 基本信息
  id: string;
  name: string;
  category: 'advanced' | 'basic' | 'custom' | 'layout';
  icon: string;
  description: string;
  version: string;

  // 组件实现
  component: React.ComponentType<any>;

  // CraftJS配置
  craft: {
    props: Record<string, any>;
    related: {
      settings: React.ComponentType;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };

  // 配置Schema
  schema: ComponentSchema;

  // 预览组件（用户网站渲染）
  preview: React.ComponentType<any>;
}

/**
 * 组件分类定义
 */
export type ComponentCategory = 'advanced' | 'basic' | 'custom' | 'layout';

/**
 * 工具箱项目接口
 */
export interface ToolboxItem {
  id: string;
  name: string;
  icon: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

/**
 * 组件属性类型
 */
export type ComponentProps = Record<string, any>;
