'use client';

import { useEditor, useNode } from '@craftjs/core';
import React from 'react';

/**
 * Container组件的属性接口
 */
export interface ContainerProps {
  background: string;
  padding: number;
  margin: number;
  borderRadius: number;
  border: string;
  minHeight: number;
  className?: string;
  children?: React.ReactNode;
}

/**
 * CraftJS组件类型
 */
type CraftComponent = React.FC<ContainerProps> & {
  craft: {
    custom: {
      displayName: string;
    };
    props: Partial<ContainerProps>;
    related: {
      settings: () => Promise<any>;
    };
    rules: {
      canDrag: boolean;
      canDrop: (incomingId: string) => boolean;
      canMoveIn: (incomingIds: string[]) => boolean;
      canMoveOut: boolean;
    };
  };
};

/**
 * 用于编辑器的Container组件
 */
export const MaterialContainer: CraftComponent = ({
  background = '#ffffff',
  padding = 20,
  margin = 0,
  borderRadius = 0,
  border = 'none',
  minHeight = 100,
  className = '',
  children,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    isHover,
  } = useNode((state) => ({
    selected: state.events.selected,
    isHover: state.events.hovered,
  }));

  const { enabled } = useEditor((state) => ({
    enabled: state.options.enabled,
  }));

  return (
    <div
      className={` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} ${isHover ? 'ring-1 ring-blue-300' : ''} ${enabled ? 'cursor-move' : ''} relative transition-all duration-200`}
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(drag(ref));
        }
        return undefined;
      }}
      style={{
        background,
        padding: `${padding}px`,
        margin: `${margin}px`,
        borderRadius: `${borderRadius}px`,
        border,
        minHeight: `${minHeight}px`,
      }}
    >
      {enabled && (selected || isHover) && (
        <div className="absolute left-0 top-0 -mt-6 rounded bg-blue-500 px-2 py-1 text-xs text-white">
          容器
        </div>
      )}
      {children}
    </div>
  );
};

/**
 * 用于用户网站的纯Container组件（预览组件）
 */
export const ContainerPreview: React.FC<ContainerProps> = ({
  background = '#ffffff',
  padding = 20,
  margin = 0,
  borderRadius = 0,
  border = 'none',
  minHeight = 100,
  className = '',
  children,
}) => {
  return (
    <div
      className={className}
      style={{
        background,
        padding: `${padding}px`,
        margin: `${margin}px`,
        borderRadius: `${borderRadius}px`,
        border,
        minHeight: `${minHeight}px`,
      }}
    >
      {children}
    </div>
  );
};

// CraftJS配置
MaterialContainer.craft = {
  props: {
    background: '#ffffff',
    padding: 20,
    margin: 0,
    borderRadius: 0,
    border: 'none',
    minHeight: 100,
    className: '',
  },
  related: {
    settings: () => import('./settings').then((comp) => comp.ContainerSettings),
  },
  rules: {
    canDrag: true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: true,
  },
  custom: {
    displayName: '容器',
  },
};
