import { ComponentSchema } from '../../../types/component';

/**
 * Container组件的配置Schema
 */
export const containerSchema: ComponentSchema = {
  type: 'object',
  properties: {
    background: {
      type: 'color',
      title: '背景颜色',
      description: '容器的背景颜色',
      default: '#ffffff',
    },
    padding: {
      type: 'number',
      title: '内边距',
      description: '容器内部的间距，单位：像素',
      default: 20,
      min: 0,
      max: 100,
    },
    margin: {
      type: 'number',
      title: '外边距',
      description: '容器外部的间距，单位：像素',
      default: 0,
      min: 0,
      max: 100,
    },
    borderRadius: {
      type: 'number',
      title: '圆角',
      description: '容器的圆角大小，单位：像素',
      default: 0,
      min: 0,
      max: 50,
    },
    border: {
      type: 'string',
      title: '边框',
      description: 'CSS边框样式，例如：1px solid #ccc',
      default: 'none',
    },
    minHeight: {
      type: 'number',
      title: '最小高度',
      description: '容器的最小高度，单位：像素',
      default: 100,
      min: 50,
      max: 1000,
    },
    className: {
      type: 'string',
      title: '自定义样式类名',
      description: '添加自定义的CSS类名',
      default: '',
    },
  },
  required: [],
};
