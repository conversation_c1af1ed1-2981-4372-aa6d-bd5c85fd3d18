'use client';

import { Input } from '@/components/ui/input';
import { useNode } from '@craftjs/core';
import React from 'react';

import { ContainerProps } from './index';

export const ContainerSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ContainerProps,
  }));

  return (
    <div className="space-y-6 p-4">
      <div className="border-b border-gray-200 pb-2">
        <h3 className="text-lg font-medium text-gray-900">容器设置</h3>
        <p className="text-sm text-gray-500">配置容器的样式和布局</p>
      </div>

      <div className="space-y-4">
        {/* 背景颜色 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            背景颜色
          </label>
          <div className="flex items-center space-x-2">
            <input
              className="h-8 w-8 rounded border"
              onChange={(e) =>
                setProp((props: ContainerProps) => {
                  props.background = e.target.value;
                })
              }
              type="color"
              value={props.background || '#ffffff'}
            />
            <Input
              onChange={(e) =>
                setProp((props: ContainerProps) => {
                  props.background = e.target.value;
                })
              }
              placeholder="#ffffff"
              value={props.background || '#ffffff'}
            />
          </div>
        </div>

        {/* 内边距 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            内边距 (px)
          </label>
          <Input
            max={100}
            min={0}
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.padding = Number(e.target.value);
              })
            }
            type="number"
            value={props.padding || 20}
          />
        </div>

        {/* 外边距 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            外边距 (px)
          </label>
          <Input
            max={100}
            min={0}
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.margin = Number(e.target.value);
              })
            }
            type="number"
            value={props.margin || 0}
          />
        </div>

        {/* 圆角 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            圆角 (px)
          </label>
          <Input
            max={50}
            min={0}
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.borderRadius = Number(e.target.value);
              })
            }
            type="number"
            value={props.borderRadius || 0}
          />
        </div>

        {/* 边框 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            边框
          </label>
          <Input
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.border = e.target.value;
              })
            }
            placeholder="例如: 1px solid #ccc"
            value={props.border || 'none'}
          />
          <p className="text-xs text-gray-500">
            使用CSS边框语法，例如：1px solid #ccc
          </p>
        </div>

        {/* 最小高度 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            最小高度 (px)
          </label>
          <Input
            max={1000}
            min={50}
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.minHeight = Number(e.target.value);
              })
            }
            type="number"
            value={props.minHeight || 100}
          />
        </div>

        {/* 自定义CSS类名 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            自定义样式类名
          </label>
          <Input
            onChange={(e) =>
              setProp((props: ContainerProps) => {
                props.className = e.target.value;
              })
            }
            placeholder="例如: my-custom-container-class"
            value={props.className || ''}
          />
          <p className="text-xs text-gray-500">可以添加自定义的CSS类名</p>
        </div>
      </div>
    </div>
  );
};
