'use client';

import { Button as UIButton } from '@/components/ui/button';
import { useNode } from '@craftjs/core';
import React from 'react';

/**
 * Button组件的属性接口
 */
export interface ButtonProps {
  text: string;
  variant:
    | 'default'
    | 'destructive'
    | 'ghost'
    | 'link'
    | 'outline'
    | 'secondary';
  size: 'default' | 'lg' | 'sm';
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}

/**
 * CraftJS组件类型
 */
type CraftComponent = React.FC<ButtonProps> & {
  craft: {
    custom: {
      displayName: string;
    };
    props: Partial<ButtonProps>;
    related: {
      settings: () => Promise<any>;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };
};

/**
 * 用于编辑器的Button组件
 */
export const MaterialButton: CraftComponent = ({
  text = '按钮',
  variant = 'default',
  size = 'default',
  disabled = false,
  className = '',
  onClick,
}) => {
  const {
    connectors: { connect, drag },
    selected,
  } = useNode((state) => ({
    selected: state.events.selected,
  }));

  return (
    <UIButton
      className={` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} transition-all duration-200`}
      disabled={disabled}
      onClick={onClick}
      ref={(ref: HTMLButtonElement | null) => {
        if (ref) {
          connect(drag(ref));
        }
        return undefined;
      }}
      size={size}
      variant={variant}
    >
      {text}
    </UIButton>
  );
};

/**
 * 用于用户网站的纯Button组件（预览组件）
 */
export const ButtonPreview: React.FC<ButtonProps> = ({
  text = '按钮',
  variant = 'default',
  size = 'default',
  disabled = false,
  className = '',
  onClick,
}) => {
  return (
    <UIButton
      className={className}
      disabled={disabled}
      onClick={onClick}
      size={size}
      variant={variant}
    >
      {text}
    </UIButton>
  );
};

// CraftJS配置
MaterialButton.craft = {
  props: {
    text: '按钮',
    variant: 'default',
    size: 'default',
    disabled: false,
    className: '',
  },
  related: {
    settings: () => import('./settings').then((comp) => comp.ButtonSettings),
  },
  rules: {
    canDrag: true,
    canDrop: false,
    canMoveIn: false,
    canMoveOut: true,
  },
  custom: {
    displayName: '按钮',
  },
};
