'use client';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNode } from '@craftjs/core';
import React from 'react';

import { ButtonProps } from './index';

export const ButtonSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ButtonProps,
  }));

  return (
    <div className="space-y-6 p-4">
      <div className="border-b border-gray-200 pb-2">
        <h3 className="text-lg font-medium text-gray-900">按钮设置</h3>
        <p className="text-sm text-gray-500">配置按钮的属性和样式</p>
      </div>

      <div className="space-y-4">
        {/* 按钮文本 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            按钮文本 <span className="text-red-500">*</span>
          </label>
          <Input
            onChange={(e) =>
              setProp((props: ButtonProps) => {
                props.text = e.target.value;
              })
            }
            placeholder="请输入按钮文本"
            value={props.text || ''}
          />
        </div>

        {/* 按钮样式 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            按钮样式
          </label>
          <Select
            onValueChange={(value) =>
              setProp((props: ButtonProps) => {
                props.variant = value as ButtonProps['variant'];
              })
            }
            value={props.variant || 'default'}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择按钮样式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">默认</SelectItem>
              <SelectItem value="destructive">危险</SelectItem>
              <SelectItem value="outline">边框</SelectItem>
              <SelectItem value="secondary">次要</SelectItem>
              <SelectItem value="ghost">幽灵</SelectItem>
              <SelectItem value="link">链接</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 按钮大小 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            按钮大小
          </label>
          <Select
            onValueChange={(value) =>
              setProp((props: ButtonProps) => {
                props.size = value as ButtonProps['size'];
              })
            }
            value={props.size || 'default'}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择按钮大小" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sm">小</SelectItem>
              <SelectItem value="default">默认</SelectItem>
              <SelectItem value="lg">大</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 禁用状态 */}
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              checked={props.disabled || false}
              className="rounded border-gray-300"
              onChange={(e) =>
                setProp((props: ButtonProps) => {
                  props.disabled = e.target.checked;
                })
              }
              type="checkbox"
            />
            <span className="text-sm font-medium text-gray-700">禁用按钮</span>
          </label>
        </div>

        {/* 自定义CSS类名 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            自定义样式类名
          </label>
          <Input
            onChange={(e) =>
              setProp((props: ButtonProps) => {
                props.className = e.target.value;
              })
            }
            placeholder="例如: my-custom-button-class"
            value={props.className || ''}
          />
          <p className="text-xs text-gray-500">可以添加自定义的CSS类名</p>
        </div>
      </div>
    </div>
  );
};
