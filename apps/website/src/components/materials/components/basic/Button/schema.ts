import { ComponentSchema } from '../../../types/component';

/**
 * Button组件的配置Schema
 */
export const buttonSchema: ComponentSchema = {
  type: 'object',
  properties: {
    text: {
      type: 'string',
      title: '按钮文本',
      description: '显示在按钮上的文本内容',
      default: '按钮',
    },
    variant: {
      type: 'select',
      title: '按钮样式',
      description: '选择按钮的视觉样式',
      default: 'default',
      enum: [
        { label: '默认', value: 'default' },
        { label: '危险', value: 'destructive' },
        { label: '边框', value: 'outline' },
        { label: '次要', value: 'secondary' },
        { label: '幽灵', value: 'ghost' },
        { label: '链接', value: 'link' },
      ],
    },
    size: {
      type: 'select',
      title: '按钮大小',
      description: '选择按钮的尺寸',
      default: 'default',
      enum: [
        { label: '小', value: 'sm' },
        { label: '默认', value: 'default' },
        { label: '大', value: 'lg' },
      ],
    },
    disabled: {
      type: 'boolean',
      title: '禁用状态',
      description: '是否禁用按钮',
      default: false,
    },
    className: {
      type: 'string',
      title: '自定义样式类名',
      description: '添加自定义的CSS类名',
      default: '',
    },
  },
  required: ['text'],
};
