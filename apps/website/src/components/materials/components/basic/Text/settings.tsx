'use client';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNode } from '@craftjs/core';
import React from 'react';

import { TextProps } from './index';

export const TextSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as TextProps,
  }));

  return (
    <div className="space-y-6 p-4">
      <div className="border-b border-gray-200 pb-2">
        <h3 className="text-lg font-medium text-gray-900">文本设置</h3>
        <p className="text-sm text-gray-500">配置文本的内容和样式</p>
      </div>

      <div className="space-y-4">
        {/* 文本内容 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            文本内容 <span className="text-red-500">*</span>
          </label>
          <Input
            onChange={(e) =>
              setProp((props: TextProps) => {
                props.text = e.target.value;
              })
            }
            placeholder="请输入文本内容"
            value={props.text || ''}
          />
        </div>

        {/* 字体大小 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            字体大小 (px)
          </label>
          <Input
            max={72}
            min={8}
            onChange={(e) =>
              setProp((props: TextProps) => {
                props.fontSize = Number(e.target.value);
              })
            }
            type="number"
            value={props.fontSize || 16}
          />
        </div>

        {/* 字体粗细 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            字体粗细
          </label>
          <Select
            onValueChange={(value) =>
              setProp((props: TextProps) => {
                props.fontWeight = value;
              })
            }
            value={props.fontWeight || '400'}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择字体粗细" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="300">细</SelectItem>
              <SelectItem value="400">普通</SelectItem>
              <SelectItem value="500">中等</SelectItem>
              <SelectItem value="600">半粗</SelectItem>
              <SelectItem value="700">粗</SelectItem>
              <SelectItem value="800">特粗</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 文本颜色 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            文本颜色
          </label>
          <div className="flex items-center space-x-2">
            <input
              className="h-8 w-8 rounded border"
              onChange={(e) =>
                setProp((props: TextProps) => {
                  props.color = e.target.value;
                })
              }
              type="color"
              value={props.color || '#000000'}
            />
            <Input
              onChange={(e) =>
                setProp((props: TextProps) => {
                  props.color = e.target.value;
                })
              }
              placeholder="#000000"
              value={props.color || '#000000'}
            />
          </div>
        </div>

        {/* 文本对齐 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            文本对齐
          </label>
          <Select
            onValueChange={(value) =>
              setProp((props: TextProps) => {
                props.textAlign = value as TextProps['textAlign'];
              })
            }
            value={props.textAlign || 'left'}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择对齐方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">左对齐</SelectItem>
              <SelectItem value="center">居中</SelectItem>
              <SelectItem value="right">右对齐</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 自定义CSS类名 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            自定义样式类名
          </label>
          <Input
            onChange={(e) =>
              setProp((props: TextProps) => {
                props.className = e.target.value;
              })
            }
            placeholder="例如: my-custom-text-class"
            value={props.className || ''}
          />
          <p className="text-xs text-gray-500">可以添加自定义的CSS类名</p>
        </div>
      </div>
    </div>
  );
};
