import { ComponentSchema } from '../../../types/component';

/**
 * Text组件的配置Schema
 */
export const textSchema: ComponentSchema = {
  type: 'object',
  properties: {
    text: {
      type: 'string',
      title: '文本内容',
      description: '显示的文本内容',
      default: '文本内容',
    },
    fontSize: {
      type: 'number',
      title: '字体大小',
      description: '字体大小，单位：像素',
      default: 16,
      min: 8,
      max: 72,
    },
    fontWeight: {
      type: 'select',
      title: '字体粗细',
      description: '选择字体的粗细程度',
      default: '400',
      enum: [
        { label: '细', value: '300' },
        { label: '普通', value: '400' },
        { label: '中等', value: '500' },
        { label: '半粗', value: '600' },
        { label: '粗', value: '700' },
        { label: '特粗', value: '800' },
      ],
    },
    color: {
      type: 'color',
      title: '文本颜色',
      description: '文本的颜色',
      default: '#000000',
    },
    textAlign: {
      type: 'select',
      title: '文本对齐',
      description: '文本的对齐方式',
      default: 'left',
      enum: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' },
      ],
    },
    className: {
      type: 'string',
      title: '自定义样式类名',
      description: '添加自定义的CSS类名',
      default: '',
    },
  },
  required: ['text'],
};
