'use client';

import { useNode } from '@craftjs/core';
import React from 'react';

/**
 * Text组件的属性接口
 */
export interface TextProps {
  text: string;
  fontSize: number;
  fontWeight: string;
  color: string;
  textAlign: 'center' | 'left' | 'right';
  className?: string;
}

/**
 * CraftJS组件类型
 */
type CraftComponent = React.FC<TextProps> & {
  craft: {
    custom: {
      displayName: string;
    };
    props: Partial<TextProps>;
    related: {
      settings: () => Promise<any>;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };
};

/**
 * 用于编辑器的Text组件
 */
export const MaterialText: CraftComponent = ({
  text = '文本内容',
  fontSize = 16,
  fontWeight = '400',
  color = '#000000',
  textAlign = 'left',
  className = '',
}) => {
  const {
    connectors: { connect, drag },
    selected,
  } = useNode((state) => ({
    selected: state.events.selected,
  }));

  return (
    <p
      className={` ${className} ${selected ? 'ring-2 ring-blue-500 ring-offset-2' : ''} cursor-move transition-all duration-200`}
      ref={(ref: HTMLParagraphElement | null) => {
        if (ref) {
          connect(drag(ref));
        }
        return undefined;
      }}
      style={{
        fontSize: `${fontSize}px`,
        fontWeight,
        color,
        textAlign,
      }}
    >
      {text}
    </p>
  );
};

/**
 * 用于用户网站的纯Text组件（预览组件）
 */
export const TextPreview: React.FC<TextProps> = ({
  text = '文本内容',
  fontSize = 16,
  fontWeight = '400',
  color = '#000000',
  textAlign = 'left',
  className = '',
}) => {
  return (
    <p
      className={className}
      style={{
        fontSize: `${fontSize}px`,
        fontWeight,
        color,
        textAlign,
      }}
    >
      {text}
    </p>
  );
};

// CraftJS配置
MaterialText.craft = {
  props: {
    text: '文本内容',
    fontSize: 16,
    fontWeight: '400',
    color: '#000000',
    textAlign: 'left',
    className: '',
  },
  related: {
    settings: () => import('./settings').then((comp) => comp.TextSettings),
  },
  rules: {
    canDrag: true,
    canDrop: false,
    canMoveIn: false,
    canMoveOut: true,
  },
  custom: {
    displayName: '文本',
  },
};
