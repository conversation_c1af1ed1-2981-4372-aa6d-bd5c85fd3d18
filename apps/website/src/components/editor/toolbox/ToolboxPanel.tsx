'use client';

import { materialRegistry } from '@/components/materials/registry';
import { basicComponents } from '@/components/materials/registry/basic';
import { layoutComponents } from '@/components/materials/registry/layout';
import { useEditor } from '@craftjs/core';
import React from 'react';

export const ToolboxPanel: React.FC = () => {
  const { connectors } = useEditor();

  // 注册所有物料组件
  React.useEffect(() => {
    materialRegistry.registerBatch([...basicComponents, ...layoutComponents]);
  }, []);

  // 获取分类后的工具箱数据
  const toolboxData = materialRegistry.getToolboxData();

  return (
    <div className="h-full bg-gray-50">
      <div className="border-b border-gray-200 p-4">
        <h2 className="text-lg font-semibold text-gray-900">组件工具箱</h2>
        <p className="text-sm text-gray-500">拖拽组件到画布中开始设计</p>
      </div>

      <div className="space-y-6 p-4">
        {toolboxData.map((categoryData) => (
          <div className="space-y-3" key={categoryData.category}>
            <h3 className="text-sm font-medium uppercase tracking-wide text-gray-700">
              {categoryData.category === 'basic' && '基础组件'}
              {categoryData.category === 'layout' && '布局组件'}
              {categoryData.category === 'advanced' && '高级组件'}
              {categoryData.category === 'custom' && '自定义组件'}
            </h3>

            <div className="grid grid-cols-2 gap-2">
              {categoryData.items.map((item) => (
                <div
                  className="group flex h-16 cursor-grab flex-col items-center justify-center rounded-lg border border-gray-200 bg-white transition-all duration-200 hover:border-gray-300 hover:bg-gray-50 active:cursor-grabbing"
                  key={item.id}
                  ref={(ref) => {
                    if (ref) {
                      connectors.create(
                        ref,
                        React.createElement(item.component, item.props),
                      );
                    }
                  }}
                >
                  <span className="mb-1 text-lg">{item.icon}</span>
                  <span className="text-center text-xs text-gray-600 group-hover:text-gray-800">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* 统计信息 */}
        <div className="mt-6 border-t border-gray-200 pt-4">
          <div className="text-xs text-gray-500">
            已加载 {materialRegistry.size()} 个组件
          </div>
        </div>
      </div>
    </div>
  );
};
