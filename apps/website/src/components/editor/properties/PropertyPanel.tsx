'use client';

import { useEditor } from '@craftjs/core';
import React from 'react';

export const PropertyPanel: React.FC = () => {
  const { selected } = useEditor((state) => ({
    selected: state.events.selected,
  }));

  return (
    <div className="h-full bg-white">
      <div className="border-b border-gray-200 p-4">
        <h2 className="text-lg font-semibold text-gray-900">属性面板</h2>
        <p className="text-sm text-gray-500">编辑选中组件的属性</p>
      </div>

      <div className="flex-1">
        {selected.size > 0 ? (
          <PropertyContent />
        ) : (
          <div className="p-4 text-center text-gray-500">
            <div className="mb-4">
              <svg
                className="mx-auto h-12 w-12 text-gray-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                />
              </svg>
            </div>
            <p className="text-sm">请选择一个组件来编辑其属性</p>
          </div>
        )}
      </div>
    </div>
  );
};

const PropertyContent: React.FC = () => {
  const { selected } = useEditor((state) => ({
    selected: state.events.selected,
  }));

  // 如果没有选中组件，显示提示
  if (selected.size === 0) {
    return (
      <div className="p-4">
        <div className="space-y-4">
          <div className="rounded-lg border border-gray-200 bg-gray-50 p-3">
            <h3 className="font-medium text-gray-900">未选中组件</h3>
            <p className="mt-1 text-sm text-gray-700">
              请在画布中选择一个组件来编辑其属性。
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 返回选中组件的设置界面
  return (
    <div className="p-4">
      <div className="space-y-4">
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
          <h3 className="font-medium text-blue-900">组件已选中</h3>
          <p className="mt-1 text-sm text-blue-700">
            物料组件系统已集成，组件设置面板将在这里显示。
          </p>
        </div>

        <div className="text-sm text-gray-500">
          <span className="font-medium">选中组件数量:</span> {selected.size}
        </div>
      </div>
    </div>
  );
};
