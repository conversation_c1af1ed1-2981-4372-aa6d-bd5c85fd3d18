import { ReactNode } from 'react';

// 编辑器配置接口
export interface EditorConfig {
  enabled: boolean;
  locale: string;
  siteId: string;
}

// 组件基础接口
export interface BaseComponent {
  id: string;
  type: string;
  displayName: string;
  props: Record<string, any>;
  children?: BaseComponent[];
}

// 用户组件接口
export interface UserComponent {
  component: React.ComponentType<any>;
  craft: {
    custom?: {
      category?: string;
      displayName?: string;
      icon?: ReactNode;
    };
    props: Record<string, any>;
    related?: {
      settings?: React.ComponentType<any>;
      toolbar?: React.ComponentType<any>;
    };
    rules?: {
      canDrag?: boolean;
      canDrop?: boolean;
      canMoveIn?: boolean;
      canMoveOut?: boolean;
    };
  };
}

// 工具箱项目接口
export interface ToolboxItem {
  id: string;
  name: string;
  icon: ReactNode;
  category: string;
  component: React.ComponentType<any>;
  defaultProps?: Record<string, any>;
}

// 编辑器状态接口
export interface EditorState {
  selectedNodeId?: string;
  hoveredNodeId?: string;
  enabled: boolean;
  isDragging: boolean;
}

// 面板类型
export type PanelType = 'layers' | 'preview' | 'properties' | 'toolbox';

// 组件分类
export type ComponentCategory =
  | 'content'
  | 'form'
  | 'interactive'
  | 'layout'
  | 'media';
