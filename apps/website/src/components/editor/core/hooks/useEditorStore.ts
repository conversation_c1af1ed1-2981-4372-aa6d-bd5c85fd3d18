import { EditorState, PanelType } from '../types';

interface EditorStore extends EditorState {
  // 面板状态
  activePanel: PanelType;
  sidebarCollapsed: boolean;
  toolboxCollapsed: boolean;

  // 编辑器操作
  setEnabled: (enabled: boolean) => void;
  setSelectedNode: (nodeId?: string) => void;
  setHoveredNode: (nodeId?: string) => void;
  setDragging: (isDragging: boolean) => void;

  // 面板操作
  setActivePanel: (panel: PanelType) => void;
  toggleSidebar: () => void;
  toggleToolbox: () => void;

  // 快捷键操作
  copy: () => void;
  paste: () => void;
  delete: () => void;
  undo: () => void;
  redo: () => void;
}

// 简化版状态管理，避免依赖zustand
const editorState: EditorStore = {
  // 初始状态
  selectedNodeId: undefined,
  hoveredNodeId: undefined,
  enabled: true,
  isDragging: false,
  activePanel: 'toolbox',
  sidebarCollapsed: false,
  toolboxCollapsed: false,

  // 编辑器操作
  setEnabled: (enabled: boolean) => {
    editorState.enabled = enabled;
  },
  setSelectedNode: (nodeId?: string) => {
    editorState.selectedNodeId = nodeId;
  },
  setHoveredNode: (nodeId?: string) => {
    editorState.hoveredNodeId = nodeId;
  },
  setDragging: (isDragging: boolean) => {
    editorState.isDragging = isDragging;
  },

  // 面板操作
  setActivePanel: (panel: PanelType) => {
    editorState.activePanel = panel;
  },
  toggleSidebar: () => {
    editorState.sidebarCollapsed = !editorState.sidebarCollapsed;
  },
  toggleToolbox: () => {
    editorState.toolboxCollapsed = !editorState.toolboxCollapsed;
  },

  // 快捷键操作 (待实现)
  copy: () => {
    // TODO: 实现复制功能
  },
  paste: () => {
    // TODO: 实现粘贴功能
  },
  delete: () => {
    // TODO: 实现删除功能
  },
  undo: () => {
    // TODO: 实现撤销功能
  },
  redo: () => {
    // TODO: 实现重做功能
  },
};

export const useEditorStore = () => editorState;
