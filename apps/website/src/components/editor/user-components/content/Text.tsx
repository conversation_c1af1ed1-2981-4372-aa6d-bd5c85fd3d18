import { useNode } from '@craftjs/core';
import React from 'react';

export interface TextProps {
  text?: string;
  fontSize?: number;
  fontWeight?:
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900'
    | 'bold'
    | 'normal';
  color?: { a: number; b: number; g: number; r: number };
  textAlign?: 'center' | 'justify' | 'left' | 'right';
  margin?: (number | string)[] | number | string;
  padding?: (number | string)[] | number | string;
  lineHeight?: number;
  custom?: {
    displayName?: string;
  };
}

const TextComponent: React.FC<TextProps> = ({
  text = '请输入文本',
  fontSize = 14,
  fontWeight = 'normal',
  color = { r: 0, g: 0, b: 0, a: 1 },
  textAlign = 'left',
  margin = 0,
  padding = 0,
  lineHeight = 1.5,
  ...props
}) => {
  const {
    connectors: { connect, drag },
    selected,
    actions: { setProp },
  } = useNode((state) => ({
    selected: state.events.selected,
  }));

  const textColor = `rgba(${color.r}, ${color.g}, ${color.b}, ${color.a})`;

  // 处理spacing数组格式
  const formatSpacing = (spacing: (number | string)[] | number | string) => {
    if (Array.isArray(spacing)) {
      return spacing
        .map((s) => (typeof s === 'number' ? `${s}px` : s))
        .join(' ');
    }
    return typeof spacing === 'number' ? `${spacing}px` : spacing;
  };

  return (
    <div
      {...props}
      onClick={(e) => {
        e.stopPropagation();
      }}
      ref={(ref) => {
        if (ref) {
          connect(drag(ref));
        }
      }}
      style={{
        margin: formatSpacing(margin),
        padding: formatSpacing(padding),
        display: 'inline-block',
        width: '100%',
      }}
    >
      {selected ? (
        <textarea
          autoFocus
          onBlur={(e) => {
            e.stopPropagation();
          }}
          onChange={(e) =>
            setProp((props: TextProps) => {
              props.text = e.target.value;
            })
          }
          style={{
            fontSize: `${fontSize}px`,
            fontWeight,
            color: textColor,
            textAlign,
            lineHeight,
            width: '100%',
            border: '1px dashed #ccc',
            backgroundColor: 'transparent',
            resize: 'none',
            outline: 'none',
            fontFamily: 'inherit',
          }}
          value={text}
        />
      ) : (
        <p
          dangerouslySetInnerHTML={{ __html: text }}
          style={{
            fontSize: `${fontSize}px`,
            fontWeight,
            color: textColor,
            textAlign,
            lineHeight,
            margin: 0,
            cursor: 'pointer',
          }}
        />
      )}
    </div>
  );
};

export const TextSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as TextProps,
  }));

  const currentColor = props.color || { r: 0, g: 0, b: 0, a: 1 };

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-2 block text-sm font-medium">文本内容</label>
        <textarea
          className="w-full resize-none rounded border px-3 py-2"
          onChange={(e) =>
            setProp((props: TextProps) => (props.text = e.target.value))
          }
          placeholder="请输入文本内容"
          rows={3}
          value={props.text}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">字体大小</label>
        <input
          className="w-full rounded border px-3 py-2"
          max="72"
          min="8"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.fontSize = Number.parseInt(e.target.value)),
            )
          }
          type="number"
          value={props.fontSize}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">字体粗细</label>
        <select
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.fontWeight = e.target.value as TextProps['fontWeight']),
            )
          }
          value={props.fontWeight}
        >
          <option value="normal">正常</option>
          <option value="bold">粗体</option>
          <option value="100">100</option>
          <option value="200">200</option>
          <option value="300">300</option>
          <option value="400">400</option>
          <option value="500">500</option>
          <option value="600">600</option>
          <option value="700">700</option>
          <option value="800">800</option>
          <option value="900">900</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">文字颜色</label>
        <input
          className="h-10 w-full rounded border"
          onChange={(e) => {
            const hex = e.target.value;
            const r = Number.parseInt(hex.slice(1, 3), 16);
            const g = Number.parseInt(hex.slice(3, 5), 16);
            const b = Number.parseInt(hex.slice(5, 7), 16);
            setProp((props: TextProps) => {
              props.color = { r, g, b, a: currentColor.a };
            });
          }}
          type="color"
          value={`#${Math.round(currentColor.r).toString(16).padStart(2, '0')}${Math.round(currentColor.g).toString(16).padStart(2, '0')}${Math.round(currentColor.b).toString(16).padStart(2, '0')}`}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">文字对齐</label>
        <select
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.textAlign = e.target.value as TextProps['textAlign']),
            )
          }
          value={props.textAlign}
        >
          <option value="left">左对齐</option>
          <option value="center">居中对齐</option>
          <option value="right">右对齐</option>
          <option value="justify">两端对齐</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">行高</label>
        <input
          className="w-full rounded border px-3 py-2"
          max="3"
          min="1"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.lineHeight = Number.parseFloat(e.target.value)),
            )
          }
          step="0.1"
          type="number"
          value={props.lineHeight}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">内边距 (px)</label>
        <input
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.padding = Number.parseInt(e.target.value)),
            )
          }
          type="number"
          value={
            Array.isArray(props.padding) ? props.padding[0] : props.padding
          }
        />
      </div>
    </div>
  );
};

export const TextDefaultProps: TextProps = {
  text: '请输入文本',
  fontSize: 14,
  fontWeight: 'normal',
  color: { r: 0, g: 0, b: 0, a: 1 },
  textAlign: 'left',
  margin: 0,
  padding: 0,
  lineHeight: 1.5,
  custom: {
    displayName: '文本',
  },
};

// 使用类型断言来添加 craft 属性
export const Text = TextComponent as typeof TextComponent & {
  craft: {
    props: TextProps;
    related: {
      settings: React.ComponentType;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };
};

Text.craft = {
  props: TextDefaultProps,
  related: {
    settings: TextSettings,
  },
  rules: {
    canDrag: true,
    canDrop: false,
    canMoveIn: false,
    canMoveOut: true,
  },
};
