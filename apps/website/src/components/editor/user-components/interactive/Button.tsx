import { Button as UIButton } from '@/components/ui';
import { cn } from '@/lib/utils';
import { useNode } from '@craftjs/core';
import React from 'react';

export interface ButtonProps {
  text?: string;
  size?: 'default' | 'lg' | 'sm' | 'xl';
  variant?:
    | 'default'
    | 'destructive'
    | 'ghost'
    | 'link'
    | 'outline'
    | 'secondary'
    | 'success'
    | 'warning';
  disabled?: boolean;
  className?: string;
  custom?: {
    displayName?: string;
  };
}

const ButtonComponent: React.FC<ButtonProps> = ({
  text = '按钮',
  size = 'default',
  variant = 'default',
  disabled = false,
  className,
  custom: _custom,
  ...props
}) => {
  const {
    connectors: { connect, drag },
    selected,
    dragged,
  } = useNode((state) => ({
    selected: state.events.selected,
    dragged: state.events.dragged,
  }));

  return (
    <div
      {...props}
      className={cn(
        'inline-block',
        selected && 'ring-2 ring-blue-500 ring-offset-2',
        dragged && 'opacity-50',
        className,
      )}
      ref={(ref) => {
        if (ref) {
          connect(drag(ref));
        }
      }}
    >
      <UIButton
        disabled={disabled}
        onClick={(e) => {
          // 在编辑模式下阻止默认点击行为
          e.preventDefault();
        }}
        size={size}
        variant={variant}
      >
        {text}
      </UIButton>
    </div>
  );
};

export const ButtonSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ButtonProps,
  }));

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-2 block text-sm font-medium">按钮文本</label>
        <input
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp((props: ButtonProps) => (props.text = e.target.value))
          }
          placeholder="按钮文本"
          type="text"
          value={props.text}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">按钮大小</label>
        <select
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: ButtonProps) =>
                (props.size = e.target.value as ButtonProps['size']),
            )
          }
          value={props.size}
        >
          <option value="sm">小</option>
          <option value="default">默认</option>
          <option value="lg">大</option>
          <option value="xl">超大</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">按钮样式</label>
        <select
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: ButtonProps) =>
                (props.variant = e.target.value as ButtonProps['variant']),
            )
          }
          value={props.variant}
        >
          <option value="default">默认</option>
          <option value="destructive">危险</option>
          <option value="outline">轮廓</option>
          <option value="secondary">次要</option>
          <option value="ghost">幽灵</option>
          <option value="link">链接</option>
          <option value="success">成功</option>
          <option value="warning">警告</option>
        </select>
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            checked={props.disabled}
            onChange={(e) =>
              setProp(
                (props: ButtonProps) => (props.disabled = e.target.checked),
              )
            }
            type="checkbox"
          />
          <span className="text-sm">禁用状态</span>
        </label>
      </div>
    </div>
  );
};

export const ButtonDefaultProps: ButtonProps = {
  text: '按钮',
  size: 'default',
  variant: 'default',
  disabled: false,
  custom: {
    displayName: '按钮',
  },
};

// 使用类型断言来添加 craft 属性
export const Button = ButtonComponent as typeof ButtonComponent & {
  craft: {
    props: ButtonProps;
    related: {
      settings: React.ComponentType;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };
};

Button.craft = {
  props: ButtonDefaultProps,
  related: {
    settings: ButtonSettings,
  },
  rules: {
    canDrag: true,
    canDrop: false,
    canMoveIn: false,
    canMoveOut: true,
  },
};
