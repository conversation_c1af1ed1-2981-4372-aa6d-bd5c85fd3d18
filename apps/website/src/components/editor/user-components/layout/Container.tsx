import { useNode } from '@craftjs/core';
import React from 'react';

export interface ContainerProps {
  background?: { a: number; b: number; g: number; r: number };
  padding?: (number | string)[] | number | string;
  margin?: (number | string)[] | number | string;
  width?: number | string;
  height?: number | string;
  flexDirection?: 'column' | 'row';
  alignItems?: 'center' | 'flex-end' | 'flex-start' | 'stretch';
  justifyContent?:
    | 'center'
    | 'flex-end'
    | 'flex-start'
    | 'space-around'
    | 'space-between';
  shadow?: number;
  children?: React.ReactNode;
  custom?: {
    displayName?: string;
  };
}

const ContainerComponent: React.FC<ContainerProps> = ({
  background = { r: 255, g: 255, b: 255, a: 1 },
  padding = 0,
  margin = 0,
  width = 'auto',
  height = 'auto',
  flexDirection = 'column',
  alignItems = 'flex-start',
  justifyContent = 'flex-start',
  shadow = 0,
  children,
  ...props
}) => {
  const {
    connectors: { connect, drag },
  } = useNode();

  const backgroundColor = `rgba(${background.r}, ${background.g}, ${background.b}, ${background.a})`;
  const boxShadow =
    shadow > 0 ? `0px ${shadow}px ${shadow * 2}px rgba(0, 0, 0, 0.1)` : 'none';

  // 处理padding和margin数组格式
  const formatSpacing = (spacing: (number | string)[] | number | string) => {
    if (Array.isArray(spacing)) {
      return spacing
        .map((s) => (typeof s === 'number' ? `${s}px` : s))
        .join(' ');
    }
    return typeof spacing === 'number' ? `${spacing}px` : spacing;
  };

  return (
    <div
      {...props}
      ref={(ref) => {
        if (ref) {
          connect(drag(ref));
        }
      }}
      style={{
        backgroundColor,
        padding: formatSpacing(padding),
        margin: formatSpacing(margin),
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
        display: 'flex',
        flexDirection,
        alignItems,
        justifyContent,
        boxShadow,
        minHeight: '50px',
        position: 'relative',
      }}
    >
      {children}
    </div>
  );
};

export const ContainerSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ContainerProps,
  }));

  const currentBackground = props.background || {
    r: 255,
    g: 255,
    b: 255,
    a: 1,
  };

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-2 block text-sm font-medium">背景颜色</label>
        <input
          className="h-10 w-full rounded border"
          onChange={(e) => {
            const hex = e.target.value;
            const r = Number.parseInt(hex.slice(1, 3), 16);
            const g = Number.parseInt(hex.slice(3, 5), 16);
            const b = Number.parseInt(hex.slice(5, 7), 16);
            setProp((props: ContainerProps) => {
              props.background = { r, g, b, a: currentBackground.a };
            });
          }}
          type="color"
          value={`#${Math.round(currentBackground.r).toString(16).padStart(2, '0')}${Math.round(currentBackground.g).toString(16).padStart(2, '0')}${Math.round(currentBackground.b).toString(16).padStart(2, '0')}`}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">内边距 (px)</label>
        <input
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.padding = Number.parseInt(e.target.value)),
            )
          }
          type="number"
          value={
            Array.isArray(props.padding) ? props.padding[0] : props.padding
          }
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">宽度</label>
        <input
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp((props: ContainerProps) => (props.width = e.target.value))
          }
          placeholder="auto, 100%, 300px"
          type="text"
          value={props.width}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">高度</label>
        <input
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp((props: ContainerProps) => (props.height = e.target.value))
          }
          placeholder="auto, 100%, 300px"
          type="text"
          value={props.height}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium">排列方向</label>
        <select
          className="w-full rounded border px-3 py-2"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.flexDirection = e.target.value as 'column' | 'row'),
            )
          }
          value={props.flexDirection}
        >
          <option value="column">垂直</option>
          <option value="row">水平</option>
        </select>
      </div>
    </div>
  );
};

export const ContainerDefaultProps: ContainerProps = {
  background: { r: 255, g: 255, b: 255, a: 1 },
  padding: 20,
  margin: 0,
  width: 'auto',
  height: 'auto',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  shadow: 0,
  custom: {
    displayName: '容器',
  },
};

// 使用类型断言来添加 craft 属性
export const Container = ContainerComponent as typeof ContainerComponent & {
  craft: {
    props: ContainerProps;
    related: {
      settings: React.ComponentType;
    };
    rules: {
      canDrag: boolean;
      canDrop: boolean;
      canMoveIn: boolean;
      canMoveOut: boolean;
    };
  };
};

Container.craft = {
  props: ContainerDefaultProps,
  related: {
    settings: ContainerSettings,
  },
  rules: {
    canDrag: true,
    canDrop: true,
    canMoveIn: true,
    canMoveOut: true,
  },
};
