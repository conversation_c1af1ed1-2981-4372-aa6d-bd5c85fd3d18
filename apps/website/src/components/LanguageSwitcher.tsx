'use client';

import type { Locale } from '../lib/i18n';

import { Check, ChevronDown, Globe } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';

import { localeConfig } from '../lib/i18n';
import { useLocaleSwitch } from '../lib/locale-utils';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'compact' | 'default' | 'icon-only';
  align?: 'left' | 'right';
}

export function LanguageSwitcher({
  className = '',
  variant = 'default',
  align = 'left',
}: LanguageSwitcherProps) {
  const t = useTranslations('language');
  const { currentLocale, switchLocale, availableLocales } = useLocaleSwitch();
  const [isOpen, setIsOpen] = useState(false);

  const currentLocaleConfig = localeConfig[currentLocale];

  const handleLocaleChange = (locale: Locale) => {
    switchLocale(locale);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  const renderTrigger = () => {
    switch (variant) {
      case 'compact': {
        return (
          <button
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            className={`focus:ring-primary-500 inline-flex items-center gap-2 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 transition-colors duration-200 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 ${className} `}
            onClick={toggleDropdown}
            onKeyDown={handleKeyDown}
            type="button"
          >
            <span className="text-lg">{currentLocaleConfig.flag}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </button>
        );
      }

      case 'icon-only': {
        return (
          <button
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            aria-label={t('switch')}
            className={`focus:ring-primary-500 inline-flex h-10 w-10 items-center justify-center rounded-md bg-gray-100 text-gray-700 transition-colors duration-200 hover:bg-gray-200 hover:text-gray-900 focus:outline-none focus:ring-2 ${className} `}
            onClick={toggleDropdown}
            onKeyDown={handleKeyDown}
            type="button"
          >
            <Globe className="h-5 w-5" />
          </button>
        );
      }

      default: {
        return (
          <button
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            className={`focus:ring-primary-500 inline-flex min-w-[140px] items-center gap-3 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 ${className} `}
            onClick={toggleDropdown}
            onKeyDown={handleKeyDown}
            type="button"
          >
            <Globe className="h-4 w-4" />
            <span className="flex-1 text-left">
              {currentLocaleConfig.nativeName}
            </span>
            <ChevronDown
              className={`h-4 w-4 transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </button>
        );
      }
    }
  };

  return (
    <div className="relative">
      {renderTrigger()}

      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            aria-hidden="true"
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* 下拉菜单 */}
          <div
            aria-label={t('switch')}
            className={`absolute z-20 mt-2 min-w-[180px] max-w-[240px] rounded-md border border-gray-200 bg-white py-1 shadow-lg ${align === 'right' ? 'right-0' : 'left-0'} `}
            role="listbox"
          >
            {availableLocales.map((locale) => {
              const config = localeConfig[locale];
              const isSelected = locale === currentLocale;

              return (
                <button
                  aria-selected={isSelected}
                  className={`flex w-full items-center gap-3 px-4 py-2 text-left text-sm transition-colors duration-150 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                    isSelected
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-gray-700'
                  } `}
                  key={locale}
                  onClick={() => handleLocaleChange(locale)}
                  role="option"
                  type="button"
                >
                  <span className="text-lg">{config.flag}</span>
                  <div className="flex-1">
                    <div className="font-medium">{config.nativeName}</div>
                    {variant === 'default' && (
                      <div className="text-xs text-gray-500">{config.name}</div>
                    )}
                  </div>
                  {isSelected && <Check className="text-primary-600 h-4 w-4" />}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

/**
 * 简化的语言切换器（仅显示国旗）
 */
export function SimpleLanguageSwitcher({
  className = '',
}: {
  className?: string;
}) {
  return <LanguageSwitcher className={className} variant="icon-only" />;
}

/**
 * 紧凑的语言切换器（国旗+下拉箭头）
 */
export function CompactLanguageSwitcher({
  className = '',
  align = 'left',
}: {
  align?: 'left' | 'right';
  className?: string;
}) {
  return (
    <LanguageSwitcher align={align} className={className} variant="compact" />
  );
}

/**
 * 移动端语言切换器
 */
export function MobileLanguageSwitcher() {
  const t = useTranslations('language');
  const { currentLocale, switchLocale, availableLocales } = useLocaleSwitch();
  const [isOpen, setIsOpen] = useState(false);

  const handleLocaleChange = (locale: Locale) => {
    switchLocale(locale);
    setIsOpen(false);
  };

  if (isOpen) {
    return (
      <div className="fixed inset-0 z-50 bg-white">
        {/* 头部 */}
        <div className="flex items-center justify-between border-b border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900">{t('switch')}</h2>
          <button
            aria-label="关闭"
            className="p-2 text-gray-400 hover:text-gray-600"
            onClick={() => setIsOpen(false)}
            type="button"
          >
            <span className="sr-only">关闭</span>
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M6 18L18 6M6 6l12 12"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </button>
        </div>

        {/* 语言列表 */}
        <div className="divide-y divide-gray-200">
          {availableLocales.map((locale) => {
            const config = localeConfig[locale];
            const isSelected = locale === currentLocale;

            return (
              <button
                className={`flex w-full items-center gap-4 p-4 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${isSelected ? 'bg-primary-50' : ''} `}
                key={locale}
                onClick={() => handleLocaleChange(locale)}
                type="button"
              >
                <span className="text-2xl">{config.flag}</span>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">
                    {config.nativeName}
                  </div>
                  <div className="text-sm text-gray-500">{config.name}</div>
                </div>
                {isSelected && <Check className="text-primary-600 h-5 w-5" />}
              </button>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <button
      className="inline-flex w-full items-center gap-3 p-3 text-left text-gray-700 hover:bg-gray-50"
      onClick={() => setIsOpen(true)}
      type="button"
    >
      <Globe className="h-5 w-5" />
      <span>{t('switch')}</span>
      <div className="ml-auto">
        <span className="text-lg">{localeConfig[currentLocale].flag}</span>
      </div>
    </button>
  );
}
