/**
 * 页面渲染器 - 用于渲染用户创建的网站页面
 */

import { Container, Text } from '@flexihub/website-components';
import React from 'react';

// 简化的类型定义
interface PageData {
  id: string;
  domain: string;
  title: string;
  content: any;
  settings: {
    locale: string;
    theme: string;
  };
}

interface PageRendererProps {
  pageData: PageData;
  className?: string;
}

/**
 * 组件映射表 - 将字符串类型映射到实际的React组件
 */
const componentMap = {
  Container,
  Text,
  // 后续可以添加更多组件
} as const;

type ComponentType = keyof typeof componentMap;

/**
 * 递归渲染组件树
 */
const renderComponent = (node: any, index: number): React.ReactNode => {
  if (!node || typeof node !== 'object') {
    return null;
  }

  const { type, props = {}, nodes = [] } = node;

  // 检查组件类型是否存在
  if (!type || !(type in componentMap)) {
    console.warn(`Unknown component type: ${type}`);
    return null;
  }

  const Component = componentMap[type as ComponentType];

  // 渲染子组件
  const children =
    nodes.length > 0
      ? nodes
          .map((childId: string, childIndex: number) =>
            renderComponent(node.linkedNodes?.[childId], childIndex),
          )
          .filter(Boolean)
      : undefined;

  return React.createElement(Component, { key: index, ...props }, children);
};

/**
 * 页面渲染器主组件
 */
export function PageRenderer({ pageData, className = '' }: PageRendererProps) {
  if (!pageData?.content) {
    return (
      <div
        className={`flex min-h-screen items-center justify-center ${className}`}
      >
        <div className="text-center text-gray-500">
          <h2 className="mb-2 text-2xl font-semibold">页面内容为空</h2>
          <p>请在编辑器中添加内容</p>
        </div>
      </div>
    );
  }

  try {
    // 解析页面内容
    const content =
      typeof pageData.content === 'string'
        ? JSON.parse(pageData.content)
        : pageData.content;

    // 渲染根节点
    const rootNodes = content.ROOT?.nodes || [];

    return (
      <div className={className}>
        {rootNodes.map((nodeId: string, index: number) => {
          const node = content[nodeId];
          return renderComponent(node, index);
        })}
      </div>
    );
  } catch (error) {
    console.error('页面渲染错误:', error);

    return (
      <div
        className={`flex min-h-screen items-center justify-center ${className}`}
      >
        <div className="text-center text-red-500">
          <h2 className="mb-2 text-2xl font-semibold">页面渲染失败</h2>
          <p>页面内容格式错误，请联系管理员</p>
        </div>
      </div>
    );
  }
}

export default PageRenderer;
