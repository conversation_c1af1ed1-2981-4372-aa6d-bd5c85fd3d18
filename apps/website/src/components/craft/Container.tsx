'use client';

import { cn } from '@/lib/utils';
import { useNode } from '@craftjs/core';
import React from 'react';

interface ContainerProps {
  children?: React.ReactNode;
  backgroundColor?: string;
  padding?: number;
  margin?: number;
  borderRadius?: number;
  className?: string;
}

// 容器设置面板
const ContainerSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as ContainerProps,
  }));

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          背景颜色
        </label>
        <input
          className="h-10 w-full cursor-pointer rounded-md border border-gray-300"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.backgroundColor = e.target.value),
            )
          }
          type="color"
          value={props.backgroundColor || '#ffffff'}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          内边距 (px)
        </label>
        <input
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          max="100"
          min="0"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.padding = Number.parseInt(e.target.value, 10)),
            )
          }
          type="number"
          value={props.padding || 20}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          外边距 (px)
        </label>
        <input
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          max="50"
          min="0"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.margin = Number.parseInt(e.target.value, 10)),
            )
          }
          type="number"
          value={props.margin || 0}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          圆角 (px)
        </label>
        <input
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          max="50"
          min="0"
          onChange={(e) =>
            setProp(
              (props: ContainerProps) =>
                (props.borderRadius = Number.parseInt(e.target.value, 10)),
            )
          }
          type="number"
          value={props.borderRadius || 0}
        />
      </div>
    </div>
  );
};

// 定义组件类型，包含craft属性
interface ContainerComponent extends React.FC<ContainerProps> {
  craft: {
    displayName: string;
    props: ContainerProps;
    related: {
      settings: React.ComponentType;
    };
    rules: {
      canDrag: () => boolean;
      canDrop: () => boolean;
      canMoveIn: () => boolean;
      canMoveOut: () => boolean;
    };
  };
}

export const Container = (({
  children,
  backgroundColor = '#ffffff',
  padding = 20,
  margin = 0,
  borderRadius = 0,
  className,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    dragged,
  } = useNode((state) => ({
    selected: state.events.selected,
    dragged: state.events.dragged,
  }));

  return (
    <div
      className={cn(
        'relative min-h-[50px] transition-all',
        selected && 'ring-2 ring-blue-500 ring-offset-2',
        dragged && 'opacity-50',
        className,
      )}
      ref={(ref) => {
        if (ref) {
          connect(drag(ref));
        }
      }}
      style={{
        backgroundColor,
        padding: `${padding}px`,
        margin: `${margin}px`,
        borderRadius: `${borderRadius}px`,
      }}
    >
      {children || (
        <div className="py-8 text-center text-gray-400">拖拽组件到此处</div>
      )}
    </div>
  );
}) as ContainerComponent;

// Craft.js 配置
Container.craft = {
  displayName: 'Container',
  props: {
    backgroundColor: '#ffffff',
    padding: 20,
    margin: 0,
    borderRadius: 0,
  },
  related: {
    settings: ContainerSettings,
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true,
  },
};

export { ContainerSettings };
