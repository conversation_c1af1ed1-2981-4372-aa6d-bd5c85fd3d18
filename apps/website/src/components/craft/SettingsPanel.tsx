'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  Input,
} from '@/components/ui';
import { useEditor } from '@craftjs/core';
import { Download, Palette, Settings, Upload, X } from 'lucide-react';
import React, { useState } from 'react';

interface SettingsPanelProps {
  isVisible?: boolean;
  onClose?: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isVisible = false,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<'export' | 'general' | 'theme'>(
    'general',
  );
  const { actions, query } = useEditor();

  const handleExportJSON = () => {
    const json = query.serialize();
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'page-design.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportJSON = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const text = await file.text();
        actions.deserialize(text);
      } catch (error) {
        console.error('导入失败:', error);
      }
    }
  };

  const handleClearCanvas = () => {
    actions.clearEvents();
    const nodes = query.getNodes();
    Object.keys(nodes).forEach((nodeId) => {
      if (nodeId !== 'ROOT') {
        actions.delete(nodeId);
      }
    });
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <Card className="max-h-[80vh] w-full max-w-2xl overflow-hidden">
        <CardHeader className="border-b border-gray-200">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">编辑器设置</CardTitle>
            <Button onClick={onClose} size="sm" variant="ghost">
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* 标签导航 */}
          <div className="mt-4 flex space-x-1">
            <button
              className={`rounded-md px-3 py-1 text-sm transition-colors ${
                activeTab === 'general'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              onClick={() => setActiveTab('general')}
            >
              常规设置
            </button>
            <button
              className={`rounded-md px-3 py-1 text-sm transition-colors ${
                activeTab === 'theme'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              onClick={() => setActiveTab('theme')}
            >
              主题配置
            </button>
            <button
              className={`rounded-md px-3 py-1 text-sm transition-colors ${
                activeTab === 'export'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              onClick={() => setActiveTab('export')}
            >
              导入导出
            </button>
          </div>
        </CardHeader>

        <CardContent className="overflow-y-auto p-6">
          {/* 常规设置 */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-4 text-lg font-medium">页面设置</h3>
                <div className="space-y-4">
                  <Input label="页面标题" placeholder="输入页面标题" />
                  <Input label="页面描述" placeholder="输入页面描述" />
                  <Input label="关键词" placeholder="输入关键词，用逗号分隔" />
                </div>
              </div>

              <div>
                <h3 className="mb-4 text-lg font-medium">画布操作</h3>
                <div className="space-y-3">
                  <Button
                    leftIcon={<Settings className="h-4 w-4" />}
                    onClick={handleClearCanvas}
                    variant="outline"
                  >
                    清空画布
                  </Button>
                  <p className="text-sm text-gray-500">
                    这将删除画布上的所有组件，此操作不可撤销
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 主题配置 */}
          {activeTab === 'theme' && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-4 flex items-center text-lg font-medium">
                  <Palette className="mr-2 h-5 w-5" />
                  颜色主题
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">主色调</label>
                    <input
                      className="h-10 w-full cursor-pointer rounded-md border border-gray-300"
                      defaultValue="#3b82f6"
                      type="color"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">辅助色</label>
                    <input
                      className="h-10 w-full cursor-pointer rounded-md border border-gray-300"
                      defaultValue="#6b7280"
                      type="color"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">背景色</label>
                    <input
                      className="h-10 w-full cursor-pointer rounded-md border border-gray-300"
                      defaultValue="#ffffff"
                      type="color"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="mb-4 text-lg font-medium">字体设置</h3>
                <div className="space-y-4">
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      主字体
                    </label>
                    <select className="w-full rounded-md border border-gray-300 px-3 py-2">
                      <option value="inter">Inter</option>
                      <option value="roboto">Roboto</option>
                      <option value="nunito">Nunito</option>
                      <option value="poppins">Poppins</option>
                    </select>
                  </div>
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      基础字号 (px)
                    </label>
                    <input
                      className="w-full rounded-md border border-gray-300 px-3 py-2"
                      defaultValue="16"
                      max="24"
                      min="12"
                      type="number"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 导入导出 */}
          {activeTab === 'export' && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-4 text-lg font-medium">数据管理</h3>
                <div className="space-y-4">
                  <div>
                    <Button
                      leftIcon={<Download className="h-4 w-4" />}
                      onClick={handleExportJSON}
                      variant="outline"
                    >
                      导出设计文件
                    </Button>
                    <p className="mt-2 text-sm text-gray-500">
                      将当前设计导出为JSON文件，可用于备份或迁移
                    </p>
                  </div>

                  <div>
                    <label className="block">
                      <span className="focus-visible:ring-primary-500 inline-flex h-10 cursor-pointer items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
                        <Upload className="mr-2 h-4 w-4" />
                        导入设计文件
                      </span>
                      <input
                        accept=".json"
                        className="hidden"
                        onChange={handleImportJSON}
                        type="file"
                      />
                    </label>
                    <p className="mt-2 text-sm text-gray-500">
                      从JSON文件导入设计，这将覆盖当前的设计内容
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="mb-4 text-lg font-medium">快捷操作</h3>
                <div className="space-y-3">
                  <Button
                    onClick={() => {
                      // TODO: 实现保存为模板功能
                    }}
                    variant="outline"
                  >
                    保存为模板
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: 实现复制链接功能
                    }}
                    variant="outline"
                  >
                    复制预览链接
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
