'use client';

import { Button } from '@/components/ui';
import { useEditor } from '@craftjs/core';
import { Eye, Layers, Redo2, Save, Settings, Undo2 } from 'lucide-react';
import React from 'react';

interface ToolbarProps {
  onPreview?: () => void;
  onSave?: () => void;
  onSettings?: () => void;
  onToggleLayers?: () => void;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  onPreview,
  onSave,
  onSettings,
  onToggleLayers,
}) => {
  const { actions, query, canUndo, canRedo } = useEditor((state, query) => ({
    canUndo: query.history.canUndo(),
    canRedo: query.history.canRedo(),
  }));

  const handleUndo = () => {
    actions.history.undo();
  };

  const handleRedo = () => {
    actions.history.redo();
  };

  const handleClear = () => {
    actions.clearEvents();
    const nodes = query.getNodes();
    Object.keys(nodes).forEach((nodeId) => {
      if (nodeId !== 'ROOT') {
        actions.delete(nodeId);
      }
    });
  };

  return (
    <div className="flex items-center justify-between border-b border-gray-200 bg-white p-4">
      {/* 左侧操作 */}
      <div className="flex items-center space-x-2">
        <Button
          disabled={!canUndo}
          leftIcon={<Undo2 className="h-4 w-4" />}
          onClick={handleUndo}
          size="sm"
          variant="outline"
        >
          撤销
        </Button>
        <Button
          disabled={!canRedo}
          leftIcon={<Redo2 className="h-4 w-4" />}
          onClick={handleRedo}
          size="sm"
          variant="outline"
        >
          重做
        </Button>
        <div className="mx-2 h-6 w-px bg-gray-300" />
        <Button onClick={handleClear} size="sm" variant="outline">
          清空
        </Button>
      </div>

      {/* 中间标题 */}
      <div className="flex-1 text-center">
        <h1 className="text-lg font-semibold text-gray-900">
          FlexiHub 网站编辑器
        </h1>
      </div>

      {/* 右侧操作 */}
      <div className="flex items-center space-x-2">
        <Button
          leftIcon={<Layers className="h-4 w-4" />}
          onClick={onToggleLayers}
          size="sm"
          variant="outline"
        >
          图层
        </Button>
        <Button
          leftIcon={<Settings className="h-4 w-4" />}
          onClick={onSettings}
          size="sm"
          variant="outline"
        >
          设置
        </Button>
        <Button
          leftIcon={<Eye className="h-4 w-4" />}
          onClick={onPreview}
          size="sm"
          variant="outline"
        >
          预览
        </Button>
        <Button
          leftIcon={<Save className="h-4 w-4" />}
          onClick={onSave}
          size="sm"
        >
          保存
        </Button>
      </div>
    </div>
  );
};
