'use client';

import { Button as UIButton } from '@/components/ui';
import { cn } from '@/lib/utils';
import { useNode } from '@craftjs/core';
import React from 'react';

interface CraftButtonProps {
  text?: string;
  variant?:
    | 'default'
    | 'destructive'
    | 'ghost'
    | 'link'
    | 'outline'
    | 'secondary'
    | 'success'
    | 'warning';
  size?: 'default' | 'icon' | 'lg' | 'sm' | 'xl';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
}

// 按钮设置面板
const CraftButtonSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as CraftButtonProps,
  }));

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          按钮文字
        </label>
        <input
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp((props: CraftButtonProps) => (props.text = e.target.value))
          }
          placeholder="输入按钮文字"
          type="text"
          value={props.text || ''}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          按钮样式
        </label>
        <select
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp(
              (props: CraftButtonProps) =>
                (props.variant = e.target.value as CraftButtonProps['variant']),
            )
          }
          value={props.variant || 'default'}
        >
          <option value="default">默认</option>
          <option value="destructive">危险</option>
          <option value="outline">轮廓</option>
          <option value="secondary">次要</option>
          <option value="ghost">幽灵</option>
          <option value="link">链接</option>
          <option value="success">成功</option>
          <option value="warning">警告</option>
        </select>
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          按钮大小
        </label>
        <select
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp(
              (props: CraftButtonProps) =>
                (props.size = e.target.value as CraftButtonProps['size']),
            )
          }
          // eslint-disable-next-line unicorn/explicit-length-check
          value={props.size || 'default'}
        >
          <option value="sm">小</option>
          <option value="default">默认</option>
          <option value="lg">大</option>
          <option value="xl">超大</option>
        </select>
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            checked={props.disabled || false}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            onChange={(e) =>
              setProp(
                (props: CraftButtonProps) =>
                  (props.disabled = e.target.checked),
              )
            }
            type="checkbox"
          />
          <span className="text-sm text-gray-700">禁用状态</span>
        </label>
      </div>
    </div>
  );
};

// 定义组件类型，包含craft属性
interface CraftButtonComponent extends React.FC<CraftButtonProps> {
  craft: {
    displayName: string;
    props: CraftButtonProps;
    related: {
      settings: React.ComponentType;
    };
  };
}

export const CraftButton = (({
  text = '按钮',
  variant = 'default',
  size = 'default',
  disabled = false,
  className,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    dragged,
  } = useNode((state) => ({
    selected: state.events.selected,
    dragged: state.events.dragged,
  }));

  return (
    <div
      className={cn(
        'inline-block',
        selected && 'ring-2 ring-blue-500 ring-offset-2',
        dragged && 'opacity-50',
        className,
      )}
      ref={(ref) => {
        if (ref !== null) {
          connect(drag(ref));
        }
      }}
    >
      <UIButton
        disabled={disabled}
        onClick={(e) => {
          // 在编辑模式下阻止默认点击行为
          e.preventDefault();
        }}
        size={size}
        variant={variant}
      >
        {text}
      </UIButton>
    </div>
  );
}) as CraftButtonComponent;

// Craft.js 配置
CraftButton.craft = {
  displayName: 'Button',
  props: {
    text: '按钮',
    variant: 'default',
    size: 'default',
    disabled: false,
  },
  related: {
    settings: CraftButtonSettings,
  },
};

export { CraftButtonSettings };
