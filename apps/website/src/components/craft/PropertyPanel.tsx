'use client';

import { useEditor } from '@craftjs/core';
import React from 'react';

export const PropertyPanel: React.FC = () => {
  const { selected, actions } = useEditor((state, query) => {
    const currentNodeId = [...state.events.selected][0];
    let selected;

    if (currentNodeId) {
      const node = state.nodes[currentNodeId];
      const componentType = node.data.type;
      const componentName =
        typeof componentType === 'string'
          ? componentType
          : (componentType as any).displayName ||
            (componentType as any).name ||
            'Component';

      selected = {
        id: currentNodeId,
        name: node.data.displayName || componentName,
        settings: node.related && node.related.settings,
        isDeletable: query.node(currentNodeId).isDeletable(),
      };
    }

    return {
      selected,
    };
  });

  return (
    <div style={{ padding: '16px' }}>
      <h4
        style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}
      >
        属性设置
      </h4>

      {selected ? (
        <div>
          <div
            style={{
              marginBottom: '16px',
              padding: '12px',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
            }}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              已选择: {selected.name}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ID: {selected.id.slice(-8)}
            </div>
          </div>

          {/* 渲染组件设置 */}
          {selected.settings && React.createElement(selected.settings)}

          {/* 删除按钮 */}
          {selected.isDeletable && (
            <button
              onClick={() => actions.delete(selected.id)}
              style={{
                marginTop: '16px',
                padding: '8px 16px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                width: '100%',
              }}
            >
              删除组件
            </button>
          )}
        </div>
      ) : (
        <div
          style={{
            textAlign: 'center',
            color: '#666',
            padding: '20px 0',
          }}
        >
          <div style={{ marginBottom: '8px' }}>选择一个组件</div>
          <div style={{ fontSize: '14px' }}>
            点击画布中的组件以查看和编辑其属性
          </div>
        </div>
      )}
    </div>
  );
};
