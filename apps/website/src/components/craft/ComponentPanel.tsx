'use client';

import { Element, useEditor } from '@craftjs/core';
import React from 'react';

import { CraftButton } from './Button';
import { Container } from './Container';
import { Text } from './Text';

export const ComponentPanel: React.FC = () => {
  const { connectors } = useEditor();

  return (
    <div>
      <h4
        style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}
      >
        拖拽添加组件
      </h4>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        }}
      >
        <button
          ref={(ref) => {
            if (ref) {
              connectors.create(ref, <CraftButton text="Click me" />);
            }
          }}
          style={{
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: '#f9f9f9',
            cursor: 'move',
            textAlign: 'center',
          }}
        >
          按钮
        </button>
        <button
          ref={(ref) => {
            if (ref) {
              connectors.create(ref, <Text text="Hi world" />);
            }
          }}
          style={{
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: '#f9f9f9',
            cursor: 'move',
            textAlign: 'center',
          }}
        >
          文本
        </button>
        <button
          ref={(ref) => {
            if (ref) {
              connectors.create(
                ref,
                <Element canvas is={Container} padding={20} />,
              );
            }
          }}
          style={{
            padding: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: '#f9f9f9',
            cursor: 'move',
            textAlign: 'center',
          }}
        >
          容器
        </button>
      </div>
    </div>
  );
};
