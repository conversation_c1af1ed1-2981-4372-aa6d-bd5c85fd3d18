'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui';
import { cn } from '@/lib/utils';
import { useEditor } from '@craftjs/core';
import { ChevronDown, ChevronRight, Copy, Trash2 } from 'lucide-react';
import React, { useState } from 'react';

interface LayerNodeProps {
  nodeId: string;
  depth?: number;
}

const LayerNode: React.FC<LayerNodeProps> = ({ nodeId, depth = 0 }) => {
  const [expanded, setExpanded] = useState(true);

  const { node, isSelected, hasChildren, children } = useEditor(
    (state, query) => {
      const nodeData = state.nodes[nodeId];
      const currentSelectedNodeId = query.getEvent('selected').first();

      return {
        node: nodeData,
        isSelected: currentSelectedNodeId === nodeId,
        hasChildren: nodeData?.data?.nodes && nodeData.data.nodes.length > 0,
        children: nodeData?.data?.nodes || [],
      };
    },
  );

  const { actions } = useEditor();

  if (!node) return null;

  const handleSelect = () => {
    actions.selectNode(nodeId);
  };

  const handleToggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    actions.delete(nodeId);
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: 实现组件复制功能

    console.warn('复制组件功能待实现:', nodeId);
  };

  const getNodeDisplayName = () => {
    const displayName = node.data.displayName;
    const type = node.data.type;

    if (typeof displayName === 'string') return displayName;
    if (typeof type === 'string') return type;
    return 'Unknown';
  };

  const getNodeIcon = () => {
    const type = node.data.type;
    const typeStr = typeof type === 'string' ? type.toLowerCase() : 'unknown';

    switch (typeStr) {
      case 'button': {
        return '�';
      }
      case 'container': {
        return '📦';
      }
      case 'image': {
        return '�️';
      }
      case 'text': {
        return '�';
      }
      default: {
        return '🧩';
      }
    }
  };

  return (
    <div className="select-none">
      <div
        className={cn(
          'group flex cursor-pointer items-center rounded px-2 py-1 hover:bg-gray-100',
          isSelected && 'bg-blue-100 hover:bg-blue-200',
          'transition-colors',
        )}
        onClick={handleSelect}
        style={{ paddingLeft: `${depth * 16 + 8}px` }}
      >
        {/* 展开/收起按钮 */}
        {hasChildren ? (
          <button
            className="mr-1 rounded p-0.5 hover:bg-gray-200"
            onClick={handleToggleExpanded}
          >
            {expanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </button>
        ) : (
          <div className="w-4" />
        )}

        {/* 组件图标 */}
        <span className="mr-2 text-sm">{getNodeIcon()}</span>

        {/* 组件名称 */}
        <span
          className={cn(
            'flex-1 truncate text-sm',
            isSelected ? 'font-medium text-blue-900' : 'text-gray-700',
          )}
        >
          {getNodeDisplayName()}
        </span>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 transition-opacity group-hover:opacity-100">
          <button
            className="rounded p-1 hover:bg-gray-200"
            onClick={handleDuplicate}
            title="复制"
          >
            <Copy className="h-3 w-3 text-gray-500" />
          </button>

          {nodeId !== 'ROOT' && (
            <button
              className="rounded p-1 hover:bg-red-100"
              onClick={handleDelete}
              title="删除"
            >
              <Trash2 className="h-3 w-3 text-red-500" />
            </button>
          )}
        </div>
      </div>

      {/* 子组件 */}
      {hasChildren && expanded && (
        <div className="mt-1">
          {children.map((childId: string) => (
            <LayerNode depth={depth + 1} key={childId} nodeId={childId} />
          ))}
        </div>
      )}
    </div>
  );
};

interface LayersPanelProps {
  isVisible?: boolean;
  onClose?: () => void;
}

export const LayersPanel: React.FC<LayersPanelProps> = ({
  isVisible = true,
  onClose,
}) => {
  const { rootNode, nodeCount } = useEditor((state) => {
    const rootNodeId = 'ROOT';
    const nodes = Object.values(state.nodes);

    return {
      rootNode: state.nodes[rootNodeId],
      nodeCount: nodes.length - 1, // 排除ROOT节点
    };
  });

  if (!isVisible) return null;

  return (
    <Card className="h-full w-64">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">图层</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">{nodeCount} 个组件</span>
            {onClose && (
              <button
                className="rounded p-1 hover:bg-gray-100"
                onClick={onClose}
              >
                ×
              </button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="max-h-96 overflow-y-auto">
          {rootNode?.data?.nodes && rootNode.data.nodes.length > 0 ? (
            <div className="space-y-1">
              {rootNode.data.nodes.map((nodeId: string) => (
                <LayerNode key={nodeId} nodeId={nodeId} />
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-gray-500">
              <div className="mb-2 text-gray-400">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                  />
                </svg>
              </div>
              <p className="text-sm">暂无组件</p>
              <p className="mt-1 text-xs">从左侧拖拽组件到画布开始编辑</p>
            </div>
          )}
        </div>

        {/* 图层操作工具 */}
        <div className="mt-4 border-t border-gray-200 pt-4">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-500">图层操作</span>
            <div className="flex space-x-2">
              <button
                className="rounded p-1 hover:bg-gray-100"
                title="全部展开"
              >
                <ChevronDown className="h-3 w-3" />
              </button>
              <button
                className="rounded p-1 hover:bg-gray-100"
                title="全部收起"
              >
                <ChevronRight className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
