'use client';

import { cn } from '@/lib/utils';
import { useNode } from '@craftjs/core';
import React from 'react';

interface TextProps {
  text?: string;
  fontSize?: number;
  color?: string;
  textAlign?: 'center' | 'left' | 'right';
  fontWeight?: 'bold' | 'normal';
  className?: string;
}

// 文本设置面板
const TextSettings: React.FC = () => {
  const {
    actions: { setProp },
    props,
  } = useNode((node) => ({
    props: node.data.props as TextProps,
  }));

  return (
    <div className="space-y-4 p-4">
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          文本内容
        </label>
        <textarea
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp((props: TextProps) => (props.text = e.target.value))
          }
          rows={3}
          value={props.text || ''}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          字体大小
        </label>
        <input
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          max="72"
          min="8"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.fontSize = Number.parseInt(e.target.value, 10)),
            )
          }
          type="number"
          value={props.fontSize || 16}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          颜色
        </label>
        <input
          className="h-10 w-full cursor-pointer rounded-md border border-gray-300"
          onChange={(e) =>
            setProp((props: TextProps) => (props.color = e.target.value))
          }
          type="color"
          value={props.color || '#000000'}
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          对齐方式
        </label>
        <select
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.textAlign = e.target.value as TextProps['textAlign']),
            )
          }
          value={props.textAlign || 'left'}
        >
          <option value="left">左对齐</option>
          <option value="center">居中</option>
          <option value="right">右对齐</option>
        </select>
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">
          字体粗细
        </label>
        <select
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) =>
            setProp(
              (props: TextProps) =>
                (props.fontWeight = e.target.value as TextProps['fontWeight']),
            )
          }
          value={props.fontWeight || 'normal'}
        >
          <option value="normal">正常</option>
          <option value="bold">粗体</option>
        </select>
      </div>
    </div>
  );
};

// 定义组件类型，包含craft属性
interface TextComponent extends React.FC<TextProps> {
  craft: {
    displayName: string;
    props: TextProps;
    related: {
      settings: React.ComponentType;
    };
  };
}

export const Text = (({
  text = '请输入文本',
  fontSize = 16,
  color = '#000000',
  textAlign = 'left',
  fontWeight = 'normal',
  className,
}) => {
  const {
    connectors: { connect, drag },
    selected,
    dragged,
  } = useNode((state) => ({
    selected: state.events.selected,
    dragged: state.events.dragged,
  }));

  return (
    <div
      className={cn(
        'relative cursor-pointer transition-all',
        selected && 'ring-2 ring-blue-500 ring-offset-2',
        dragged && 'opacity-50',
        className,
      )}
      ref={(ref) => {
        if (ref) {
          connect(drag(ref));
        }
      }}
      style={{
        fontSize: `${fontSize}px`,
        color,
        textAlign,
        fontWeight,
      }}
    >
      {text}
    </div>
  );
}) as TextComponent;

// Craft.js 配置
Text.craft = {
  displayName: 'Text',
  props: {
    text: '请输入文本',
    fontSize: 16,
    color: '#000000',
    textAlign: 'left',
    fontWeight: 'normal',
  },
  related: {
    settings: TextSettings,
  },
};

export { TextSettings };
