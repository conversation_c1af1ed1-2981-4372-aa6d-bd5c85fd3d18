'use client';

import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';

// 动态导入Stagewise工具栏
const StagewiseToolbar = dynamic(
  () =>
    import('@stagewise/toolbar-next').then((mod) => ({
      default: mod.StagewiseToolbar,
    })),
  {
    ssr: false,
    loading: () => null,
  },
);

// Stagewise配置
const stagewiseConfig = {
  plugins: [],
  theme: 'auto',
  position: 'bottom-right',
};

export function StagewiseProvider() {
  const [isDevelopment, setIsDevelopment] = useState(false);

  useEffect(() => {
    // 在客户端检查开发模式

    setIsDevelopment(
      (globalThis as any).process?.env?.NODE_ENV !== 'production',
    );
  }, []);

  // 仅在开发模式下渲染
  if (!isDevelopment) {
    return null;
  }

  return <StagewiseToolbar config={stagewiseConfig} />;
}
