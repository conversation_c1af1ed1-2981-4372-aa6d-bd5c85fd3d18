'use client';

import React from 'react';

interface SiteLayoutProps {
  children: React.ReactNode;
  locale: string;
}

export function SiteLayout({ children, locale: _locale }: SiteLayoutProps) {
  return (
    <div className="site-mode min-h-screen">
      {/* 用户网站内容 */}
      <main className="w-full">{children}</main>

      {/* 可选：网站底部信息 */}
      <footer className="mt-auto py-4 text-center text-xs text-gray-500">
        <div>Powered by FlexiHub</div>
      </footer>
    </div>
  );
}
