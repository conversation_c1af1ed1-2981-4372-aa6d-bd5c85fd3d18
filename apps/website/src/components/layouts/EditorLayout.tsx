'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

interface EditorLayoutProps {
  children: React.ReactNode;
  locale: string;
}

export function EditorLayout({ children, locale }: EditorLayoutProps) {
  const pathname = usePathname();
  const siteId = pathname.match(/\/editor\/([^/]+)/)?.[1];

  return (
    <div className="editor-mode flex h-screen flex-col">
      {/* 编辑器顶部工具栏 */}
      <header className="flex h-14 items-center justify-between border-b border-gray-200 bg-white px-4">
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold">FlexiHub 编辑器</h1>
          <div className="text-sm text-gray-500">
            {siteId && `网站: ${siteId}`} | 语言: {locale}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 撤销/重做 */}
          <button
            className="rounded bg-gray-100 px-3 py-1 text-sm hover:bg-gray-200 disabled:opacity-50"
            disabled
          >
            撤销
          </button>
          <button
            className="rounded bg-gray-100 px-3 py-1 text-sm hover:bg-gray-200 disabled:opacity-50"
            disabled
          >
            重做
          </button>

          <div className="mx-2 h-4 border-l border-gray-300"></div>

          {/* 预览按钮 */}
          {siteId && (
            <Link
              className="rounded bg-green-100 px-3 py-1 text-sm text-green-700 hover:bg-green-200"
              href={`/${locale}/editor/${siteId}/preview`}
              target="_blank"
            >
              预览
            </Link>
          )}

          {/* 保存/发布 */}
          <button className="rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700">
            保存
          </button>
          <button className="rounded bg-purple-600 px-3 py-1 text-sm text-white hover:bg-purple-700">
            发布
          </button>
        </div>
      </header>

      {/* 编辑器主体区域 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧组件面板 */}
        <aside className="w-64 overflow-y-auto border-r border-gray-200 bg-gray-50">
          <div className="p-4">
            <h3 className="mb-3 font-medium">组件库</h3>
            <div className="space-y-2">
              <div className="cursor-pointer rounded border bg-white p-2 hover:bg-gray-50">
                📝 文本组件
              </div>
              <div className="cursor-pointer rounded border bg-white p-2 hover:bg-gray-50">
                📦 容器组件
              </div>
              <div className="cursor-pointer rounded border bg-white p-2 hover:bg-gray-50">
                🖼️ 图片组件
              </div>
              <div className="cursor-pointer rounded border bg-white p-2 hover:bg-gray-50">
                🔗 按钮组件
              </div>
            </div>
          </div>
        </aside>

        {/* 中央编辑区域 */}
        <main className="flex-1 overflow-hidden bg-gray-100">
          <div className="editor-canvas h-full">{children}</div>
        </main>

        {/* 右侧属性面板 */}
        <aside className="w-64 overflow-y-auto border-l border-gray-200 bg-gray-50">
          <div className="p-4">
            <h3 className="mb-3 font-medium">属性设置</h3>
            <div className="space-y-4">
              <div className="text-sm text-gray-500">
                选择组件后显示属性配置
              </div>

              {/* 页面设置 */}
              <div className="rounded border bg-white p-3">
                <h4 className="mb-2 text-sm font-medium">页面设置</h4>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-600">
                      页面标题
                    </label>
                    <input
                      className="w-full rounded border px-2 py-1 text-sm"
                      placeholder="输入页面标题"
                      type="text"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600">
                      SEO描述
                    </label>
                    <textarea
                      className="w-full rounded border px-2 py-1 text-sm"
                      placeholder="输入SEO描述"
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
}
