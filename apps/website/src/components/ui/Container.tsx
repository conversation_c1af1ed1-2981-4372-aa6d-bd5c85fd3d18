'use client';

import type { VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import * as React from 'react';

// 容器变体配置
const containerVariants = cva('mx-auto w-full', {
  variants: {
    size: {
      default: 'max-w-7xl px-4 sm:px-6 lg:px-8',
      sm: 'max-w-3xl px-4 sm:px-6',
      md: 'max-w-5xl px-4 sm:px-6 lg:px-8',
      lg: 'max-w-7xl px-4 sm:px-6 lg:px-8',
      xl: 'max-w-[90rem] px-4 sm:px-6 lg:px-8',
      full: 'max-w-full px-4 sm:px-6 lg:px-8',
      none: 'max-w-none',
    },
    padding: {
      default: 'px-4 sm:px-6 lg:px-8',
      none: 'px-0',
      sm: 'px-2 sm:px-4',
      lg: 'px-6 sm:px-8 lg:px-12',
    },
  },
  defaultVariants: {
    size: 'default',
    padding: 'default',
  },
});

// 容器组件属性接口
export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {
  as?: React.ElementType;
}

// 容器组件
const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size, padding, as: Component = 'div', ...props }, ref) => (
    <Component
      className={cn(containerVariants({ size, padding }), className)}
      ref={ref}
      {...props}
    />
  ),
);

Container.displayName = 'Container';

// 区块组件（带背景色的容器）
const Section = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement> & {
    as?: React.ElementType;
    variant?: 'dark' | 'default' | 'gray' | 'primary';
  }
>(
  (
    { className, as: Component = 'section', variant = 'default', ...props },
    ref,
  ) => {
    const variantClasses = {
      default: 'bg-white',
      gray: 'bg-gray-50',
      primary: 'bg-primary-50',
      dark: 'bg-gray-900 text-white',
    };

    return (
      <Component
        className={cn(variantClasses[variant], className)}
        ref={ref}
        {...props}
      />
    );
  },
);

Section.displayName = 'Section';

// 网格布局组件
const Grid = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    gap?: 'default' | 'lg' | 'none' | 'sm' | 'xl';
    responsive?: boolean;
  }
>(
  (
    { className, cols = 1, gap = 'default', responsive = true, ...props },
    ref,
  ) => {
    const colsClasses = {
      1: 'grid-cols-1',
      2: responsive ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-2',
      3: responsive
        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        : 'grid-cols-3',
      4: responsive
        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
        : 'grid-cols-4',
      5: responsive
        ? 'grid-cols-1 md:grid-cols-3 lg:grid-cols-5'
        : 'grid-cols-5',
      6: responsive
        ? 'grid-cols-1 md:grid-cols-3 lg:grid-cols-6'
        : 'grid-cols-6',
      12: responsive
        ? 'grid-cols-1 md:grid-cols-6 lg:grid-cols-12'
        : 'grid-cols-12',
    };

    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      default: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };

    return (
      <div
        className={cn('grid', colsClasses[cols], gapClasses[gap], className)}
        ref={ref}
        {...props}
      />
    );
  },
);

Grid.displayName = 'Grid';

// Flex布局组件
const Flex = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    align?: 'baseline' | 'center' | 'end' | 'start' | 'stretch';
    direction?: 'col' | 'col-reverse' | 'row' | 'row-reverse';
    gap?: 'default' | 'lg' | 'none' | 'sm' | 'xl';
    justify?: 'around' | 'between' | 'center' | 'end' | 'evenly' | 'start';
    wrap?: boolean;
  }
>(
  (
    {
      className,
      direction = 'row',
      align = 'start',
      justify = 'start',
      wrap = false,
      gap = 'default',
      ...props
    },
    ref,
  ) => {
    const directionClasses = {
      row: 'flex-row',
      col: 'flex-col',
      'row-reverse': 'flex-row-reverse',
      'col-reverse': 'flex-col-reverse',
    };

    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
      baseline: 'items-baseline',
    };

    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    };

    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      default: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };

    return (
      <div
        className={cn(
          'flex',
          directionClasses[direction],
          alignClasses[align],
          justifyClasses[justify],
          wrap && 'flex-wrap',
          gapClasses[gap],
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);

Flex.displayName = 'Flex';

export { Container, containerVariants, Flex, Grid, Section };
