@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@import '../styles/design-tokens.css';
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置和优化 */
@layer base {
  /* HTML 和 Body 基础设置 */
  html {
    font-family: var(--font-family-sans);
    line-height: var(--line-height-normal);
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-size: var(--font-size-base);
    margin: 0;
    padding: 0;
  }

  /* 标题层级样式 */
  h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-gray-900);
  }

  h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--color-gray-800);
  }

  h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
    color: var(--color-gray-800);
  }

  h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
    color: var(--color-gray-700);
  }

  h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
    color: var(--color-gray-700);
  }

  h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--color-gray-600);
  }

  /* 段落和文本 */
  p {
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-relaxed);
  }

  /* 链接样式 */
  a {
    color: var(--color-primary-600);
    text-decoration: none;
    transition: color var(--transition-duration-fast) var(--transition-timing-function);
  }

  a:hover {
    color: var(--color-primary-700);
    text-decoration: underline;
  }

  /* 代码样式 */
  code {
    font-family: var(--font-family-mono);
    font-size: 0.875em;
    background-color: var(--color-gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-base);
    color: var(--color-gray-800);
  }

  pre {
    font-family: var(--font-family-mono);
    background-color: var(--color-gray-900);
    color: var(--color-gray-100);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    margin-bottom: var(--spacing-4);
  }

  pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  /* 表单元素基础样式 */
  input,
  textarea,
  select {
    font-family: inherit;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  button {
    font-family: inherit;
    cursor: pointer;
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* 焦点样式 */
  :focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }

  /* 选中文本样式 */
  ::selection {
    background-color: var(--color-primary-200);
    color: var(--color-primary-900);
  }
}

/* 组件层样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md;
    @apply border border-transparent;
    @apply transition-colors duration-200 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
    @apply focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
    @apply focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700;
    @apply focus:ring-green-500;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700;
    @apply focus:ring-yellow-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
    @apply focus:ring-red-500;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
    @apply p-6;
  }

  .card-header {
    @apply pb-4 mb-4 border-b border-gray-200;
  }

  .card-body {
    @apply space-y-4;
  }

  .card-footer {
    @apply pt-4 mt-4 border-t border-gray-200;
  }

  /* 表单样式 */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    @apply disabled:bg-gray-100 disabled:cursor-not-allowed;
  }

  .form-textarea {
    @apply form-input;
    @apply resize-vertical;
  }

  .form-select {
    @apply form-input;
    @apply pr-10 bg-white;
  }

  .form-error {
    @apply text-sm text-red-600;
  }

  .form-help {
    @apply text-sm text-gray-500;
  }

  /* 编辑器专用样式 */
  .editor-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--color-gray-50);
  }

  .editor-toolbar {
    height: var(--editor-toolbar-height);
    background-color: var(--color-gray-900);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-4);
    border-bottom: 1px solid var(--color-gray-200);
  }

  .editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .editor-sidebar {
    width: var(--editor-sidebar-width);
    background-color: var(--color-gray-50);
    border-right: 1px solid var(--color-gray-200);
    overflow-y: auto;
  }

  .editor-canvas {
    flex: 1;
    background-color: var(--editor-canvas-background);
    position: relative;
    overflow: auto;
  }

  .editor-properties {
    width: var(--editor-property-panel-width);
    background-color: var(--color-gray-50);
    border-left: 1px solid var(--color-gray-200);
    overflow-y: auto;
  }

  /* 网站渲染器样式 */
  .site-container {
    max-width: var(--site-max-width);
    margin: 0 auto;
    padding: 0 var(--site-container-padding);
  }

  /* 响应式工具类 */
  .container-responsive {
    @apply w-full mx-auto px-4;
  }

  @media (min-width: 640px) {
    .container-responsive {
      max-width: 640px;
      @apply px-6;
    }
  }

  @media (min-width: 768px) {
    .container-responsive {
      max-width: 768px;
      @apply px-8;
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .container-responsive {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .container-responsive {
      max-width: 1536px;
    }
  }
}

/* 工具类样式 */
@layer utilities {
  /* 文本工具类 */
  .text-balance {
    text-wrap: balance;
  }

  /* 动画工具类 */
  .animate-fade-in {
    animation: fadeIn var(--transition-duration-base) var(--transition-timing-function);
  }

  .animate-slide-up {
    animation: slideUp var(--transition-duration-base) var(--transition-timing-function);
  }

  .animate-scale-in {
    animation: scaleIn var(--transition-duration-fast) var(--transition-timing-function);
  }

  /* 阴影工具类 */
  .shadow-custom {
    box-shadow: var(--shadow-base);
  }

  .shadow-custom-lg {
    box-shadow: var(--shadow-lg);
  }

  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--color-gray-400) var(--color-gray-100);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: var(--radius-base);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: var(--radius-base);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .editor-layout,
  .editor-toolbar,
  .editor-sidebar,
  .editor-properties {
    display: none !important;
  }
}

/* 编辑器样式 */
.viewport {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.toolbox {
  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.sidebar {
  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.craftjs-renderer {
  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.header {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 组件选中状态 */
.component-selected {
  @apply relative;
}

.component-selected::after {
  content: ' ';
  @apply pointer-events-none absolute left-0 top-0 block h-full w-full border border-dashed border-blue-500;
}

/* 平滑过渡 */
.transition {
  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
