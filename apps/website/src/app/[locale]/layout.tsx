'use client';

import { SiteLayout } from '@/components/layouts/SiteLayout';
import { useAppMode } from '@/hooks/useAppMode';
import React from 'react';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    locale: string;
  }>;
}

export default function LocaleLayout({ children, params }: LocaleLayoutProps) {
  const { locale } = React.use(params);
  const { isEditor } = useAppMode();

  // 对于编辑器页面，直接渲染children，不使用额外的布局包装
  if (isEditor) {
    return <>{children}</>;
  }

  return <SiteLayout locale={locale}>{children}</SiteLayout>;
}
