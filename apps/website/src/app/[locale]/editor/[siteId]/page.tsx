'use client';

import { PropertyPanel } from '@/components/editor/properties/PropertyPanel';
import { ToolboxPanel } from '@/components/editor/toolbox/ToolboxPanel';
import { materialRegistry } from '@/components/materials/registry';
import { basicComponents } from '@/components/materials/registry/basic';
import { layoutComponents } from '@/components/materials/registry/layout';
import { StagewiseProvider } from '@/components/StagewiseProvider';
import { Button } from '@/components/ui/button';
import { Editor, Frame } from '@craftjs/core';
import React, { useEffect, useState } from 'react';

interface EditorPageProps {
  params: Promise<{
    locale: string;
    siteId: string;
  }>;
}

export default function EditorPage({ params }: EditorPageProps) {
  const { locale: _locale, siteId } = React.use(params);
  const [enabled, setEnabled] = useState(true);
  const [resolver, setResolver] = useState<Record<string, React.ComponentType>>(
    {},
  );

  // 初始化物料组件
  useEffect(() => {
    // 注册所有物料组件
    materialRegistry.clear(); // 清空之前的注册
    materialRegistry.registerBatch([...basicComponents, ...layoutComponents]);

    // 获取CraftJS resolver
    const craftResolver = materialRegistry.getCraftResolver();
    setResolver(craftResolver);
  }, []);

  const handleSerialize = () => {
    // eslint-disable-next-line no-console
    console.log('序列化功能将在这里实现');
  };

  const handlePreview = () => {
    setEnabled(!enabled);
  };

  // 获取物料组件 (将来用于动态内容)
  // const MaterialText = resolver.Text;
  // const MaterialButton = resolver.Button;
  // const MaterialContainer = resolver.Container;

  return (
    <div className="flex h-screen flex-col">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between border-b border-gray-200 bg-white px-6 py-3">
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold text-gray-900">
            FlexiHub 页面编辑器
          </h1>
          <span className="text-sm text-gray-500">站点ID: {siteId}</span>
        </div>

        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              checked={enabled}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              onChange={(e) => setEnabled(e.target.checked)}
              type="checkbox"
            />
            <span className="text-sm text-gray-700">编辑模式</span>
          </label>

          <Button onClick={handlePreview} size="sm" variant="outline">
            {enabled ? '预览' : '编辑'}
          </Button>

          <Button onClick={handleSerialize} size="sm" variant="default">
            保存
          </Button>
        </div>
      </div>

      <Editor enabled={enabled} resolver={resolver}>
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧工具箱 */}
          <div className="w-64 overflow-y-auto border-r border-gray-200 bg-gray-50">
            <ToolboxPanel />
          </div>

          {/* 中间画布区域 */}
          <div className="flex-1 overflow-auto bg-gray-100">
            <div className="p-6">
              <div className="min-h-[600px] rounded-lg border bg-white shadow-sm">
                <Frame>
                  <div
                    style={{
                      background: '#ffffff',
                      padding: '40px',
                      minHeight: '400px',
                      textAlign: 'center',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '20px',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <h1
                      style={{
                        fontSize: '24px',
                        fontWeight: '600',
                        color: '#1f2937',
                        margin: 0,
                      }}
                    >
                      欢迎使用 FlexiHub 页面编辑器
                    </h1>
                    <p
                      style={{
                        fontSize: '16px',
                        color: '#6b7280',
                        margin: 0,
                      }}
                    >
                      你可以从左侧工具箱拖拽组件到这里开始创建你的页面。
                    </p>
                    <div
                      style={{
                        background: '#f9fafb',
                        padding: '20px',
                        borderRadius: '8px',
                        display: 'flex',
                        gap: '16px',
                      }}
                    >
                      <button className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                        开始创建
                      </button>
                      <button className="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50">
                        查看模板
                      </button>
                    </div>
                  </div>
                </Frame>
              </div>
            </div>
          </div>

          {/* 右侧属性面板 */}
          <div className="w-80 overflow-y-auto border-l border-gray-200 bg-white">
            <PropertyPanel />
          </div>
        </div>
      </Editor>

      {/* Stagewise开发工具栏 */}
      <StagewiseProvider />
    </div>
  );
}
