'use client';

import { PageRenderer } from '@/components/renderers/PageRenderer';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface PreviewPageProps {
  params: Promise<{
    locale: string;
    siteId: string;
  }>;
}

export default function PreviewPage({ params }: PreviewPageProps) {
  const router = useRouter();
  const { locale, siteId } = React.use(params);
  const [siteData, setSiteData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function loadSiteData() {
      try {
        const response = await fetch(
          `/api/sites/${siteId}?locale=${locale}&preview=true`,
        );

        if (!response.ok) {
          throw new Error('获取网站数据失败');
        }

        const data = await response.json();
        setSiteData(data);
      } catch (error_) {
        setError(error_ instanceof Error ? error_.message : '加载失败');
      } finally {
        setIsLoading(false);
      }
    }

    loadSiteData();
  }, [locale, siteId]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
          <p className="text-gray-600">加载预览中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-red-50">
        <div className="max-w-md text-center">
          <h1 className="mb-4 text-4xl font-bold text-red-200">⚠️</h1>
          <h2 className="mb-4 text-2xl font-semibold text-red-700">预览失败</h2>
          <p className="mb-6 text-red-600">{error}</p>
          <button
            className="rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            onClick={() => router.back()}
          >
            返回编辑器
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* 预览工具栏 */}
      <div className="sticky top-0 z-50 flex items-center justify-between border-b bg-white px-4 py-2 shadow-sm">
        <div className="flex items-center space-x-4">
          <button
            className="flex items-center space-x-2 rounded bg-gray-100 px-3 py-1 text-sm hover:bg-gray-200"
            onClick={() => router.back()}
          >
            <span>←</span>
            <span>返回编辑器</span>
          </button>
          <div className="text-sm text-gray-600">
            预览模式 - 网站ID: {siteId}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
            预览
          </div>
          <select className="rounded border px-2 py-1 text-sm">
            <option>桌面 (1920px)</option>
            <option>平板 (768px)</option>
            <option>手机 (375px)</option>
          </select>
        </div>
      </div>

      {/* 预览内容 */}
      <div className="overflow-auto">
        {siteData ? (
          <PageRenderer pageData={siteData} />
        ) : (
          <div className="flex min-h-screen items-center justify-center text-gray-500">
            暂无内容
          </div>
        )}
      </div>
    </div>
  );
}
