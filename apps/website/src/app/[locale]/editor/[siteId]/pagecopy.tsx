'use client';

import { CraftButton } from '@/components/craft/Button';
import { ComponentPanel } from '@/components/craft/ComponentPanel';
import { Container } from '@/components/craft/Container';
import { PropertyPanel } from '@/components/craft/PropertyPanel';
import { Text } from '@/components/craft/Text';
import { StagewiseProvider } from '@/components/StagewiseProvider';
import { Editor, Element, Frame } from '@craftjs/core';
import React, { useState } from 'react';

interface EditorPageProps {
  params: {
    locale: string;
    siteId: string;
  };
}

export default function EditorPage({ params }: EditorPageProps) {
  const { locale: _locale, siteId: _siteId } = params;
  const [enabled, setEnabled] = useState(true);

  const handleSerialize = () => {
    // eslint-disable-next-line no-console
    console.log('Serialization will be implemented here');
  };

  return (
    <div style={{ margin: '0 auto', width: '100%', height: '100vh' }}>
      <h5 style={{ textAlign: 'center', padding: '20px 0' }}>
        FlexiHub 页面编辑器
      </h5>

      <Editor resolver={{ CraftButton, Text, Container }}>
        {/* 顶部工具栏 */}
        <div
          style={{
            padding: '10px 20px',
            backgroundColor: '#f5f5f5',
            borderBottom: '1px solid #ddd',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <input
              checked={enabled}
              onChange={(e) => setEnabled(e.target.checked)}
              type="checkbox"
            />
            启用编辑
          </label>
          <button
            onClick={handleSerialize}
            style={{
              padding: '6px 12px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            序列化到控制台
          </button>
        </div>

        {/* 主编辑区域 */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 300px',
            height: 'calc(100vh - 120px)',
            gap: '20px',
            padding: '20px',
          }}
        >
          {/* 左侧：编辑画布 */}
          <div
            style={{
              border: '1px solid #ddd',
              borderRadius: '8px',
              overflow: 'auto',
              backgroundColor: 'white',
            }}
          >
            <Frame>
              <Element
                backgroundColor="#eee"
                canvas
                is={Container}
                padding={20}
              >
                <Text fontSize={20} text="Hello World!" />
                <CraftButton size="sm" text="Click me" variant="default" />
                <Text fontSize={16} text="It's me again!" />
                <Element
                  backgroundColor="#999"
                  canvas
                  is={Container}
                  padding={20}
                >
                  <Text fontSize={14} text="I'm inside a container!" />
                </Element>
              </Element>
            </Frame>
          </div>

          {/* 右侧：组件面板和设置面板 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            {/* 组件面板 */}
            <div
              style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                backgroundColor: 'white',
                padding: '16px',
              }}
            >
              <ComponentPanel />
            </div>

            {/* 属性面板 */}
            <div
              style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                backgroundColor: 'white',
                flex: 1,
              }}
            >
              <PropertyPanel />
            </div>
          </div>
        </div>
      </Editor>

      {/* Stagewise开发工具栏 */}
      <StagewiseProvider />
    </div>
  );
}
