import Link from 'next/link';
import React from 'react';

interface LocalePageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default function LocalePage({ params }: LocalePageProps) {
  const { locale } = React.use(params);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="mx-auto max-w-6xl p-8 text-center">
        <h1 className="mb-6 text-5xl font-bold text-gray-900">
          FlexiHub 统一平台
        </h1>
        <p className="mb-12 text-xl text-gray-600">
          一体化的网站构建和渲染平台 - 支持自定义域名、预览和API集成
        </p>

        <div className="mb-12 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* 编辑器入口 */}
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="mb-4 text-4xl">🎨</div>
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              网站编辑器
            </h2>
            <p className="mb-6 text-gray-600">
              使用可视化拖拽编辑器创建和编辑您的网站
            </p>
            <Link
              className="inline-block rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
              href={`/${locale}/editor/demo-site`}
            >
              打开编辑器
            </Link>
            <div className="mt-3">
              <Link
                className="text-sm text-blue-600 hover:text-blue-800"
                href={`/${locale}/editor/demo-site/preview`}
                target="_blank"
              >
                🔗 预览功能
              </Link>
            </div>
          </div>

          {/* 网站预览入口 */}
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="mb-4 text-4xl">🌐</div>
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              网站访问
            </h2>
            <p className="mb-6 text-gray-600">
              查看用户访问时看到的实际网站效果
            </p>
            <Link
              className="inline-block rounded-lg bg-green-600 px-6 py-3 font-medium text-white transition-colors hover:bg-green-700"
              href={`/${locale}/demo-site`}
            >
              访问网站
            </Link>
            <div className="mt-3 space-y-1">
              <div>
                <Link
                  className="text-sm text-green-600 hover:text-green-800"
                  href={`/${locale}/my-blog`}
                >
                  🔗 博客演示
                </Link>
              </div>
            </div>
          </div>

          {/* API文档入口 */}
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="mb-4 text-4xl">📚</div>
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              API 文档
            </h2>
            <p className="mb-6 text-gray-600">
              查看完整的 API 接口文档和使用示例
            </p>
            <Link
              className="inline-block rounded-lg bg-purple-600 px-6 py-3 font-medium text-white transition-colors hover:bg-purple-700"
              href="/api-docs"
            >
              查看文档
            </Link>
            <div className="mt-3">
              <div className="text-xs text-gray-500">
                包含认证、网站管理、组件等API
              </div>
            </div>
          </div>

          {/* 管理后台演示入口 */}
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="mb-4 text-4xl">🎛️</div>
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              管理后台演示
            </h2>
            <p className="mb-6 text-gray-600">
              演示从管理后台免登录跳转到编辑器
            </p>
            <Link
              className="inline-block rounded-lg bg-orange-600 px-6 py-3 font-medium text-white transition-colors hover:bg-orange-700"
              href="/admin-demo"
            >
              演示跳转
            </Link>
            <div className="mt-3">
              <div className="text-xs text-gray-500">Vue3管理后台集成方案</div>
            </div>
          </div>
        </div>

        {/* 技术特性 */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow-lg">
          <h3 className="mb-4 text-xl font-semibold text-gray-900">
            ✨ 平台特性
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <div className="text-center">
              <div className="mb-2 text-2xl">🔒</div>
              <div className="text-sm font-medium">JWT权限认证</div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-2xl">🌍</div>
              <div className="text-sm font-medium">自定义域名支持</div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-2xl">👁️</div>
              <div className="text-sm font-medium">实时预览功能</div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-2xl">📡</div>
              <div className="text-sm font-medium">完整API接口</div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-2xl">🚀</div>
              <div className="text-sm font-medium">免登录跳转</div>
            </div>
          </div>
        </div>

        {/* 状态信息 */}
        <div className="space-y-2 text-sm text-gray-500">
          <p>当前语言: {locale}</p>
          <p>架构模式: 统一应用 (编辑器 + 渲染器 + API + 管理集成)</p>
          <p>自定义域名: 支持 example.com 直接访问（生产环境）</p>
        </div>

        {/* 快速测试 */}
        <div className="mt-8 rounded-lg border border-gray-200 bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">🧪 快速测试</h4>
          <div className="grid gap-4 text-sm text-gray-600 md:grid-cols-2">
            <div>
              <p>
                <strong>编辑器权限：</strong> 需要登录 (admin/123456)
              </p>
              <p>
                <strong>网站访问：</strong> 无需权限，直接访问
              </p>
            </div>
            <div>
              <p>
                <strong>API调用：</strong> 查看文档了解详细用法
              </p>
              <p>
                <strong>管理集成：</strong> 演示免登录跳转功能
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
