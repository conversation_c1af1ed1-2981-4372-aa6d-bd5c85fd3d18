import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Container,
  Flex,
  Grid,
  Input,
  LanguageSwitcher,
  Section,
} from '@/components/ui';
import { Download, Heart, Mail, Search, Settings, User } from 'lucide-react';
import React from 'react';

interface UIDemoPageProps {
  params: {
    locale: string;
  };
}

export default function UIDemoPage({ params }: UIDemoPageProps) {
  const { locale } = params;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <Section className="py-12" variant="gray">
        <Container>
          <div className="text-center">
            <h1 className="mb-4 text-4xl font-bold text-gray-900">
              FlexiHub UI 组件库演示
            </h1>
            <p className="mb-8 text-xl text-gray-600">
              基于设计令牌的统一UI组件系统
            </p>
            <div className="mb-6 flex justify-center">
              <LanguageSwitcher />
            </div>
          </div>
        </Container>
      </Section>

      {/* 按钮组件演示 */}
      <Section className="py-12">
        <Container>
          <Card>
            <CardHeader>
              <CardTitle>按钮组件 (Button)</CardTitle>
              <CardDescription>
                支持多种变体、大小和状态的按钮组件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 基础变体 */}
                <div>
                  <h4 className="mb-4 text-lg font-medium">变体 (Variants)</h4>
                  <Flex gap="sm" wrap>
                    <Button>默认按钮</Button>
                    <Button variant="destructive">危险按钮</Button>
                    <Button variant="outline">轮廓按钮</Button>
                    <Button variant="secondary">次要按钮</Button>
                    <Button variant="ghost">幽灵按钮</Button>
                    <Button variant="link">链接按钮</Button>
                    <Button variant="success">成功按钮</Button>
                    <Button variant="warning">警告按钮</Button>
                  </Flex>
                </div>

                {/* 大小变体 */}
                <div>
                  <h4 className="mb-4 text-lg font-medium">大小 (Sizes)</h4>
                  <Flex align="center" gap="sm" wrap>
                    <Button size="sm">小按钮</Button>
                    <Button size="default">默认按钮</Button>
                    <Button size="lg">大按钮</Button>
                    <Button size="xl">超大按钮</Button>
                    <Button aria-label="图标按钮" size="icon">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </Flex>
                </div>

                {/* 状态演示 */}
                <div>
                  <h4 className="mb-4 text-lg font-medium">状态 (States)</h4>
                  <Flex gap="sm" wrap>
                    <Button leftIcon={<User className="h-4 w-4" />}>
                      带左图标
                    </Button>
                    <Button rightIcon={<Download className="h-4 w-4" />}>
                      带右图标
                    </Button>
                    <Button loading>加载中...</Button>
                    <Button disabled>禁用状态</Button>
                  </Flex>
                </div>
              </div>
            </CardContent>
          </Card>
        </Container>
      </Section>

      {/* 输入框组件演示 */}
      <Section className="py-12" variant="gray">
        <Container>
          <Card>
            <CardHeader>
              <CardTitle>输入框组件 (Input)</CardTitle>
              <CardDescription>
                支持标签、错误提示和图标的输入框组件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Grid cols={2} gap="lg">
                <div className="space-y-4">
                  <Input
                    helperText="这是帮助文本"
                    label="基础输入框"
                    placeholder="请输入内容"
                  />
                  <Input
                    defaultValue="invalid-email"
                    error="请输入有效的邮箱地址"
                    label="错误状态"
                    placeholder="请输入邮箱"
                  />
                  <Input
                    defaultValue="<EMAIL>"
                    label="成功状态"
                    placeholder="验证通过"
                    variant="success"
                  />
                </div>
                <div className="space-y-4">
                  <Input
                    label="带左图标"
                    leftIcon={<Search className="h-4 w-4" />}
                    placeholder="搜索..."
                  />
                  <Input
                    label="带右图标"
                    placeholder="邮箱地址"
                    rightIcon={<Mail className="h-4 w-4" />}
                    type="email"
                  />
                  <div className="space-y-2">
                    <label className="text-sm font-medium">大小变体</label>
                    <div className="space-y-2">
                      <Input placeholder="小尺寸" size="sm" />
                      <Input placeholder="默认尺寸" size="default" />
                      <Input placeholder="大尺寸" size="lg" />
                    </div>
                  </div>
                </div>
              </Grid>
            </CardContent>
          </Card>
        </Container>
      </Section>

      {/* 卡片组件演示 */}
      <Section className="py-12">
        <Container>
          <Card>
            <CardHeader>
              <CardTitle>卡片组件 (Card)</CardTitle>
              <CardDescription>
                灵活的卡片容器，支持头部、内容和底部区域
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Grid cols={3} gap="lg">
                {/* 基础卡片 */}
                <Card>
                  <CardHeader>
                    <CardTitle>基础卡片</CardTitle>
                    <CardDescription>这是一个基础的卡片示例</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      卡片内容区域，可以放置任何内容。
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button size="sm">操作</Button>
                  </CardFooter>
                </Card>

                {/* 轮廓卡片 */}
                <Card variant="outline">
                  <CardHeader>
                    <CardTitle>轮廓卡片</CardTitle>
                    <CardDescription>使用轮廓样式的卡片</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      这种样式更加轻量，适合在浅色背景上使用。
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Flex gap="sm">
                      <Button size="sm" variant="outline">
                        取消
                      </Button>
                      <Button size="sm">确认</Button>
                    </Flex>
                  </CardFooter>
                </Card>

                {/* 幽灵卡片 */}
                <Card variant="ghost">
                  <CardHeader>
                    <CardTitle>幽灵卡片</CardTitle>
                    <CardDescription>无边框和阴影的卡片</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      适合用于简洁的界面设计，不会干扰视觉焦点。
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button
                      leftIcon={<Heart className="h-4 w-4" />}
                      size="sm"
                      variant="ghost"
                    >
                      喜欢
                    </Button>
                  </CardFooter>
                </Card>
              </Grid>
            </CardContent>
          </Card>
        </Container>
      </Section>

      {/* 布局组件演示 */}
      <Section className="py-12" variant="gray">
        <Container>
          <Card>
            <CardHeader>
              <CardTitle>布局组件演示</CardTitle>
              <CardDescription>响应式容器、网格和弹性布局组件</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* Grid 布局 */}
                <div>
                  <h4 className="mb-4 text-lg font-medium">网格布局 (Grid)</h4>
                  <Grid cols={4} gap="sm">
                    {Array.from({ length: 8 }, (_, i) => (
                      <div
                        className="bg-primary-100 border-primary-200 text-primary-700 rounded border p-4 text-center"
                        key={i}
                      >
                        项目 {i + 1}
                      </div>
                    ))}
                  </Grid>
                </div>

                {/* Flex 布局 */}
                <div>
                  <h4 className="mb-4 text-lg font-medium">弹性布局 (Flex)</h4>
                  <div className="space-y-4">
                    <div>
                      <p className="mb-2 text-sm text-gray-600">水平居中对齐</p>
                      <Flex
                        align="center"
                        className="min-h-[100px] rounded bg-gray-100 p-4"
                        justify="center"
                      >
                        <div className="rounded bg-blue-500 px-4 py-2 text-white">
                          居中内容
                        </div>
                      </Flex>
                    </div>
                    <div>
                      <p className="mb-2 text-sm text-gray-600">两端对齐</p>
                      <Flex
                        align="center"
                        className="rounded bg-gray-100 p-4"
                        justify="between"
                      >
                        <Button size="sm">左侧</Button>
                        <span className="text-gray-600">中间内容</span>
                        <Button size="sm" variant="outline">
                          右侧
                        </Button>
                      </Flex>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Container>
      </Section>

      {/* 页面底部 */}
      <Section className="py-8" variant="dark">
        <Container>
          <Flex align="center" justify="between">
            <p>FlexiHub UI 组件库 - 基于设计令牌的统一设计系统</p>
            <div className="text-sm text-gray-400">当前语言: {locale}</div>
          </Flex>
        </Container>
      </Section>
    </div>
  );
}
