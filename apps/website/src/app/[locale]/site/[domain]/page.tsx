import { <PERSON><PERSON>enderer } from '@/components/renderers/PageRenderer';
import { Metadata } from 'next';
import React from 'react';

interface SitePageProps {
  params: Promise<{
    domain: string;
    locale: string;
  }>;
}

// 错误页面组件
function ErrorPage({
  title,
  message,
  action,
}: {
  action?: React.ReactNode;
  message: string;
  title: string;
}) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-red-50">
      <div className="max-w-md text-center">
        <h1 className="mb-4 text-6xl font-bold text-red-200">😵</h1>
        <h2 className="mb-4 text-2xl font-semibold text-red-700">{title}</h2>
        <p className="mb-6 text-red-600">{message}</p>
        {action}
      </div>
    </div>
  );
}

// 404页面组件
function NotFoundPage({ domain }: { domain: string }) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="max-w-md text-center">
        <h1 className="mb-4 text-6xl font-bold text-gray-300">404</h1>
        <h2 className="mb-4 text-2xl font-semibold text-gray-700">
          网站不存在
        </h2>
        <p className="mb-6 text-gray-600">
          找不到域名为 "<strong>{domain}</strong>" 的网站
        </p>
        <p className="text-sm text-gray-500">
          请检查网址是否正确，或联系网站管理员。
        </p>
      </div>
    </div>
  );
}

// 从API获取网站数据
async function getSiteData(domain: string, locale: string) {
  try {
    // 在服务端渲染时使用内部地址
    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
        : 'http://localhost:3000';

    const response = await fetch(
      `${baseUrl}/api/sites/${domain}?locale=${locale}`,
      {
        headers: {
          'Cache-Control': 'no-cache',
        },
        // 添加超时
        signal: AbortSignal.timeout(5000),
      },
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null; // 网站不存在
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to fetch site data:', error);
    throw error;
  }
}

export default async function SitePage({ params }: SitePageProps) {
  try {
    const resolvedParams = await params;
    const { locale, domain } = resolvedParams;

    const siteData = await getSiteData(domain, locale);

    if (!siteData) {
      return <NotFoundPage domain={domain} />;
    }

    return (
      <div className="min-h-screen">
        {/* SEO 元数据 */}
        <head>
          <title>{siteData.settings?.seo?.title || siteData.title}</title>
          <meta
            content={
              siteData.settings?.seo?.description || siteData.description
            }
            name="description"
          />
          {siteData.settings?.seo?.keywords && (
            <meta
              content={siteData.settings.seo.keywords.join(', ')}
              name="keywords"
            />
          )}
        </head>

        {/* 页面内容 */}
        <PageRenderer pageData={siteData} />
      </div>
    );
  } catch (error) {
    console.error('Site page error:', error);

    return (
      <ErrorPage
        message="加载网站数据时发生错误，请稍后再试。"
        title="服务器错误"
      />
    );
  }
}

// 动态生成元数据
export async function generateMetadata({
  params,
}: SitePageProps): Promise<Metadata> {
  try {
    const resolvedParams = await params;
    const { domain, locale } = resolvedParams;

    const siteData = await getSiteData(domain, locale);

    if (!siteData) {
      return {
        title: '404 - 网站不存在',
        description: `找不到域名为 ${domain} 的网站`,
      };
    }

    return {
      title: siteData.settings?.seo?.title || siteData.title,
      description: siteData.settings?.seo?.description || siteData.description,
      keywords: siteData.settings?.seo?.keywords?.join(', '),
    };
  } catch {
    return {
      title: '服务器错误',
      description: '网站暂时无法访问',
    };
  }
}
