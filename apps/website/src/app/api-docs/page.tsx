import React from 'react';

const API_DOCS = [
  {
    category: '身份认证',
    apis: [
      {
        method: 'POST',
        path: '/api/auth/login',
        description: '用户登录',
        request: {
          username: 'string - 用户名',
          password: 'string - 密码',
        },
        response: {
          message: 'string - 响应消息',
          token: 'string - JWT令牌',
          user: 'object - 用户信息',
        },
        example: {
          request: {
            username: 'admin',
            password: '123456',
          },
          response: {
            message: '登录成功',
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            user: {
              userId: '1',
              username: 'admin',
              tenantId: 'tenant-1',
              permissions: ['admin', 'website:edit'],
            },
          },
        },
      },
    ],
  },
  {
    category: '网站管理',
    apis: [
      {
        method: 'GET',
        path: '/api/sites/{domain}',
        description: '获取网站数据',
        parameters: {
          domain: 'string - 网站域名',
          locale: 'string - 语言代码 (可选)',
          preview: 'boolean - 是否预览模式 (可选)',
        },
        response: {
          id: 'string - 网站ID',
          domain: 'string - 域名',
          title: 'string - 网站标题',
          description: 'string - 网站描述',
          content: 'object - 网站内容结构',
          settings: 'object - 网站设置',
        },
        example: {
          request: 'GET /api/sites/demo-site?locale=zh-CN',
          response: {
            id: 'demo-site',
            domain: 'demo-site',
            title: 'FlexiHub 演示网站',
            description: '这是一个使用 FlexiHub 构建的演示网站',
            content: {
              ROOT: {
                type: 'div',
                nodes: ['container-1'],
              },
            },
            settings: {
              locale: 'zh-CN',
              theme: 'default',
            },
          },
        },
      },
      {
        method: 'PUT',
        path: '/api/sites/{domain}',
        description: '更新网站数据',
        parameters: {
          domain: 'string - 网站域名',
        },
        request: {
          title: 'string - 网站标题 (可选)',
          description: 'string - 网站描述 (可选)',
          content: 'object - 网站内容结构 (可选)',
          settings: 'object - 网站设置 (可选)',
        },
        response: {
          message: 'string - 响应消息',
          site: 'object - 更新后的网站数据',
        },
        example: {
          request: {
            title: '新的网站标题',
            content: {
              ROOT: {
                type: 'div',
                nodes: ['new-container'],
              },
            },
          },
          response: {
            message: '网站更新成功',
            site: {},
          },
        },
      },
    ],
  },
  {
    category: '组件管理',
    apis: [
      {
        method: 'GET',
        path: '/api/components',
        description: '获取可用组件列表',
        response: {
          components: 'array - 组件列表',
        },
        example: {
          request: 'GET /api/components',
          response: {
            components: [
              {
                type: 'Text',
                name: '文本组件',
                category: 'content',
                props: {
                  content: 'string',
                  fontSize: 'string',
                  color: 'string',
                },
              },
            ],
          },
        },
      },
    ],
  },
];

const StatusBadge = ({ method }: { method: string }) => {
  const colors = {
    GET: 'bg-green-100 text-green-800',
    POST: 'bg-blue-100 text-blue-800',
    PUT: 'bg-yellow-100 text-yellow-800',
    DELETE: 'bg-red-100 text-red-800',
  };

  return (
    <span
      className={`inline-block rounded px-2 py-1 text-xs font-medium ${colors[method as keyof typeof colors]}`}
    >
      {method}
    </span>
  );
};

export default function ApiDocsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-6xl px-4">
        <div className="mb-8">
          <h1 className="mb-4 text-4xl font-bold text-gray-900">
            FlexiHub API 文档
          </h1>
          <p className="text-lg text-gray-600">
            FlexiHub 平台提供的 RESTful API 接口文档
          </p>
        </div>

        {/* API 基础信息 */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-2xl font-semibold">基础信息</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900">Base URL</h3>
              <code className="rounded bg-gray-100 px-2 py-1 text-sm">
                http://localhost:3000/api
              </code>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">认证方式</h3>
              <p className="text-gray-600">
                JWT Token，通过 Cookie 或 Authorization Header 传递
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">响应格式</h3>
              <p className="text-gray-600">JSON</p>
            </div>
          </div>
        </div>

        {/* API 列表 */}
        {API_DOCS.map((category, categoryIndex) => (
          <div className="mb-8" key={categoryIndex}>
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              {category.category}
            </h2>

            <div className="space-y-6">
              {category.apis.map((api, apiIndex) => (
                <div
                  className="rounded-lg bg-white p-6 shadow-sm"
                  key={apiIndex}
                >
                  <div className="mb-4 flex items-center space-x-3">
                    <StatusBadge method={api.method} />
                    <code className="font-mono text-lg">{api.path}</code>
                  </div>

                  <p className="mb-4 text-gray-600">{api.description}</p>

                  {/* 请求参数 */}
                  {(api.request || api.parameters) && (
                    <div className="mb-4">
                      <h4 className="mb-2 font-medium text-gray-900">
                        {api.request ? '请求体' : '参数'}
                      </h4>
                      <div className="rounded bg-gray-50 p-4">
                        <pre className="text-sm">
                          {JSON.stringify(
                            api.request || api.parameters,
                            null,
                            2,
                          )}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* 响应 */}
                  <div className="mb-4">
                    <h4 className="mb-2 font-medium text-gray-900">响应</h4>
                    <div className="rounded bg-gray-50 p-4">
                      <pre className="text-sm">
                        {JSON.stringify(api.response, null, 2)}
                      </pre>
                    </div>
                  </div>

                  {/* 示例 */}
                  {api.example && (
                    <div>
                      <h4 className="mb-2 font-medium text-gray-900">示例</h4>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-gray-700">
                            请求
                          </h5>
                          <div className="rounded bg-gray-900 p-4 text-gray-100">
                            <pre className="text-sm">
                              {typeof api.example.request === 'string'
                                ? api.example.request
                                : JSON.stringify(api.example.request, null, 2)}
                            </pre>
                          </div>
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-gray-700">
                            响应
                          </h5>
                          <div className="rounded bg-gray-900 p-4 text-gray-100">
                            <pre className="text-sm">
                              {JSON.stringify(api.example.response, null, 2)}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
