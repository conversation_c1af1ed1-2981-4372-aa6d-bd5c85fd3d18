import { NextRequest, NextResponse } from 'next/server';

// 模拟网站数据库
const MOCK_SITES = {
  'demo-site': {
    id: 'demo-site',
    domain: 'demo-site',
    title: 'FlexiHub 演示网站',
    description: '这是一个使用 FlexiHub 构建的演示网站',
    tenantId: 'tenant-1',
    isPublished: true,
    content: {
      ROOT: {
        type: 'div',
        nodes: ['container-1'],
      },
      'container-1': {
        type: 'Container',
        props: {
          maxWidth: 'xl',
          padding: 'lg',
          textAlign: 'center',
          background: '#f8fafc',
        },
        nodes: ['text-1', 'text-2'],
      },
      'text-1': {
        type: 'Text',
        props: {
          content: '欢迎访问 FlexiHub 演示网站！',
          tag: 'h1',
          fontSize: '3xl',
          fontWeight: 'bold',
          textAlign: 'center',
          color: '#1f2937',
        },
      },
      'text-2': {
        type: 'Text',
        props: {
          content: '这是使用统一架构构建的网站，支持可视化编辑和用户访问。',
          tag: 'p',
          fontSize: 'lg',
          textAlign: 'center',
          color: '#6b7280',
        },
      },
    },
    settings: {
      locale: 'zh-CN',
      theme: 'default',
      seo: {
        title: 'FlexiHub 演示网站',
        description: '这是一个使用 FlexiHub 构建的演示网站',
        keywords: ['FlexiHub', '网站构建', '可视化编辑'],
      },
    },
  },
  'my-blog': {
    id: 'my-blog',
    domain: 'my-blog',
    title: '我的博客',
    description: '个人博客网站',
    tenantId: 'tenant-2',
    isPublished: true,
    content: {
      ROOT: {
        type: 'div',
        nodes: ['container-1'],
      },
      'container-1': {
        type: 'Container',
        props: {
          maxWidth: 'lg',
          padding: 'md',
        },
        nodes: ['text-1'],
      },
      'text-1': {
        type: 'Text',
        props: {
          content: '欢迎来到我的博客！',
          tag: 'h1',
          fontSize: '2xl',
          fontWeight: 'semibold',
        },
      },
    },
    settings: {
      locale: 'zh-CN',
      theme: 'default',
    },
  },
};

export async function GET(
  request: NextRequest,
  { params }: { params: { domain: string } },
) {
  try {
    const { domain } = params;
    const url = new URL(request.url);
    const locale = url.searchParams.get('locale') || 'zh-CN';

    // 查找网站数据
    const site = MOCK_SITES[domain as keyof typeof MOCK_SITES];

    if (!site) {
      return NextResponse.json(
        { message: `网站 "${domain}" 不存在` },
        { status: 404 },
      );
    }

    if (!site.isPublished) {
      return NextResponse.json({ message: '网站尚未发布' }, { status: 403 });
    }

    // 返回网站数据
    return NextResponse.json({
      ...site,
      settings: {
        ...site.settings,
        locale,
      },
    });
  } catch (error) {
    console.error('Get site error:', error);
    return NextResponse.json({ message: '服务器错误' }, { status: 500 });
  }
}
