import { generateMockToken } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

interface LoginRequest {
  username: string;
  password: string;
}

// 模拟用户数据库
const MOCK_USERS = [
  {
    userId: '1',
    username: 'admin',
    password: '123456',
    tenantId: 'tenant-1',
    permissions: ['admin', 'website:edit', 'website:view'],
  },
  {
    userId: '2',
    username: 'editor',
    password: '123456',
    tenantId: 'tenant-1',
    permissions: ['website:edit', 'website:view'],
  },
  {
    userId: '3',
    username: 'viewer',
    password: '123456',
    tenantId: 'tenant-2',
    permissions: ['website:view'],
  },
];

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    const { username, password } = body;

    // 验证输入
    if (!username || !password) {
      return NextResponse.json(
        { message: '用户名和密码不能为空' },
        { status: 400 },
      );
    }

    // 查找用户
    const user = MOCK_USERS.find(
      (u) => u.username === username && u.password === password,
    );

    if (!user) {
      return NextResponse.json(
        { message: '用户名或密码错误' },
        { status: 401 },
      );
    }

    // 生成JWT令牌
    const token = generateMockToken({
      userId: user.userId,
      username: user.username,
      tenantId: user.tenantId,
      permissions: user.permissions,
    });

    return NextResponse.json({
      message: '登录成功',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        tenantId: user.tenantId,
        permissions: user.permissions,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({ message: '服务器错误' }, { status: 500 });
  }
}
