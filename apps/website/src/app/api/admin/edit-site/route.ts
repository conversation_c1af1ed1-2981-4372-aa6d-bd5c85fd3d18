import { generateMockToken } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

interface EditSiteRequest {
  siteId: string;
  userId: string;
  tenantId: string;
  adminToken?: string;
}

/**
 * 管理后台跳转到编辑器接口
 * POST /api/admin/edit-site
 */
export async function POST(request: NextRequest) {
  try {
    const body: EditSiteRequest = await request.json();
    const { siteId, userId, tenantId, adminToken } = body;

    // 验证输入
    if (!siteId || !userId || !tenantId) {
      return NextResponse.json({ message: '缺少必要参数' }, { status: 400 });
    }

    // 验证管理后台令牌（可选，增强安全性）
    const expectedAdminToken = 'flexihub-admin-secret-2024';
    if (adminToken && adminToken !== expectedAdminToken) {
      return NextResponse.json(
        { message: '无效的管理后台令牌' },
        { status: 401 },
      );
    }

    // 模拟从数据库获取用户信息
    const userInfo = {
      userId,
      username: `user-${userId}`,
      tenantId,
      permissions: ['website:edit', 'website:view'], // 编辑权限
    };

    // 生成JWT令牌用于编辑器访问
    const token = generateMockToken(userInfo);

    // 构建编辑器URL
    const editorUrl = `/zh-CN/editor/${siteId}`;
    const websiteBaseUrl =
      process.env.NEXT_PUBLIC_WEBSITE_URL || 'http://localhost:3000';
    const fullEditorUrl = `${websiteBaseUrl}${editorUrl}`;

    return NextResponse.json({
      success: true,
      message: '跳转链接生成成功',
      data: {
        editorUrl: fullEditorUrl,
        token,
        siteId,
        userId,
        tenantId,
        // 客户端需要设置的cookie信息
        cookieInfo: {
          name: 'auth-token',
          value: token,
          options: {
            maxAge: 7 * 24 * 60 * 60, // 7天
            httpOnly: false, // 允许客户端访问
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
          },
        },
      },
    });
  } catch (error) {
    console.error('编辑器跳转错误:', error);
    return NextResponse.json({ message: '服务器错误' }, { status: 500 });
  }
}

/**
 * 获取网站编辑权限
 * GET /api/admin/edit-site?siteId=xxx&userId=xxx&tenantId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const siteId = searchParams.get('siteId');
    const userId = searchParams.get('userId');
    const tenantId = searchParams.get('tenantId');

    if (!siteId || !userId || !tenantId) {
      return NextResponse.json({ message: '缺少必要参数' }, { status: 400 });
    }

    // 检查用户权限（模拟）
    const hasPermission = true; // 在真实项目中应该查询数据库

    if (!hasPermission) {
      return NextResponse.json({ message: '用户无编辑权限' }, { status: 403 });
    }

    // 生成跳转信息
    return NextResponse.json({
      success: true,
      data: {
        canEdit: true,
        siteId,
        userId,
        tenantId,
        editorUrl: `/zh-CN/editor/${siteId}`,
      },
    });
  } catch (error) {
    console.error('权限检查错误:', error);
    return NextResponse.json({ message: '服务器错误' }, { status: 500 });
  }
}
