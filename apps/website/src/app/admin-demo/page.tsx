'use client';

import React, { useState } from 'react';

interface JumpToEditorData {
  siteId: string;
  userId: string;
  tenantId: string;
  adminToken: string;
}

export default function AdminDemoPage() {
  const [formData, setFormData] = useState<JumpToEditorData>({
    siteId: 'demo-site',
    userId: '1',
    tenantId: 'tenant-1',
    adminToken: 'flexihub-admin-secret-2024',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleJumpToEditor = async () => {
    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/admin/edit-site', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '请求失败');
      }

      setResult(data);

      // 自动跳转到编辑器（带token）
      if (data.success && data.data.token) {
        const editorUrl = `/zh-CN/editor/${formData.siteId}?token=${data.data.token}`;
        window.open(editorUrl, '_blank');
      }
    } catch (error_) {
      setError(error_ instanceof Error ? error_.message : '请求失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDirectJump = () => {
    // 直接跳转（会被重定向到登录页面）
    const editorUrl = `/zh-CN/editor/${formData.siteId}`;
    window.open(editorUrl, '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-4xl px-4">
        <div className="mb-8">
          <h1 className="mb-4 text-4xl font-bold text-gray-900">
            管理后台演示页面
          </h1>
          <p className="text-lg text-gray-600">
            模拟从Vue3管理后台跳转到Next.js编辑器的功能
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* 配置表单 */}
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-2xl font-semibold">跳转配置</h2>

            <div className="space-y-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  网站ID
                </label>
                <input
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
                  name="siteId"
                  onChange={handleInputChange}
                  placeholder="demo-site"
                  type="text"
                  value={formData.siteId}
                />
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  用户ID
                </label>
                <input
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
                  name="userId"
                  onChange={handleInputChange}
                  placeholder="1"
                  type="text"
                  value={formData.userId}
                />
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  租户ID
                </label>
                <input
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
                  name="tenantId"
                  onChange={handleInputChange}
                  placeholder="tenant-1"
                  type="text"
                  value={formData.tenantId}
                />
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  管理后台令牌
                </label>
                <input
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
                  name="adminToken"
                  onChange={handleInputChange}
                  placeholder="flexihub-admin-secret-2024"
                  type="text"
                  value={formData.adminToken}
                />
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <button
                className="w-full rounded-lg bg-blue-600 px-4 py-3 text-white hover:bg-blue-700 disabled:opacity-50"
                disabled={isLoading}
                onClick={handleJumpToEditor}
              >
                {isLoading ? '生成跳转链接中...' : '🚀 免登录跳转到编辑器'}
              </button>

              <button
                className="w-full rounded-lg bg-gray-600 px-4 py-3 text-white hover:bg-gray-700"
                onClick={handleDirectJump}
              >
                🔒 直接跳转（需要登录）
              </button>
            </div>

            {error && (
              <div className="mt-4 rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
                {error}
              </div>
            )}
          </div>

          {/* 结果显示 */}
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-2xl font-semibold">API响应结果</h2>

            {result ? (
              <div className="space-y-4">
                <div className="rounded bg-green-50 p-4">
                  <h3 className="mb-2 font-medium text-green-800">✅ 成功</h3>
                  <p className="text-sm text-green-700">{result.message}</p>
                </div>

                <div className="rounded bg-gray-50 p-4">
                  <h4 className="mb-2 font-medium text-gray-800">返回数据</h4>
                  <pre className="overflow-auto text-sm text-gray-600">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>

                {result.data.editorUrl && (
                  <div className="rounded bg-blue-50 p-4">
                    <h4 className="mb-2 font-medium text-blue-800">
                      编辑器链接
                    </h4>
                    <a
                      className="break-all text-blue-600 hover:text-blue-800"
                      href={`${result.data.editorUrl}?token=${result.data.token}`}
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      {result.data.editorUrl}?token=...
                    </a>
                  </div>
                )}
              </div>
            ) : (
              <div className="py-8 text-center text-gray-500">
                点击上方按钮生成跳转链接
              </div>
            )}
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-2xl font-semibold">使用说明</h2>

          <div className="space-y-4 text-gray-600">
            <div>
              <h3 className="font-medium text-gray-800">🚀 免登录跳转</h3>
              <p>
                调用API生成带token的跳转链接，用户可以直接进入编辑器，无需再次登录。
                适用于管理后台"编辑网站"功能。
              </p>
            </div>

            <div>
              <h3 className="font-medium text-gray-800">🔒 直接跳转</h3>
              <p>
                直接访问编辑器URL，会被权限中间件拦截并重定向到登录页面。
                适用于用户直接访问编辑器的场景。
              </p>
            </div>

            <div>
              <h3 className="font-medium text-gray-800">🔧 集成方式</h3>
              <p>在Vue3管理后台中，在"编辑网站"按钮的点击事件中：</p>
              <ol className="mt-2 list-inside list-decimal space-y-1 text-sm">
                <li>
                  调用 <code>POST /api/admin/edit-site</code> 接口
                </li>
                <li>获取返回的编辑器URL和token</li>
                <li>打开新窗口并跳转到编辑器</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
