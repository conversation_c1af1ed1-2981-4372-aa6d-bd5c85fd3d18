/**
 * 环境变量配置管理
 */

interface EnvConfig {
  // 应用配置
  NODE_ENV: string;
  APP_MODE: string;
  EDITOR_MODE: boolean;

  // API配置
  API_URL: string;
  WEBSITE_URL: string;

  // 平台域名
  PLATFORM_DOMAIN: string;

  // 管理后台
  ADMIN_URL: string;
  ADMIN_SECRET_KEY: string;

  // JWT配置
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;

  // 开发模式
  DEV_MODE: boolean;
  DEBUG: boolean;

  // 功能开关
  CUSTOM_DOMAIN_MODE: boolean;
  MOCK_DATA_MODE: boolean;
}

/**
 * 获取环境变量配置
 */
export const env: EnvConfig = {
  // 应用配置
  NODE_ENV: process.env.NODE_ENV || 'development',
  APP_MODE: process.env.APP_MODE || 'unified',
  EDITOR_MODE: process.env.EDITOR_MODE === 'true',

  // API配置
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  WEBSITE_URL: process.env.NEXT_PUBLIC_WEBSITE_URL || 'http://localhost:3000',

  // 平台域名
  PLATFORM_DOMAIN: process.env.PLATFORM_DOMAIN || 'flexihub.com',

  // 管理后台
  ADMIN_URL: process.env.NEXT_PUBLIC_ADMIN_URL || 'http://localhost:5173',
  ADMIN_SECRET_KEY:
    process.env.ADMIN_SECRET_KEY || 'flexihub-admin-secret-2024',

  // JWT配置
  JWT_SECRET: process.env.JWT_SECRET || 'flexihub-jwt-secret-2024',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',

  // 开发模式
  DEV_MODE:
    process.env.NEXT_PUBLIC_DEV_MODE === 'true' ||
    process.env.NODE_ENV === 'development',
  DEBUG: process.env.NEXT_PUBLIC_DEBUG === 'true',

  // 功能开关
  CUSTOM_DOMAIN_MODE: process.env.CUSTOM_DOMAIN_MODE === 'true',
  MOCK_DATA_MODE: process.env.MOCK_DATA_MODE !== 'false', // 默认开启
};

/**
 * 检查必要的环境变量是否配置
 */
export function validateEnvConfig(): void {
  const requiredVars = ['API_URL', 'WEBSITE_URL', 'ADMIN_URL'];

  const missingVars = requiredVars.filter((varName) => {
    const value = env[varName as keyof EnvConfig];
    return !value || value === '';
  });

  if (missingVars.length > 0) {
    console.warn('⚠️ 缺少环境变量配置:', missingVars);
    console.warn('请创建 .env.local 文件并配置必要的环境变量');
  }
}

/**
 * 开发环境辅助函数
 */
export const isDev = env.NODE_ENV === 'development';
export const isProd = env.NODE_ENV === 'production';
export const isDebug = env.DEBUG && isDev;

/**
 * API URL 构建器
 */
export function buildApiUrl(endpoint: string): string {
  return `${env.API_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
}

/**
 * 网站URL构建器
 */
export function buildWebsiteUrl(path: string = ''): string {
  return `${env.WEBSITE_URL}${path.startsWith('/') ? path : `/${path}`}`;
}

/**
 * 管理后台URL构建器
 */
export function buildAdminUrl(path: string = ''): string {
  return `${env.ADMIN_URL}${path.startsWith('/') ? path : `/${path}`}`;
}

// 在开发环境下验证配置
if (isDev) {
  validateEnvConfig();
}
