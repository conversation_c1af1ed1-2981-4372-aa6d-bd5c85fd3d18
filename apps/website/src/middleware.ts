import { jwtDecode } from 'jwt-decode';
import createIntlMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';

import { defaultLocale, locales } from './lib/i18n';
import { detectAndRedirectLocale } from './lib/locale-utils';

interface JWTPayload {
  userId: string;
  tenantId: string;
  permissions: string[];
  exp: number;
}

/**
 * next-intl 中间件配置
 */
const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always', // 始终显示语言前缀
});

/**
 * 检查JWT令牌是否有效
 */
function validateJWTToken(token: string): JWTPayload | null {
  try {
    const decoded = jwtDecode<JWTPayload>(token);

    // 检查令牌是否过期
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error('JWT decode error:', error);
    return null;
  }
}

/**
 * 获取当前路径的语言信息
 */
function extractLocaleFromRequest(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const segments = pathname.split('/');
  const potentialLocale = segments[1];

  // 检查是否为有效语言
  if (potentialLocale && locales.includes(potentialLocale as any)) {
    return {
      locale: potentialLocale,
      pathWithoutLocale: `/${segments.slice(2).join('/')}`,
    };
  }

  return {
    locale: defaultLocale,
    pathWithoutLocale: pathname,
  };
}

/**
 * 构建带语言前缀的URL
 */
function buildLocalizedUrl(
  request: NextRequest,
  path: string,
  locale?: string,
) {
  const targetLocale = locale || defaultLocale;
  const baseUrl = new URL(request.url);
  baseUrl.pathname = `/${targetLocale}${path}`;
  return baseUrl;
}

/**
 * 检查编辑器权限
 */
function checkEditorPermission(
  request: NextRequest,
  locale: string,
): NextResponse {
  const { searchParams } = request.nextUrl;
  const { pathWithoutLocale } = extractLocaleFromRequest(request);

  // 检查URL参数中的token（用于管理后台跳转）
  const urlToken = searchParams.get('token');

  // 从cookie或header中获取JWT令牌
  let token =
    request.cookies.get('auth-token')?.value ||
    request.headers.get('Authorization')?.replace('Bearer ', '');

  // 如果URL中有token，优先使用URL中的token
  if (urlToken) {
    token = urlToken;

    // 验证URL token
    const payload = validateJWTToken(urlToken);
    if (payload) {
      // token有效，设置cookie并重定向（去除URL中的token）
      const redirectUrl = buildLocalizedUrl(request, pathWithoutLocale, locale);
      const response = NextResponse.redirect(redirectUrl);
      response.cookies.set('auth-token', urlToken, {
        maxAge: 7 * 24 * 60 * 60, // 7天
        httpOnly: false,
        secure: false, // 开发环境设为false
        sameSite: 'lax',
      });
      return response;
    }
  }

  if (!token) {
    // 重定向到登录页面
    const loginUrl = buildLocalizedUrl(request, '/login', locale);
    loginUrl.searchParams.set('redirect', pathWithoutLocale);
    return NextResponse.redirect(loginUrl);
  }

  const payload = validateJWTToken(token);
  if (!payload) {
    // 令牌无效，重定向到登录页面
    const loginUrl = buildLocalizedUrl(request, '/login', locale);
    loginUrl.searchParams.set('redirect', pathWithoutLocale);
    return NextResponse.redirect(loginUrl);
  }

  // 检查编辑器权限
  const hasEditorPermission =
    payload.permissions.includes('website:edit') ||
    payload.permissions.includes('admin');

  if (!hasEditorPermission) {
    // 无权限，返回403页面
    return new NextResponse('Forbidden', { status: 403 });
  }

  // 权限验证通过，继续请求
  return NextResponse.next();
}

/**
 * 检查登录页面访问
 */
function checkLoginPageAccess(
  request: NextRequest,
  locale: string,
): NextResponse {
  // 如果已经有有效token，重定向到主页
  const token = request.cookies.get('auth-token')?.value;

  if (token) {
    const payload = validateJWTToken(token);
    if (payload) {
      // 获取重定向URL
      const { searchParams } = request.nextUrl;
      const redirectPath = searchParams.get('redirect') || '/';
      const redirectUrl = buildLocalizedUrl(request, redirectPath, locale);
      return NextResponse.redirect(redirectUrl);
    }
  }

  return NextResponse.next();
}

/**
 * 处理语言检测和重定向
 */
function handleLanguageDetection(request: NextRequest): NextResponse | null {
  const pathname = request.nextUrl.pathname;

  // 如果路径已经包含语言前缀，不需要重定向
  const segments = pathname.split('/');
  const potentialLocale = segments[1];
  if (potentialLocale && locales.includes(potentialLocale as any)) {
    return null;
  }

  // 检测浏览器语言偏好
  const acceptLanguage = request.headers.get('Accept-Language');
  const { shouldRedirect, redirectPath } = detectAndRedirectLocale(
    pathname,
    acceptLanguage || undefined,
  );

  if (shouldRedirect && redirectPath) {
    return NextResponse.redirect(new URL(redirectPath, request.url));
  }

  // 如果没有语言前缀，重定向到默认语言
  if (!potentialLocale || !locales.includes(potentialLocale as any)) {
    const redirectUrl = new URL(`/${defaultLocale}${pathname}`, request.url);
    return NextResponse.redirect(redirectUrl);
  }

  return null;
}

/**
 * 主中间件函数
 */
export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 静态资源和API路由跳过处理
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // 1. 首先处理语言检测和重定向
  const languageRedirect = handleLanguageDetection(request);
  if (languageRedirect) {
    return languageRedirect;
  }

  // 2. 提取语言信息
  const { locale, pathWithoutLocale } = extractLocaleFromRequest(request);

  // 3. API文档页面无需验证
  if (pathWithoutLocale.includes('/api-docs')) {
    return intlMiddleware(request);
  }

  // 4. 登录页面特殊处理
  if (pathWithoutLocale.includes('/login')) {
    const authResponse = checkLoginPageAccess(request, locale);
    if (authResponse.status === 302) {
      return authResponse; // 重定向响应
    }
    return intlMiddleware(request);
  }

  // 5. 编辑器路由权限检查（包括预览）
  if (pathWithoutLocale.includes('/editor/')) {
    const authResponse = checkEditorPermission(request, locale);
    if (authResponse.status === 302 || authResponse.status === 403) {
      return authResponse; // 重定向或权限拒绝响应
    }
    return intlMiddleware(request);
  }

  // 6. 其他路由使用国际化中间件处理
  return intlMiddleware(request);
}

/**
 * 中间件配置 - 指定哪些路径需要执行中间件
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了：
     * 1. /api/ (API routes)
     * 2. /_next/ (Next.js internals)
     * 3. /favicon.ico (favicon file)
     * 4. 静态文件 (.*)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
