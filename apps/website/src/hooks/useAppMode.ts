'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

export interface AppModeInfo {
  /** 是否为编辑器模式 */
  isEditor: boolean;
  /** 是否为用户网站模式 */
  isUserSite: boolean;
  /** 是否为预览模式 */
  isPreview: boolean;
  /** 应用模式 */
  mode: 'editor' | 'preview' | 'site';
  /** 网站ID（编辑器模式下） */
  siteId?: string;
  /** 域名（用户网站模式下） */
  domain?: string;
  /** 当前语言 */
  locale?: string;
}

/**
 * 应用模式检测Hook
 * 用于判断当前是编辑器模式还是用户网站渲染模式
 */
export function useAppMode(): AppModeInfo {
  const pathname = usePathname();

  return useMemo(() => {
    // 解析路径
    const pathSegments = pathname.split('/').filter(Boolean);
    const locale = pathSegments[0];

    // 检查是否为编辑器模式
    const isEditor = pathname.includes('/editor/');
    const isPreview = pathname.includes('/preview');
    const isUserSite = !isEditor;

    // 提取网站ID（编辑器模式）
    let siteId: string | undefined;
    if (isEditor) {
      const editorMatch = pathname.match(/\/editor\/([^/]+)/);
      siteId = editorMatch?.[1];
    }

    // 提取域名（用户网站模式）
    let domain: string | undefined;
    if (isUserSite && pathSegments.length >= 2) {
      domain = pathSegments[1]; // 第二个路径段是域名
    }

    // 确定模式
    let mode: 'editor' | 'preview' | 'site';
    if (isPreview) {
      mode = 'preview';
    } else if (isEditor) {
      mode = 'editor';
    } else {
      mode = 'site';
    }

    return {
      isEditor,
      isUserSite,
      isPreview,
      mode,
      siteId,
      domain,
      locale,
    };
  }, [pathname]);
}

/**
 * 生成编辑器路径
 */
export function generateEditorPath(siteId: string, locale = 'zh-CN'): string {
  return `/${locale}/editor/${siteId}`;
}

/**
 * 生成用户网站路径
 */
export function generateSitePath(
  domain: string,
  locale = 'zh-CN',
  path = '',
): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `/${locale}/${domain}${cleanPath}`;
}

/**
 * 生成预览路径
 */
export function generatePreviewPath(siteId: string, locale = 'zh-CN'): string {
  return `/${locale}/editor/${siteId}/preview`;
}
