import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

// 支持的语言列表
export const locales = ['zh-CN', 'en-US', 'ja-JP'] as const;
export type Locale = (typeof locales)[number];

// 默认语言
export const defaultLocale: Locale = 'zh-CN';

// 语言配置信息
export const localeConfig = {
  'zh-CN': {
    name: '简体中文',
    nativeName: '简体中文',
    flag: '🇨🇳',
    direction: 'ltr' as const,
  },
  'en-US': {
    name: 'English (US)',
    nativeName: 'English (US)',
    flag: '🇺🇸',
    direction: 'ltr' as const,
  },
  'ja-JP': {
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    direction: 'ltr' as const,
  },
} as const;

// 验证语言是否支持
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

// 获取语言显示名称
export function getLocaleName(locale: Locale, displayLocale?: Locale): string {
  if (displayLocale === locale) {
    return localeConfig[locale].nativeName;
  }
  return localeConfig[locale].name;
}

// Next-intl 配置
export default getRequestConfig(async ({ locale }) => {
  // 验证传入的locale是否支持
  if (!isValidLocale(locale)) {
    notFound();
  }

  try {
    // 动态导入对应的语言包
    const messagesModule = await import(`../messages/${locale}.json`);
    const messages = messagesModule.default;

    return {
      messages,
      timeZone: getTimeZone(locale),
      now: new Date(),
    };
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    notFound();
  }
});

// 根据语言获取时区
function getTimeZone(locale: Locale): string {
  const timeZoneMap: Record<Locale, string> = {
    'zh-CN': 'Asia/Shanghai',
    'en-US': 'America/New_York',
    'ja-JP': 'Asia/Tokyo',
  };

  return timeZoneMap[locale] || 'UTC';
}

// 语言路径处理工具
export function getLocalizedPath(path: string, locale: Locale): string {
  // 如果路径已经包含语言前缀，先移除
  const cleanPath = path.replace(/^\/[a-z]{2}-[A-Z]{2}/, '');

  // 添加新的语言前缀
  return `/${locale}${cleanPath}`;
}

// 从路径中提取语言
export function extractLocaleFromPath(path: string): {
  locale: Locale;
  pathname: string;
} {
  const segments = path.split('/');
  const potentialLocale = segments[1];

  if (potentialLocale && isValidLocale(potentialLocale)) {
    return {
      locale: potentialLocale,
      pathname: `/${segments.slice(2).join('/')}`,
    };
  }

  return {
    locale: defaultLocale,
    pathname: path,
  };
}
