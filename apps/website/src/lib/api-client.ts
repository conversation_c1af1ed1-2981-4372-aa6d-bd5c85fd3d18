import { getCookie } from './auth';

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  status: number;
}

export interface Site {
  id: string;
  domain: string;
  title: string;
  description: string;
  content: any;
  settings: {
    locale: string;
    seo?: {
      description: string;
      keywords: string[];
      title: string;
    };
    theme: string;
  };
}

export interface User {
  userId: string;
  username: string;
  tenantId: string;
  permissions: string[];
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  token: string;
  user: User;
}

/**
 * API 客户端配置
 */
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10_000,
  retries: 3,
};

/**
 * 请求拦截器
 */
function createRequestInterceptor() {
  return {
    headers: {
      'Content-Type': 'application/json',
      // 自动添加认证令牌
      ...(getCookie('auth-token') && {
        Authorization: `Bearer ${getCookie('auth-token')}`,
      }),
    },
  };
}

/**
 * 响应拦截器
 */
async function responseInterceptor<T>(
  response: Response,
): Promise<ApiResponse<T>> {
  const status = response.status;

  try {
    const data = await response.json();

    if (!response.ok) {
      return {
        status,
        error: data.message || `HTTP ${status}`,
        data: null,
      };
    }

    return {
      status,
      data,
      message: data.message,
    };
  } catch (error) {
    return {
      status,
      error: `解析响应失败: ${error}`,
      data: null,
    };
  }
}

/**
 * 基础请求函数
 */
async function request<T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<ApiResponse<T>> {
  const url = `${API_CONFIG.baseURL}${endpoint}`;
  const requestConfig = createRequestInterceptor();

  const config: RequestInit = {
    ...options,
    headers: {
      ...requestConfig.headers,
      ...options.headers,
    },
    // 添加超时控制
    signal: AbortSignal.timeout(API_CONFIG.timeout),
  };

  try {
    const response = await fetch(url, config);
    return await responseInterceptor<T>(response);
  } catch (error) {
    if (error instanceof Error) {
      return {
        status: 0,
        error: error.message,
        data: null,
      };
    }

    return {
      status: 0,
      error: '未知错误',
      data: null,
    };
  }
}

/**
 * API 客户端类
 */
export const ApiClient = {
  // 身份认证相关
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // 网站数据相关
  async getSite(
    domain: string,
    options: { locale?: string; preview?: boolean } = {},
  ): Promise<ApiResponse<Site>> {
    const searchParams = new URLSearchParams();
    if (options.locale) searchParams.append('locale', options.locale);
    if (options.preview) searchParams.append('preview', 'true');

    const query = searchParams.toString();
    const endpoint = `/sites/${domain}${query ? `?${query}` : ''}`;

    return request<Site>(endpoint);
  },

  async updateSite(
    domain: string,
    siteData: Partial<Site>,
  ): Promise<ApiResponse<Site>> {
    return request<Site>(`/sites/${domain}`, {
      method: 'PUT',
      body: JSON.stringify(siteData),
    });
  },

  async publishSite(domain: string): Promise<ApiResponse<{ message: string }>> {
    return request<{ message: string }>(`/sites/${domain}/publish`, {
      method: 'POST',
    });
  },

  // 组件相关
  async getComponents(): Promise<ApiResponse<{ components: any[] }>> {
    return request<{ components: any[] }>('/components');
  },

  // 用户管理
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return request<User>('/auth/me');
  },

  // 租户管理
  async getTenantSites(tenantId: string): Promise<ApiResponse<Site[]>> {
    return request<Site[]>(`/tenants/${tenantId}/sites`);
  },
};

/**
 * React Hook 风格的 API 调用
 */
export function useApi() {
  return {
    login: ApiClient.login,
    getSite: ApiClient.getSite,
    updateSite: ApiClient.updateSite,
    publishSite: ApiClient.publishSite,
    getComponents: ApiClient.getComponents,
    getCurrentUser: ApiClient.getCurrentUser,
    getTenantSites: ApiClient.getTenantSites,
  };
}

/**
 * 错误处理工具
 */
export function handleApiError(response: ApiResponse): string {
  if (response.error) {
    return response.error;
  }

  if (response.status >= 400) {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问，请重新登录',
      403: '权限不足',
      404: '资源不存在',
      500: '服务器内部错误',
    };

    return statusMessages[response.status] || `请求失败 (${response.status})`;
  }

  return '未知错误';
}

/**
 * 成功响应检查
 */
export function isApiSuccess<T>(
  response: ApiResponse<T>,
): response is ApiResponse<T> & { data: T } {
  return (
    response.status >= 200 && response.status < 300 && response.data !== null
  );
}
