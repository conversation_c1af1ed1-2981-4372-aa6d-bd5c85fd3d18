'use client';

import type { Locale } from './i18n';

import Cookies from 'js-cookie';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback } from 'react';

import {
  defaultLocale,
  getLocalizedPath,
  isValidLocale,
  locales,
} from './i18n';

/**
 * 语言Cookie配置
 */
const LOCALE_COOKIE_NAME = 'NEXT_LOCALE';
const LOCALE_COOKIE_OPTIONS = {
  expires: 365, // 1年
  sameSite: 'lax' as const,
  secure: false, // 开发环境设为false，生产环境应设为true
};

/**
 * 获取当前语言
 */
export function getCurrentLocale(): Locale {
  // 优先从Cookie获取
  const cookieLocale = Cookies.get(LOCALE_COOKIE_NAME);
  if (cookieLocale && isValidLocale(cookieLocale)) {
    return cookieLocale;
  }

  // 从浏览器语言获取
  if (typeof window !== 'undefined') {
    const browserLocale = getBrowserLocale();
    if (browserLocale) {
      return browserLocale;
    }
  }

  return defaultLocale;
}

/**
 * 设置当前语言
 */
export function setCurrentLocale(locale: Locale): void {
  if (!isValidLocale(locale)) {
    console.warn(`Invalid locale: ${locale}`);
    return;
  }

  Cookies.set(LOCALE_COOKIE_NAME, locale, LOCALE_COOKIE_OPTIONS);
}

/**
 * 获取浏览器首选语言
 */
export function getBrowserLocale(): Locale | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const browserLang = navigator.language || navigator.languages?.[0];
  if (!browserLang) {
    return null;
  }

  // 精确匹配
  if (isValidLocale(browserLang)) {
    return browserLang;
  }

  // 语言前缀匹配
  const langPrefix = browserLang.split('-')[0];
  const matchedLocale = locales.find((locale) => locale.startsWith(langPrefix));

  return matchedLocale || null;
}

/**
 * 语言切换Hook
 */
export function useLocaleSwitch() {
  const router = useRouter();
  const pathname = usePathname();

  const switchLocale = useCallback(
    (newLocale: Locale) => {
      if (!isValidLocale(newLocale)) {
        console.warn(`Invalid locale: ${newLocale}`);
        return;
      }

      // 保存到Cookie
      setCurrentLocale(newLocale);

      // 构建新路径
      const newPath = getLocalizedPath(pathname, newLocale);

      // 导航到新路径
      router.push(newPath);
    },
    [router, pathname],
  );

  return {
    currentLocale: getCurrentLocale(),
    switchLocale,
    availableLocales: locales,
  };
}

/**
 * 从路径中获取语言
 */
export function getLocaleFromPath(path: string): Locale {
  const segments = path.split('/');
  const potentialLocale = segments[1];

  if (potentialLocale && isValidLocale(potentialLocale)) {
    return potentialLocale;
  }

  return defaultLocale;
}

/**
 * 移除路径中的语言前缀
 */
export function removeLocaleFromPath(path: string): string {
  const segments = path.split('/');
  const potentialLocale = segments[1];

  if (potentialLocale && isValidLocale(potentialLocale)) {
    return `/${segments.slice(2).join('/')}`;
  }

  return path;
}

/**
 * 语言方向检测
 */
export function getTextDirection(locale: Locale): 'ltr' | 'rtl' {
  // 目前支持的语言都是从左到右
  // 如果后续添加阿拉伯语或希伯来语，需要在这里处理
  const rtlLocales: string[] = []; // 如: ['ar-SA', 'he-IL']

  return rtlLocales.includes(locale) ? 'rtl' : 'ltr';
}

/**
 * 检查是否为默认语言
 */
export function isDefaultLocale(locale: Locale): boolean {
  return locale === defaultLocale;
}

/**
 * 获取语言相关的格式化器
 */
export function getLocaleFormatters(locale: Locale) {
  return {
    // 数字格式化
    number: new Intl.NumberFormat(locale),

    // 货币格式化
    currency: new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: getCurrencyByLocale(locale),
    }),

    // 日期格式化
    date: new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }),

    // 时间格式化
    time: new Intl.DateTimeFormat(locale, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }),

    // 相对时间格式化
    relativeTime: new Intl.RelativeTimeFormat(locale, {
      numeric: 'auto',
    }),
  };
}

/**
 * 根据语言获取默认货币
 */
function getCurrencyByLocale(locale: Locale): string {
  const currencyMap: Record<Locale, string> = {
    'zh-CN': 'CNY',
    'en-US': 'USD',
    'ja-JP': 'JPY',
  };

  return currencyMap[locale] || 'USD';
}

/**
 * 格式化消息（支持变量插值）
 */
export function formatMessage(
  template: string,
  values: Record<string, number | string> = {},
): string {
  return template.replaceAll(/\{(\w+)\}/g, (match, key) => {
    return values[key]?.toString() || match;
  });
}

/**
 * 语言检测和重定向辅助函数
 */
export function detectAndRedirectLocale(
  requestPath: string,
  acceptLanguage?: string,
): { redirectPath?: string; shouldRedirect: boolean } {
  const currentLocale = getLocaleFromPath(requestPath);

  // 如果已经有有效的语言前缀，不需要重定向
  if (currentLocale !== defaultLocale) {
    return { shouldRedirect: false };
  }

  // 检测浏览器语言偏好
  const preferredLocale = detectBrowserLanguage(acceptLanguage);

  // 如果检测到的语言不是默认语言，重定向
  if (preferredLocale && preferredLocale !== defaultLocale) {
    const redirectPath = getLocalizedPath(requestPath, preferredLocale);
    return { shouldRedirect: true, redirectPath };
  }

  return { shouldRedirect: false };
}

/**
 * 从Accept-Language头检测语言
 */
function detectBrowserLanguage(acceptLanguage?: string): Locale | null {
  if (!acceptLanguage) {
    return null;
  }

  // 解析Accept-Language头
  const languages = acceptLanguage
    .split(',')
    .map((lang) => {
      const [locale, qValue] = lang.trim().split(';q=');
      return {
        locale: locale.trim(),
        quality: qValue ? Number.parseFloat(qValue) : 1,
      };
    })
    .sort((a, b) => b.quality - a.quality);

  // 查找匹配的语言
  for (const { locale } of languages) {
    // 精确匹配
    if (isValidLocale(locale)) {
      return locale;
    }

    // 语言前缀匹配
    const langPrefix = locale.split('-')[0];
    const matchedLocale = locales.find((l) => l.startsWith(langPrefix));
    if (matchedLocale) {
      return matchedLocale;
    }
  }

  return null;
}
