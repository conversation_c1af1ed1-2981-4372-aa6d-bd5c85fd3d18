import { NextRequest } from 'next/server';

/**
 * 检查是否为自定义域名
 *
 * @param request Next.js 请求对象
 * @returns 是否为自定义域名
 */
export function isCustomDomain(request: NextRequest): boolean {
  const hostname = request.nextUrl.hostname;

  // 开发环境的默认域名
  const devDomains = ['localhost', '127.0.0.1'];

  // 平台的官方域名（生产环境需要配置）
  const platformDomains = [
    'flexihub.com',
    'www.flexihub.com',
    process.env.PLATFORM_DOMAIN || 'flexihub.com',
  ];

  // 如果是开发环境
  if (process.env.NODE_ENV === 'development') {
    return !devDomains.includes(hostname);
  }

  // 生产环境：不是平台域名就是自定义域名
  return !platformDomains.includes(hostname);
}

/**
 * 获取自定义域名对应的网站域名标识
 *
 * @param request Next.js 请求对象
 * @returns 网站域名标识
 */
export function getCustomDomainSite(request: NextRequest): string {
  const hostname = request.nextUrl.hostname;

  // 在实际项目中，这里应该查询数据库
  // 根据自定义域名获取对应的网站ID

  // 模拟映射表
  const domainMapping: Record<string, string> = {
    'example.com': 'demo-site',
    'myblog.com': 'my-blog',
    // 可以添加更多映射
  };

  return domainMapping[hostname] || hostname.replaceAll('.', '-');
}

/**
 * 获取支持的语言列表
 */
export function getSupportedLocales(): string[] {
  return ['zh-CN', 'en-US', 'ja-JP'];
}

/**
 * 检测和规范化语言代码
 */
export function normalizeLocale(locale: string): string {
  const supportedLocales = getSupportedLocales();

  if (supportedLocales.includes(locale)) {
    return locale;
  }

  // 默认语言
  return 'zh-CN';
}
