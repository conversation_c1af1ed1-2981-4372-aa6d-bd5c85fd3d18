import Cookies from 'js-cookie';
import { jwtDecode } from 'jwt-decode';

export interface User {
  userId: string;
  username: string;
  tenantId: string;
  permissions: string[];
}

export interface JWTPayload extends User {
  exp: number;
  iat: number;
}

/**
 * 设置认证cookie
 */
export function setCookie(
  name: string,
  value: string,
  options?: Cookies.CookieAttributes,
) {
  Cookies.set(name, value, {
    expires: 7, // 7天过期
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    ...options,
  });
}

/**
 * 获取认证cookie
 */
export function getCookie(name: string): string | undefined {
  return Cookies.get(name);
}

/**
 * 删除认证cookie
 */
export function removeCookie(name: string) {
  Cookies.remove(name);
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): null | User {
  try {
    const token = getCookie('auth-token');
    if (!token) return null;

    const decoded = jwtDecode<JWTPayload>(token);

    // 检查令牌是否过期
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      removeCookie('auth-token');
      return null;
    }

    return {
      userId: decoded.userId,
      username: decoded.username,
      tenantId: decoded.tenantId,
      permissions: decoded.permissions,
    };
  } catch (error) {
    console.error('Failed to get current user:', error);
    removeCookie('auth-token');
    return null;
  }
}

/**
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  return getCurrentUser() !== null;
}

/**
 * 检查用户是否有特定权限
 */
export function hasPermission(permission: string): boolean {
  const user = getCurrentUser();
  if (!user) return false;

  return (
    user.permissions.includes(permission) || user.permissions.includes('admin')
  );
}

/**
 * 注销用户
 */
export function logout(): void {
  removeCookie('auth-token');
  // 重定向到首页
  window.location.href = '/zh-CN';
}

/**
 * 生成模拟JWT令牌（仅用于开发测试）
 */
export function generateMockToken(user: Partial<User>): string {
  const payload: JWTPayload = {
    userId: user.userId || '1',
    username: user.username || 'admin',
    tenantId: user.tenantId || 'tenant-1',
    permissions: user.permissions || ['admin', 'website:edit'],
    exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7天后过期
    iat: Math.floor(Date.now() / 1000),
  };

  // 简单的base64编码（实际项目中应该使用真实的JWT签名）
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payloadStr = btoa(JSON.stringify(payload));
  const signature = btoa('mock-signature');

  return `${header}.${payloadStr}.${signature}`;
}
