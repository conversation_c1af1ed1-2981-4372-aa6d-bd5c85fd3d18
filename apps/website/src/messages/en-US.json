{"navigation": {"home": "Home", "editor": "Editor", "dashboard": "Dashboard", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings", "back": "Back", "close": "Close", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "create": "Create", "preview": "Preview", "publish": "Publish", "draft": "Draft"}, "auth": {"loginTitle": "Login to FlexiHub", "loginSubtitle": "Access your website building tools", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loginWithAdmin": "Login with Admin Panel", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed, please check your credentials", "tokenExpired": "<PERSON>gin expired, please login again", "noPermission": "You don't have permission to access this feature", "redirecting": "Redirecting..."}, "editor": {"title": "Website Editor", "subtitle": "Drag and drop components to build your website", "toolbar": {"undo": "Undo", "redo": "Redo", "save": "Save", "preview": "Preview", "publish": "Publish", "settings": "Settings"}, "sidebar": {"components": "Components", "layers": "Layers", "properties": "Properties"}, "components": {"layout": {"title": "Layout Components", "container": "Container", "row": "Row", "column": "Column", "section": "Section"}, "content": {"title": "Content Components", "text": "Text", "heading": "Heading", "paragraph": "Paragraph", "list": "List", "button": "<PERSON><PERSON>", "link": "Link"}, "media": {"title": "Media Components", "image": "Image", "video": "Video", "gallery": "Gallery", "icon": "Icon"}, "interactive": {"title": "Interactive Components", "form": "Form", "input": "Input", "select": "Select", "checkbox": "Checkbox", "radio": "Radio", "textarea": "Textarea"}}, "properties": {"general": "General", "style": "Style", "layout": "Layout", "advanced": "Advanced", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "margin": "<PERSON><PERSON>", "padding": "Padding", "background": "Background", "border": "Border", "typography": "Typography", "color": "Color", "size": "Size", "weight": "Weight", "align": "Align", "spacing": "Spacing"}, "preview": {"title": "Website Preview", "returnToEditor": "Return to Editor", "device": {"desktop": "Desktop", "tablet": "Tablet", "mobile": "Mobile"}, "loading": "Loading preview...", "error": "Failed to load preview"}, "publish": {"title": "Publish Website", "subtitle": "Make your website live online", "domain": "Domain", "customDomain": "Custom Domain", "defaultDomain": "Default Domain", "publishButton": "Publish", "unpublishButton": "Unpublish", "publishSuccess": "Website published successfully", "publishError": "Failed to publish website", "status": {"draft": "Draft", "published": "Published", "publishing": "Publishing..."}}, "save": {"saving": "Saving...", "saved": "Saved", "error": "Save failed", "unsavedChanges": "You have unsaved changes"}}, "website": {"loading": "Loading website...", "error": "Failed to load website", "notFound": "Website not found", "maintenance": "Website under maintenance"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back to FlexiHub", "sites": {"title": "My Websites", "create": "Create New Website", "edit": "Edit", "preview": "Preview", "settings": "Settings", "delete": "Delete", "status": "Status", "lastModified": "Last Modified", "visitors": "Visitors", "empty": "You haven't created any websites yet", "createFirst": "Create your first website"}, "analytics": {"title": "Analytics", "visitors": "Visitors", "pageViews": "Page Views", "bounceRate": "Bounce Rate", "avgDuration": "Average Duration"}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "retry": "Retry", "refresh": "Refresh", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select": "Select", "selectAll": "Select All", "clear": "Clear", "reset": "Reset", "apply": "Apply", "submit": "Submit", "next": "Next", "previous": "Previous", "finish": "Finish", "skip": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "ok": "OK", "cancel": "Cancel", "continue": "Continue", "stop": "Stop", "start": "Start", "pause": "Pause", "resume": "Resume", "enable": "Enable", "disable": "Disable", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "maximize": "Maximize", "minimize": "Minimize", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters", "passwordConfirm": "Password confirmation does not match", "url": "Please enter a valid URL", "number": "Please enter a valid number", "integer": "Please enter an integer", "positive": "Please enter a positive number", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "pattern": "Invalid format"}, "errors": {"network": "Network connection error", "server": "Server error", "notFound": "Page not found", "forbidden": "Access denied", "unauthorized": "Unauthorized access", "timeout": "Request timeout", "unknown": "Unknown error", "tryAgain": "Please try again later"}, "language": {"switch": "Switch Language", "current": "Current Language", "zh-CN": "简体中文", "en-US": "English (US)", "ja-JP": "日本語"}, "api": {"title": "API Documentation", "subtitle": "FlexiHub API Documentation", "endpoints": "Endpoints", "authentication": "Authentication", "parameters": "Parameters", "response": "Response", "examples": "Examples", "errorCodes": "Error Codes"}}