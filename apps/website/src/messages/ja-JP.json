{"navigation": {"home": "ホーム", "editor": "エディター", "dashboard": "ダッシュボード", "login": "ログイン", "logout": "ログアウト", "profile": "プロフィール", "settings": "設定", "back": "戻る", "close": "閉じる", "save": "保存", "cancel": "キャンセル", "confirm": "確認", "delete": "削除", "edit": "編集", "create": "作成", "preview": "プレビュー", "publish": "公開", "draft": "下書き"}, "auth": {"loginTitle": "FlexiHub にログイン", "loginSubtitle": "ウェブサイト構築ツールにアクセス", "email": "メールアドレス", "password": "パスワード", "rememberMe": "ログイン状態を保持", "forgotPassword": "パスワードを忘れた方", "loginButton": "ログイン", "loginWithAdmin": "管理パネルでログイン", "loginSuccess": "ログインに成功しました", "loginError": "ログインに失敗しました。認証情報を確認してください", "tokenExpired": "ログインの有効期限が切れました。再度ログインしてください", "noPermission": "この機能にアクセスする権限がありません", "redirecting": "リダイレクト中..."}, "editor": {"title": "ウェブサイトエディター", "subtitle": "コンポーネントをドラッグ＆ドロップしてウェブサイトを構築", "toolbar": {"undo": "元に戻す", "redo": "やり直し", "save": "保存", "preview": "プレビュー", "publish": "公開", "settings": "設定"}, "sidebar": {"components": "コンポーネント", "layers": "レイヤー", "properties": "プロパティ"}, "components": {"layout": {"title": "レイアウトコンポーネント", "container": "コンテナ", "row": "行", "column": "列", "section": "セクション"}, "content": {"title": "コンテンツコンポーネント", "text": "テキスト", "heading": "見出し", "paragraph": "段落", "list": "リスト", "button": "ボタン", "link": "リンク"}, "media": {"title": "メディアコンポーネント", "image": "画像", "video": "動画", "gallery": "ギャラリー", "icon": "アイコン"}, "interactive": {"title": "インタラクティブコンポーネント", "form": "フォーム", "input": "入力フィールド", "select": "選択フィールド", "checkbox": "チェックボックス", "radio": "ラジオボタン", "textarea": "テキストエリア"}}, "properties": {"general": "一般", "style": "スタイル", "layout": "レイアウト", "advanced": "詳細設定", "width": "幅", "height": "高さ", "margin": "マージン", "padding": "パディング", "background": "背景", "border": "ボーダー", "typography": "タイポグラフィ", "color": "色", "size": "サイズ", "weight": "太さ", "align": "配置", "spacing": "間隔"}, "preview": {"title": "ウェブサイトプレビュー", "returnToEditor": "エディターに戻る", "device": {"desktop": "デスクトップ", "tablet": "タブレット", "mobile": "モバイル"}, "loading": "プレビューを読み込み中...", "error": "プレビューの読み込みに失敗しました"}, "publish": {"title": "ウェブサイトを公開", "subtitle": "ウェブサイトをオンラインで公開", "domain": "ドメイン", "customDomain": "カスタムドメイン", "defaultDomain": "デフォルトドメイン", "publishButton": "公開", "unpublishButton": "公開停止", "publishSuccess": "ウェブサイトが正常に公開されました", "publishError": "ウェブサイトの公開に失敗しました", "status": {"draft": "下書き", "published": "公開済み", "publishing": "公開中..."}}, "save": {"saving": "保存中...", "saved": "保存済み", "error": "保存に失敗しました", "unsavedChanges": "未保存の変更があります"}}, "website": {"loading": "ウェブサイトを読み込み中...", "error": "ウェブサイトの読み込みに失敗しました", "notFound": "ウェブサイトが見つかりません", "maintenance": "ウェブサイトはメンテナンス中です"}, "dashboard": {"title": "ダッシュボード", "welcome": "FlexiHub へようこそ", "sites": {"title": "マイウェブサイト", "create": "新しいウェブサイトを作成", "edit": "編集", "preview": "プレビュー", "settings": "設定", "delete": "削除", "status": "ステータス", "lastModified": "最終更新", "visitors": "訪問者数", "empty": "まだウェブサイトを作成していません", "createFirst": "最初のウェブサイトを作成"}, "analytics": {"title": "アナリティクス", "visitors": "訪問者", "pageViews": "ページビュー", "bounceRate": "直帰率", "avgDuration": "平均滞在時間"}}, "common": {"loading": "読み込み中...", "error": "エラー", "success": "成功", "warning": "警告", "info": "情報", "retry": "再試行", "refresh": "更新", "search": "検索", "filter": "フィルター", "sort": "並び替え", "export": "エクスポート", "import": "インポート", "copy": "コピー", "paste": "貼り付け", "cut": "切り取り", "select": "選択", "selectAll": "すべて選択", "clear": "クリア", "reset": "リセット", "apply": "適用", "submit": "送信", "next": "次へ", "previous": "前へ", "finish": "完了", "skip": "スキップ", "yes": "はい", "no": "いいえ", "ok": "OK", "cancel": "キャンセル", "continue": "続行", "stop": "停止", "start": "開始", "pause": "一時停止", "resume": "再開", "enable": "有効", "disable": "無効", "show": "表示", "hide": "非表示", "expand": "展開", "collapse": "折りたたみ", "maximize": "最大化", "minimize": "最小化", "fullscreen": "フルスクリーン", "exitFullscreen": "フルスクリーン解除"}, "validation": {"required": "この項目は必須です", "email": "有効なメールアドレスを入力してください", "password": "パスワードは8文字以上で入力してください", "passwordConfirm": "パスワードの確認が一致しません", "url": "有効なURLを入力してください", "number": "有効な数値を入力してください", "integer": "整数を入力してください", "positive": "正の数を入力してください", "min": "最小値は {min} です", "max": "最大値は {max} です", "minLength": "最小文字数は {min} 文字です", "maxLength": "最大文字数は {max} 文字です", "pattern": "形式が正しくありません"}, "errors": {"network": "ネットワーク接続エラー", "server": "サーバーエラー", "notFound": "ページが見つかりません", "forbidden": "アクセスが拒否されました", "unauthorized": "認証されていないアクセス", "timeout": "リクエストがタイムアウトしました", "unknown": "不明なエラー", "tryAgain": "しばらくしてから再試行してください"}, "language": {"switch": "言語切り替え", "current": "現在の言語", "zh-CN": "简体中文", "en-US": "English (US)", "ja-JP": "日本語"}, "api": {"title": "API ドキュメント", "subtitle": "FlexiHub API ドキュメント", "endpoints": "エンドポイント", "authentication": "認証", "parameters": "パラメータ", "response": "レスポンス", "examples": "例", "errorCodes": "エラーコード"}}