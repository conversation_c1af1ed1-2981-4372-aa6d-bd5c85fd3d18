{"navigation": {"home": "首页", "editor": "编辑器", "dashboard": "仪表板", "login": "登录", "logout": "退出登录", "profile": "个人资料", "settings": "设置", "back": "返回", "close": "关闭", "save": "保存", "cancel": "取消", "confirm": "确认", "delete": "删除", "edit": "编辑", "create": "创建", "preview": "预览", "publish": "发布", "draft": "草稿"}, "auth": {"loginTitle": "登录到 FlexiHub", "loginSubtitle": "访问您的网站构建工具", "email": "邮箱地址", "password": "密码", "rememberMe": "记住我", "forgotPassword": "忘记密码？", "loginButton": "登录", "loginWithAdmin": "使用管理后台登录", "loginSuccess": "登录成功", "loginError": "登录失败，请检查您的凭据", "tokenExpired": "登录已过期，请重新登录", "noPermission": "您没有访问此功能的权限", "redirecting": "正在跳转..."}, "editor": {"title": "网站编辑器", "subtitle": "拖拽组件构建您的网站", "toolbar": {"undo": "撤销", "redo": "重做", "save": "保存", "preview": "预览", "publish": "发布", "settings": "设置"}, "sidebar": {"components": "组件", "layers": "图层", "properties": "属性"}, "components": {"layout": {"title": "布局组件", "container": "容器", "row": "行", "column": "列", "section": "区域"}, "content": {"title": "内容组件", "text": "文本", "heading": "标题", "paragraph": "段落", "list": "列表", "button": "按钮", "link": "链接"}, "media": {"title": "媒体组件", "image": "图片", "video": "视频", "gallery": "图片库", "icon": "图标"}, "interactive": {"title": "交互组件", "form": "表单", "input": "输入框", "select": "选择器", "checkbox": "复选框", "radio": "单选框", "textarea": "文本域"}}, "properties": {"general": "常规", "style": "样式", "layout": "布局", "advanced": "高级", "width": "宽度", "height": "高度", "margin": "外边距", "padding": "内边距", "background": "背景", "border": "边框", "typography": "字体", "color": "颜色", "size": "大小", "weight": "粗细", "align": "对齐", "spacing": "间距"}, "preview": {"title": "网站预览", "returnToEditor": "返回编辑器", "device": {"desktop": "桌面端", "tablet": "平板端", "mobile": "移动端"}, "loading": "正在加载预览...", "error": "预览加载失败"}, "publish": {"title": "发布网站", "subtitle": "将您的网站发布到线上", "domain": "域名", "customDomain": "自定义域名", "defaultDomain": "默认域名", "publishButton": "发布", "unpublishButton": "取消发布", "publishSuccess": "网站发布成功", "publishError": "网站发布失败", "status": {"draft": "草稿", "published": "已发布", "publishing": "发布中..."}}, "save": {"saving": "保存中...", "saved": "已保存", "error": "保存失败", "unsavedChanges": "您有未保存的更改"}}, "website": {"loading": "正在加载网站...", "error": "网站加载失败", "notFound": "网站不存在", "maintenance": "网站维护中"}, "dashboard": {"title": "仪表板", "welcome": "欢迎回到 FlexiHub", "sites": {"title": "我的网站", "create": "创建新网站", "edit": "编辑", "preview": "预览", "settings": "设置", "delete": "删除", "status": "状态", "lastModified": "最后修改", "visitors": "访问量", "empty": "您还没有创建任何网站", "createFirst": "创建您的第一个网站"}, "analytics": {"title": "数据分析", "visitors": "访问者", "pageViews": "页面浏览量", "bounceRate": "跳出率", "avgDuration": "平均停留时间"}}, "common": {"loading": "加载中...", "error": "出错了", "success": "成功", "warning": "警告", "info": "信息", "retry": "重试", "refresh": "刷新", "search": "搜索", "filter": "筛选", "sort": "排序", "export": "导出", "import": "导入", "copy": "复制", "paste": "粘贴", "cut": "剪切", "select": "选择", "selectAll": "全选", "clear": "清空", "reset": "重置", "apply": "应用", "submit": "提交", "next": "下一步", "previous": "上一步", "finish": "完成", "skip": "跳过", "yes": "是", "no": "否", "ok": "确定", "cancel": "取消", "continue": "继续", "stop": "停止", "start": "开始", "pause": "暂停", "resume": "恢复", "enable": "启用", "disable": "禁用", "show": "显示", "hide": "隐藏", "expand": "展开", "collapse": "收起", "maximize": "最大化", "minimize": "最小化", "fullscreen": "全屏", "exitFullscreen": "退出全屏"}, "validation": {"required": "此字段是必填的", "email": "请输入有效的邮箱地址", "password": "密码至少需要8个字符", "passwordConfirm": "密码确认不匹配", "url": "请输入有效的URL", "number": "请输入有效的数字", "integer": "请输入整数", "positive": "请输入正数", "min": "最小值为 {min}", "max": "最大值为 {max}", "minLength": "最少需要 {min} 个字符", "maxLength": "最多只能 {max} 个字符", "pattern": "格式不正确"}, "errors": {"network": "网络连接错误", "server": "服务器错误", "notFound": "页面不存在", "forbidden": "访问被拒绝", "unauthorized": "未授权访问", "timeout": "请求超时", "unknown": "未知错误", "tryAgain": "请稍后重试"}, "language": {"switch": "切换语言", "current": "当前语言", "zh-CN": "简体中文", "en-US": "English (US)", "ja-JP": "日本語"}, "api": {"title": "API 文档", "subtitle": "FlexiHub API 接口文档", "endpoints": "接口列表", "authentication": "身份验证", "parameters": "参数", "response": "响应", "examples": "示例", "errorCodes": "错误码"}}