{"compilerOptions": {"incremental": true, "target": "es5", "jsx": "preserve", "lib": ["dom", "dom.iterable", "es6"], "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/styles/*": ["./src/styles/*"]}, "resolveJsonModule": true, "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "skipLibCheck": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}