{"name": "@flexihub/website", "version": "0.1.0", "description": "FlexiHub 统一网站应用 - 编辑器+用户网站渲染", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@craftjs/core": "^0.2.12", "@craftjs/layers": "^0.2.7", "@craftjs/utils": "^0.2.5", "@flexihub/shared": "workspace:*", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "axios": "^1.7.7", "class-variance-authority": "catalog:", "clsx": "catalog:", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.460.0", "next": "15.3.3", "next-intl": "^3.21.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "catalog:"}, "devDependencies": {"@eslint/eslintrc": "^3", "@stagewise/toolbar-next": "^0.1.2", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}