{"name": "editor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@flexihub/shared": "workspace:*", "@flexihub/website-components": "workspace:*", "@craftjs/core": "^0.2.7", "@craftjs/layers": "^0.2.7", "react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "lucide-react": "^0.460.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.5", "jwt-decode": "^4.0.0", "axios": "^1.7.7"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}