'use client';

import { useEditor } from '@craftjs/core';
import { Download, Eye, Redo, Save, Settings, Undo } from 'lucide-react';

export function TopBar() {
  const { actions, query, enabled } = useEditor((state) => ({
    enabled: state.options.enabled,
  }));

  return (
    <div className="flex h-14 items-center justify-between border-b border-gray-200 bg-white px-6 shadow-sm">
      {/* 左侧 - Logo 和项目信息 */}
      <div className="flex items-center space-x-4">
        <div className="text-xl font-bold text-gray-900">FlexiHub Editor</div>
        <div className="text-sm text-gray-500">未命名页面</div>
      </div>

      {/* 中间 - 编辑工具 */}
      <div className="flex items-center space-x-2">
        <button
          className="rounded-md p-2 hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
          disabled={!query.history.canUndo()}
          onClick={() => actions.history.undo()}
          title="撤销"
        >
          <Undo className="h-4 w-4" />
        </button>

        <button
          className="rounded-md p-2 hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
          disabled={!query.history.canRedo()}
          onClick={() => actions.history.redo()}
          title="重做"
        >
          <Redo className="h-4 w-4" />
        </button>

        <div className="mx-2 h-6 w-px bg-gray-300" />

        <button
          className={`rounded-md px-3 py-1 text-sm font-medium transition-colors ${
            enabled
              ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          }`}
          onClick={() =>
            actions.setOptions((options) => (options.enabled = !enabled))
          }
        >
          {enabled ? '编辑模式' : '预览模式'}
        </button>
      </div>

      {/* 右侧 - 操作按钮 */}
      <div className="flex items-center space-x-2">
        <button className="rounded-md p-2 hover:bg-gray-100" title="预览">
          <Eye className="h-4 w-4" />
        </button>

        <button className="rounded-md p-2 hover:bg-gray-100" title="设置">
          <Settings className="h-4 w-4" />
        </button>

        <button className="rounded-md p-2 hover:bg-gray-100" title="导出">
          <Download className="h-4 w-4" />
        </button>

        <div className="mx-2 h-6 w-px bg-gray-300" />

        <button
          className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          onClick={() => {
            // 保存页面数据
            const json = query.serialize();
            void json; // 避免未使用变量警告
            // TODO: 调用API保存页面
          }}
        >
          <Save className="mr-2 inline h-4 w-4" />
          保存
        </button>
      </div>
    </div>
  );
}
