'use client';

import { useEditor } from '@craftjs/core';
import { Container, Text } from '@flexihub/website-components';
import { Box, Grid, Layout, Type } from 'lucide-react';

export function ToolPanel() {
  const { connectors } = useEditor();

  const componentItems = [
    {
      name: '容器',
      icon: Box,
      component: Container,
      props: { maxWidth: 'lg', padding: 'md', background: 'transparent' },
    },
    {
      name: '文本',
      icon: Type,
      component: Text,
      props: { content: '新建文本', tag: 'p', fontSize: 'base' },
    },
    // 后续可以添加更多组件
  ];

  return (
    <div className="flex h-full flex-col">
      {/* 面板标题 */}
      <div className="border-b border-gray-200 p-4">
        <h3 className="text-sm font-semibold text-gray-900">组件库</h3>
      </div>

      {/* 组件分类 */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-4 p-4">
          {/* 基础组件 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              基础组件
            </h4>
            <div className="grid grid-cols-2 gap-2">
              {componentItems.map((item) => (
                <div
                  className="flex cursor-grab flex-col items-center space-y-2 rounded-lg border border-gray-200 p-3 text-center transition-colors hover:border-blue-300 hover:bg-blue-50"
                  key={item.name}
                  ref={(ref) => {
                    if (ref) {
                      connectors.create(ref, item.component, item.props);
                    }
                  }}
                >
                  <item.icon className="h-6 w-6 text-gray-600" />
                  <span className="text-xs font-medium text-gray-700">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 预设布局 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              预设布局
            </h4>
            <div className="space-y-2">
              <div className="cursor-pointer rounded-lg border border-gray-200 p-3 transition-colors hover:border-blue-300 hover:bg-blue-50">
                <div className="flex items-center space-x-2">
                  <Layout className="h-4 w-4 text-gray-600" />
                  <span className="text-xs font-medium text-gray-700">
                    单列布局
                  </span>
                </div>
              </div>
              <div className="cursor-pointer rounded-lg border border-gray-200 p-3 transition-colors hover:border-blue-300 hover:bg-blue-50">
                <div className="flex items-center space-x-2">
                  <Grid className="h-4 w-4 text-gray-600" />
                  <span className="text-xs font-medium text-gray-700">
                    两列布局
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 页面块 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              页面块
            </h4>
            <div className="space-y-2">
              <div className="cursor-pointer rounded-lg border border-gray-200 p-4 transition-colors hover:border-blue-300 hover:bg-blue-50">
                <div className="mb-1 text-xs font-medium text-gray-700">
                  英雄区块
                </div>
                <div className="text-xs text-gray-500">标题 + 描述 + 按钮</div>
              </div>
              <div className="cursor-pointer rounded-lg border border-gray-200 p-4 transition-colors hover:border-blue-300 hover:bg-blue-50">
                <div className="mb-1 text-xs font-medium text-gray-700">
                  特性展示
                </div>
                <div className="text-xs text-gray-500">图标 + 标题 + 描述</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
