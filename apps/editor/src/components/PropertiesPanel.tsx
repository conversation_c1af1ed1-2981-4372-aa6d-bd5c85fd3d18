'use client';

import { useEditor } from '@craftjs/core';
import { Copy, Move, Settings, Trash2 } from 'lucide-react';

export function PropertiesPanel() {
  const { selected, actions } = useEditor((state) => {
    const selectedNodeId = state.events.selected;
    let selectedNode = null;

    if (selectedNodeId) {
      selectedNode = state.nodes[selectedNodeId];
    }

    return {
      selected: selectedNode,
    };
  });

  if (!selected) {
    return (
      <div className="flex h-full flex-col">
        <div className="border-b border-gray-200 p-4">
          <h3 className="text-sm font-semibold text-gray-900">属性面板</h3>
        </div>
        <div className="flex flex-1 items-center justify-center p-6">
          <div className="text-center text-gray-500">
            <Settings className="mx-auto mb-3 h-12 w-12 text-gray-400" />
            <p className="text-sm">请选择一个组件</p>
            <p className="mt-1 text-xs">点击页面中的组件来编辑其属性</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      {/* 面板标题 */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-900">属性面板</h3>
          <div className="flex items-center space-x-1">
            <button className="rounded p-1 hover:bg-gray-100" title="复制组件">
              <Copy className="h-4 w-4 text-gray-600" />
            </button>
            <button className="rounded p-1 hover:bg-gray-100" title="移动组件">
              <Move className="h-4 w-4 text-gray-600" />
            </button>
            <button
              className="rounded p-1 text-red-600 hover:bg-gray-100"
              onClick={() => actions.delete(selected.id)}
              title="删除组件"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* 组件信息 */}
      <div className="border-b border-gray-200 p-4">
        <div className="mb-1 text-sm font-medium text-gray-900">
          {selected.data.displayName || selected.data.type}
        </div>
        <div className="text-xs text-gray-500">组件ID: {selected.id}</div>
      </div>

      {/* 属性编辑区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {/* 基础属性 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              基础属性
            </h4>
            <div className="space-y-3">
              {/* 这里将根据组件类型显示不同的属性编辑器 */}
              <div>
                <label className="mb-1 block text-xs font-medium text-gray-700">
                  CSS类名
                </label>
                <input
                  className="w-full rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="输入CSS类名"
                  type="text"
                />
              </div>
            </div>
          </div>

          {/* 样式属性 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              样式设置
            </h4>
            <div className="space-y-3">
              <div>
                <label className="mb-1 block text-xs font-medium text-gray-700">
                  背景色
                </label>
                <div className="flex space-x-2">
                  <input
                    className="h-6 w-8 cursor-pointer rounded border border-gray-300"
                    type="color"
                  />
                  <input
                    className="flex-1 rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="#ffffff"
                    type="text"
                  />
                </div>
              </div>

              <div>
                <label className="mb-1 block text-xs font-medium text-gray-700">
                  内边距
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    className="rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="上"
                    type="number"
                  />
                  <input
                    className="rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="右"
                    type="number"
                  />
                  <input
                    className="rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="下"
                    type="number"
                  />
                  <input
                    className="rounded border border-gray-300 px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="左"
                    type="number"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 响应式设置 */}
          <div>
            <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
              响应式设置
            </h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input className="rounded" id="show-mobile" type="checkbox" />
                <label className="text-xs text-gray-700" htmlFor="show-mobile">
                  在移动端显示
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input className="rounded" id="show-tablet" type="checkbox" />
                <label className="text-xs text-gray-700" htmlFor="show-tablet">
                  在平板端显示
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input className="rounded" id="show-desktop" type="checkbox" />
                <label className="text-xs text-gray-700" htmlFor="show-desktop">
                  在桌面端显示
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
