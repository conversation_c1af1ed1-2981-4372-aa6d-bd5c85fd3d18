'use client';

import { PropertiesPanel } from '@/components/PropertiesPanel';
import { ToolPanel } from '@/components/ToolPanel';
import { TopBar } from '@/components/TopBar';
import { Editor, Element, Frame } from '@craftjs/core';
import { Container, Text } from '@flexihub/website-components';

export default function EditorPage() {
  return (
    <div className="flex h-screen bg-gray-100">
      <Editor
        indicator={{
          success: '#22c55e',
          error: '#ef4444',
        }}
        resolver={{
          Container,
          Text,
        }}
      >
        {/* 顶部工具栏 */}
        <TopBar />

        <div className="flex flex-1">
          {/* 左侧组件面板 */}
          <div className="w-64 border-r border-gray-200 bg-white shadow-sm">
            <ToolPanel />
          </div>

          {/* 中间编辑区域 */}
          <div className="flex flex-1 flex-col">
            <div className="flex-1 overflow-auto p-6">
              <div className="mx-auto max-w-4xl">
                <Frame>
                  <Element
                    background="white"
                    canvas
                    is={Container}
                    maxWidth="lg"
                    padding="md"
                  >
                    <Element
                      content="欢迎使用 FlexiHub 网站编辑器"
                      fontSize="3xl"
                      fontWeight="bold"
                      is={Text}
                      tag="h1"
                      textAlign="center"
                    />
                    <Element
                      color="muted"
                      content="拖拽左侧组件开始构建您的网站"
                      fontSize="lg"
                      is={Text}
                      tag="p"
                      textAlign="center"
                    />
                  </Element>
                </Frame>
              </div>
            </div>
          </div>

          {/* 右侧属性面板 */}
          <div className="w-80 border-l border-gray-200 bg-white shadow-sm">
            <PropertiesPanel />
          </div>
        </div>
      </Editor>
    </div>
  );
}
