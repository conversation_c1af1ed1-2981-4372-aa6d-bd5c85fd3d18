# FlexiHub 开发指南

## 环境配置

### 开发环境要求

- **Node.js**: >=18.0.0
- **pnpm**: >=8.0.0
- **Vue CLI**: 最新版本
- **IDE**: 推荐 VS Code + Vetur/Volar 插件

### 项目初始化

```bash
# 克隆项目
git clone [project-repo-url]

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## 开发规范

### 代码风格

1. **严格遵循 TypeScript 规范**

   - 所有变量、函数、组件必须明确类型定义
   - 禁止使用 `any` 类型，推荐使用 `unknown` 或具体类型
   - 接口定义放在独立的 namespace 中

2. **组件开发规范**

   ```typescript
   // ✅ 推荐写法
   interface Props {
     data: FeatureCodeApi.FeatureCode[];
     loading?: boolean;
   }

   const props = defineProps<Props>();
   const emits = defineEmits<{
     success: [];
     error: [message: string];
   }>();
   ```

3. **API 调用规范**
   ```typescript
   // ✅ 统一错误处理
   try {
     const data = await getFeatureCodeList();
     // 处理成功逻辑
   } catch (error) {
     console.error('获取功能代码列表失败:', error);
     message.error('操作失败，请重试');
   }
   ```

### 文件组织

```
src/views/system/[module]/
├── index.vue           # 入口页面
├── list.vue           # 列表页面
├── data.ts            # 表格列定义、表单配置
├── modules/           # 子组件
│   ├── form.vue       # 表单组件
│   ├── detail.vue     # 详情组件
│   └── menu-form.vue  # 菜单关联组件
```

### 命名约定

- **文件名**: kebab-case (`feature-code.ts`)
- **组件名**: PascalCase (`FeatureCodeList`)
- **变量名**: camelCase (`featureCodeList`)
- **常量名**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **类型定义**: PascalCase (`FeatureCodeApi.FeatureCode`)

## 功能开发流程

### 1. 需求分析

- 明确功能边界和用户故事
- 识别涉及的API接口
- 确定UI/UX设计方案
- 评估技术难点和风险

### 2. 技术设计

- 定义TypeScript接口
- 设计组件层次结构
- 规划状态管理方案
- 确定路由配置

### 3. 开发实现

#### API层开发

```typescript
// src/api/system/[module].ts
export namespace ModuleApi {
  export interface ModuleItem {
    id: string;
    name: string;
    // ... 其他属性
  }
}

export async function getModuleList() {
  return requestClient.get<ModuleApi.ModuleItem[]>('/api/module/list');
}
```

#### 组件开发

```vue
<script lang="ts" setup>
import type { ModuleApi } from '#/api/system/module';

// Props 定义
interface Props {
  // 明确定义所有属性
}

// 状态管理
const loading = ref(false);
const dataList = ref<ModuleApi.ModuleItem[]>([]);

// 方法定义
async function loadData() {
  loading.value = true;
  try {
    const data = await getModuleList();
    dataList.value = data;
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载失败');
  } finally {
    loading.value = false;
  }
}
</script>
```

### 4. 测试验证

- 功能测试：确保所有功能按预期工作
- 界面测试：验证响应式设计和用户体验
- 兼容性测试：不同浏览器和设备测试
- 性能测试：页面加载速度和操作响应速度

## 架构设计原则

### 1. 模块化设计

- **单一职责**: 每个模块只负责一个核心功能
- **低耦合**: 模块间依赖关系最小化
- **高内聚**: 模块内部功能紧密相关

### 2. 组件复用

- **通用组件**: 提取可复用的UI组件
- **业务组件**: 封装特定业务逻辑
- **组合式API**: 使用 Composition API 提取逻辑

### 3. 状态管理

- **本地状态**: 使用 `ref` / `reactive`
- **全局状态**: 使用 Pinia store
- **缓存策略**: 合理使用前端缓存

### 4. 性能优化

- **懒加载**: 路由和组件按需加载
- **虚拟列表**: 大数据量列表优化
- **防抖节流**: 用户输入操作优化
- **缓存机制**: API结果缓存

## 质量保证

### 代码审查检查项

1. **类型安全**

   - [ ] 所有变量都有明确类型定义
   - [ ] 没有使用 `any` 类型
   - [ ] API 响应数据有类型定义

2. **错误处理**

   - [ ] API 调用有 try-catch 包装
   - [ ] 用户友好的错误提示
   - [ ] 加载状态正确管理

3. **用户体验**

   - [ ] 操作有确认对话框
   - [ ] 加载状态有视觉反馈
   - [ ] 成功/失败操作有消息提示

4. **代码质量**
   - [ ] 函数命名清晰表达意图
   - [ ] 注释覆盖复杂逻辑
   - [ ] 代码结构清晰易读

### 性能评估标准

- **首屏加载**: < 2秒
- **页面切换**: < 500ms
- **操作响应**: < 300ms
- **内存使用**: 合理范围内

## 调试技巧

### 1. Vue DevTools

- 使用 Vue DevTools 调试组件状态
- 检查 Props 和 Events 传递
- 监控性能指标

### 2. 网络调试

- 使用浏览器 Network 面板监控 API 调用
- 检查请求参数和响应数据
- 验证错误处理逻辑

### 3. 控制台调试

```typescript
// 开发环境调试日志
if (import.meta.env.DEV) {
  console.log('调试信息:', data);
}
```

### 4. 错误边界

```vue
<script setup>
// 组件错误处理
onErrorCaptured((error, instance, info) => {
  console.error('组件错误:', error, info);
  return false;
});
</script>
```

## 部署指南

### 构建配置

```bash
# 开发环境构建
pnpm build:dev

# 生产环境构建
pnpm build:prod

# 预览构建结果
pnpm preview
```

### 环境变量

```env
# .env.development
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=FlexiHub Dev

# .env.production
VITE_API_BASE_URL=https://api.flexihub.com
VITE_APP_TITLE=FlexiHub
```

### 部署检查项

- [ ] 所有 API 接口正常访问
- [ ] 静态资源正确加载
- [ ] 路由跳转正常
- [ ] 权限控制生效

## 常见问题

### 1. TypeScript 类型错误

**问题**: 类型定义不匹配 **解决**: 检查 API 接口定义，确保前后端类型一致

### 2. 表格数据不更新

**问题**: 数据变更后表格未刷新 **解决**: 调用 `gridApi.query()` 或 `gridApi.setGridOptions({ data })`

### 3. 模态框数据传递

**问题**: 模态框无法获取传递的数据 **解决**: 使用 `modalApi.setData()` 设置，`modalApi.getData()` 获取

### 4. 路由权限问题

**问题**: 用户访问无权限页面 **解决**: 检查路由守卫配置和用户权限验证

## 贡献指南

### 提交规范

```
feat: 新功能
fix: Bug修复
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建配置等
```

### Pull Request 流程

1. 从 main 分支创建特性分支
2. 完成开发并进行自测
3. 更新相关文档
4. 提交 PR 并请求代码审查
5. 通过审查后合并到 main 分支

---

**维护者**: FlexiHub 开发团队  
**最后更新**: 2024年12月
