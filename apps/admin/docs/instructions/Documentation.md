# FlexiHub Vue Frontend - 项目文档

## 项目概述

FlexiHub是一个基于Vue 3的多租户SaaS建站系统前端，采用Vben Admin架构。系统通过功能代码、功能模板和租户配置实现灵活的多租户权限控制。

## 技术架构

### 核心技术栈

- **Vue 3**: 前端框架
- **TypeScript**: 类型安全
- **Vben Admin**: 企业级管理后台解决方案
- **Ant Design Vue**: UI组件库
- **VxeTable**: 表格组件
- **Vue Router**: 路由管理
- **Pinia**: 状态管理

### 项目结构

```
playground/
├── src/
│   ├── api/                    # API接口层
│   │   ├── system/            # 系统管理API
│   │   │   ├── feature-code.ts    # 功能代码API
│   │   │   ├── feature-menu.ts    # 功能菜单API
│   │   │   ├── feature-template.ts # 功能模板API
│   │   │   ├── menu.ts            # 系统菜单API
│   │   │   └── tenant.ts          # 租户管理API
│   ├── views/                 # 页面组件
│   │   └── system/           # 系统管理页面
│   │       ├── feature-code/     # 功能代码管理
│   │       ├── feature-template/ # 功能模板管理
│   │       ├── tenant/           # 租户管理
│   │       └── menu/             # 菜单管理
│   ├── router/               # 路由配置
│   ├── locales/             # 国际化
│   └── components/          # 通用组件
└── docs/                    # 项目文档
    ├── api-docs.md         # 后端API文档
    ├── instructions/       # 项目说明文档
    └── project_management/ # 项目管理文档
```

## 核心功能模块

### 1. 权限控制体系

#### 功能代码 (Feature Code)

- **作用**: 系统最小功能单元，每个功能代码代表一个具体的业务功能
- **属性**: 代码、名称、描述、模块、状态、排序
- **示例**: `ai.ppt` (AI PPT生成功能)

#### 功能模板 (Feature Template)

- **作用**: 功能代码的组合方案，用于快速配置租户权限
- **属性**: 模板代码、名称、包含的功能代码列表、状态
- **用途**: 为不同类型租户提供预设的功能组合

#### 菜单权限

- **功能代码关联菜单**: 每个功能代码可以关联多个菜单项
- **租户菜单同步**: 根据租户拥有的功能代码自动生成可用菜单
- **动态权限控制**: 前端根据用户权限动态显示菜单和功能

### 2. 租户管理

- **多租户隔离**: 数据和权限完全隔离
- **功能配置**: 为租户启用/禁用特定功能
- **菜单同步**: 根据租户功能自动同步菜单权限
- **配置管理**: 租户级别的个性化配置

### 3. 系统菜单管理

- **树形结构**: 支持多级菜单嵌套
- **动态路由**: 基于权限动态生成路由
- **菜单元数据**: 图标、标题、权限等配置
- **权限标识**: 与功能代码关联的权限控制

## 已实现功能

### ✅ 完成的模块

1. **功能代码管理**

   - 功能代码列表查询（支持前端筛选）
   - 功能代码CRUD操作
   - 功能代码与菜单关联管理
   - 响应式表格设计

2. **功能模板管理**

   - 功能模板列表展示
   - 功能模板CRUD操作
   - 功能模板关联菜单查看
   - 模板应用到租户

3. **租户管理**

   - 租户列表管理
   - 租户状态控制
   - 租户功能配置跳转
   - 租户菜单查看和同步

4. **菜单权限系统**

   - 功能代码菜单关联
   - 功能模板菜单查看
   - 租户菜单同步
   - 权限动态控制

5. **用户界面**
   - 现代化UI设计
   - 响应式布局
   - 表格操作优化
   - 模态框交互
   - 国际化支持

### 🔄 技术特性

1. **数据管理**

   - 前端缓存优化
   - 实时数据刷新
   - 错误处理机制
   - 加载状态管理

2. **用户体验**

   - 操作确认对话框
   - 加载动画提示
   - 成功/失败消息反馈
   - 表单验证

3. **代码质量**
   - TypeScript类型安全
   - 组件复用设计
   - API接口统一管理
   - 错误边界处理

## API接口映射

### 已对接的后端API

| 功能模块 | 前端实现 | 后端API | 状态 |
| --- | --- | --- | --- |
| 功能代码列表 | ✅ | `/api/system/feature-codes/list` | 已对接 |
| 功能代码CRUD | ✅ | `/api/system/feature-codes/*` | 已对接 |
| 功能代码菜单 | ✅ | `/api/system/feature-codes/{code}/menus` | 已对接 |
| 功能模板列表 | ✅ | `/api/system/feature-templates/list` | 已对接 |
| 功能模板CRUD | ✅ | `/api/system/feature-templates/*` | 已对接 |
| 功能模板菜单 | ✅ | `/api/system/feature-templates/{code}/menus` | 已对接 |
| 租户管理 | ✅ | `/api/system/tenants/*` | 已对接 |
| 租户菜单 | ✅ | `/api/system/tenant-features/{tenantId}/menus` | 已对接 |
| 菜单同步 | ✅ | `/api/system/tenant-features/{tenantId}/sync-menus` | 已对接 |
| 系统菜单 | ✅ | `/api/system/menu/*` | 已对接 |

## 代码规范

### 命名规范

- **文件命名**: kebab-case (feature-code.ts)
- **组件命名**: PascalCase (FeatureCodeList)
- **变量命名**: camelCase (featureCodeList)
- **常量命名**: UPPER_SNAKE_CASE (API_BASE_URL)

### API设计原则

- 统一的请求/响应格式
- 错误处理标准化
- TypeScript类型定义
- 接口参数验证

### 组件设计原则

- 单一职责原则
- 可复用性设计
- Props类型定义
- 事件统一管理

## 国际化支持

### 已支持语言

- 中文 (zh-CN)
- 英文 (en-US)

### 翻译覆盖

- 界面标签和按钮
- 操作提示消息
- 表格列标题
- 表单验证信息

## 文档维护规范

### 更新频率

- 新功能开发完成后立即更新文档
- 重大重构后更新架构文档
- Bug修复后更新相关说明

### 文档类型

1. **技术文档**: API接口、组件使用说明
2. **产品文档**: 功能说明、用户指南
3. **项目文档**: 进度跟踪、任务规划
4. **开发文档**: 环境配置、部署指南

### 质量标准

- 内容准确性: 与实际代码保持一致
- 结构清晰性: 逻辑层次分明
- 完整性: 覆盖所有重要功能点
- 可读性: 语言简洁明了

---

**最后更新**: 2024年12月 **维护者**: FlexiHub开发团队
