import { defineConfig } from '@vben/vite-config';

import { VueMcp } from 'vite-plugin-vue-mcp';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      plugins: [VueMcp()],
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            // 不改变路径，直接将/api请求转发到后端
            // 因为后端已经有/api前缀了
            rewrite: (path) => path,
            // 指向我们的NestJS后端服务
            target: 'http://127.0.0.1:4000',
            ws: true,
          },
        },
      },
    },
  };
});
