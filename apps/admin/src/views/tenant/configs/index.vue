<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { TenantConfigApi } from '#/api/system/tenant-config';

import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Button, Card, message, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getConfigCategories,
  getTenantConfig,
  testTenantConfig,
  updateTenantConfig,
} from '#/api/system/tenant-config';
import { $t } from '#/locales';

import {
  useEmailConfigFormSchema,
  useSmsConfigFormSchema,
} from '../../system/tenant-config/data';

const categories = ref<TenantConfigApi.ConfigCategory[]>([]);
const activeCategory = ref<string>('');
const isLoading = ref(false);
const isTesting = ref(false);
const testResult = ref<null | { message: string; success: boolean }>(null);

// 根据类别获取表单配置
const formSchema = computed<VbenFormSchema[]>(() => {
  switch (activeCategory.value) {
    case 'email': {
      return useEmailConfigFormSchema();
    }
    case 'sms': {
      return useSmsConfigFormSchema();
    }
    default: {
      return [];
    }
  }
});

const [Form, formApi] = useVbenForm({
  schema: formSchema,
  showDefaultActions: false,
});

// 加载配置类别
async function loadCategories() {
  isLoading.value = true;
  try {
    categories.value = await getConfigCategories();
    if (categories.value.length > 0) {
      activeCategory.value = categories.value[0].code;
      await loadConfig(activeCategory.value);
    }
  } catch (error) {
    console.error('获取配置类别失败:', error);
    message.error('获取配置类别失败');
  } finally {
    isLoading.value = false;
  }
}

// 加载配置
async function loadConfig(category: string) {
  isLoading.value = true;
  try {
    const config = await getTenantConfig(category);
    formApi.setValues(config);
  } catch (error) {
    console.error('获取配置失败:', error);
    message.error('获取配置失败');
  } finally {
    isLoading.value = false;
  }
}

// 保存配置
async function saveConfig() {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();

  isLoading.value = true;
  try {
    await updateTenantConfig(activeCategory.value, values);
    message.success($t('ui.actionMessage.operationSuccess'));
  } catch (error) {
    console.error('保存配置失败:', error);
    message.error('保存配置失败');
  } finally {
    isLoading.value = false;
  }
}

// 测试配置
async function handleTestConfig() {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();

  isTesting.value = true;
  testResult.value = null;

  try {
    const result = await testTenantConfig(activeCategory.value, values);

    testResult.value = {
      success: result.success || false,
      message: result.message || '',
    };

    if (result.success) {
      message.success('测试成功');
    } else {
      message.error(`测试失败: ${result.message}`);
    }
  } catch (error) {
    console.error('测试配置失败:', error);
    testResult.value = {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
    };
    message.error('测试配置失败');
  } finally {
    isTesting.value = false;
  }
}

// 切换类别
async function handleCategoryChange(category: string) {
  activeCategory.value = category;
  testResult.value = null;
  await loadConfig(category);
}

onMounted(() => {
  loadCategories();
});
</script>

<template>
  <Page auto-content-height>
    <div class="p-4">
      <h1 class="mb-6 text-2xl font-bold">
        {{ $t('system.tenantConfig.configCenter') }}
      </h1>

      <Card :loading="isLoading">
        <Tabs
          v-model:active-key="activeCategory"
          @change="handleCategoryChange"
        >
          <Tabs.TabPane
            v-for="category in categories"
            :key="category.code"
            :tab="category.name"
          >
            <div class="mb-4 text-gray-500">{{ category.description }}</div>

            <Form />

            <div class="mt-6 flex gap-4">
              <Button type="primary" :loading="isLoading" @click="saveConfig">
                {{ $t('system.tenantConfig.saveConfig') }}
              </Button>

              <Button :loading="isTesting" @click="handleTestConfig">
                {{ $t('system.tenantConfig.testConfig') }}
              </Button>
            </div>

            <div
              v-if="testResult"
              class="mt-4 rounded border p-3"
              :class="
                testResult.success ? 'border-green-500' : 'border-red-500'
              "
            >
              <div
                class="font-medium"
                :class="testResult.success ? 'text-green-500' : 'text-red-500'"
              >
                {{ testResult.success ? '测试成功' : '测试失败' }}
              </div>
              <div class="mt-1">{{ testResult.message }}</div>
            </div>
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </div>
  </Page>
</template>
