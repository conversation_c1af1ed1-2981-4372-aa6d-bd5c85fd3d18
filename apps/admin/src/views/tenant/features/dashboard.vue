<script lang="ts" setup>
import type { TenantFeatureApi } from '#/api/system/tenant-feature';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Card, Col, message, Progress, Row } from 'ant-design-vue';

import { getMyFeatures } from '#/api/system/tenant-feature';
import { $t } from '#/locales';

const features = ref<TenantFeatureApi.MyFeature[]>([]);
const isLoading = ref(false);

// 加载当前租户功能
async function loadFeatures() {
  isLoading.value = true;
  try {
    features.value = await getMyFeatures();
  } catch (error) {
    console.error('获取功能列表失败:', error);
    message.error('获取功能列表失败');
  } finally {
    isLoading.value = false;
  }
}

// 计算配额使用百分比
function calculatePercentage(feature: TenantFeatureApi.MyFeature) {
  if (!feature.enabled) return 0;
  if (feature.quota === null) return 100;
  if (feature.quota === 0) return 100;
  return Math.min(100, Math.round((feature.usedQuota / feature.quota) * 100));
}

// 获取进度条状态
function getProgressStatus(feature: TenantFeatureApi.MyFeature) {
  if (!feature.enabled) return 'exception';
  if (feature.quota === null) return 'success';

  const percentage = calculatePercentage(feature);
  if (percentage >= 90) return 'exception';
  if (percentage >= 70) return 'warning';
  return 'success';
}

// 格式化过期时间
function formatExpiresAt(expiresAt: null | string) {
  if (!expiresAt) return $t('system.tenantFeature.noExpiration');
  return new Date(expiresAt).toLocaleString();
}

// 格式化配额
function formatQuota(feature: TenantFeatureApi.MyFeature) {
  if (feature.quota === null) return $t('system.tenantFeature.unlimited');
  return `${feature.usedQuota} / ${feature.quota}`;
}

onMounted(() => {
  loadFeatures();
});
</script>

<template>
  <Page auto-content-height>
    <div class="p-4">
      <h1 class="mb-6 text-2xl font-bold">
        {{ $t('system.tenantFeature.dashboard') }}
      </h1>

      <div v-if="isLoading" class="py-8 text-center">
        <div class="text-lg">加载中...</div>
      </div>

      <div v-else-if="features.length === 0" class="py-8 text-center">
        <div class="text-lg">暂无功能</div>
      </div>

      <Row v-else :gutter="[16, 16]">
        <Col v-for="feature in features" :key="feature.code" :span="8">
          <Card :title="feature.code" :bordered="true">
            <template #extra>
              <div
                :class="feature.enabled ? 'bg-green-500' : 'bg-red-500'"
                class="rounded px-2 py-1 text-xs text-white"
              >
                {{
                  feature.enabled ? $t('common.enabled') : $t('common.disabled')
                }}
              </div>
            </template>

            <div class="mb-4">
              <div class="mb-1 text-gray-500">
                {{ $t('system.tenantFeature.quota') }}
              </div>
              <Progress
                :percent="calculatePercentage(feature)"
                :status="getProgressStatus(feature)"
                :format="() => formatQuota(feature)"
              />
            </div>

            <div class="mb-4">
              <div class="mb-1 text-gray-500">
                {{ $t('system.tenantFeature.expiresAt') }}
              </div>
              <div>{{ formatExpiresAt(feature.expiresAt) }}</div>
            </div>

            <div v-if="Object.keys(feature.config).length > 0">
              <div class="mb-1 text-gray-500">
                {{ $t('system.tenantFeature.config') }}
              </div>
              <div class="rounded bg-gray-50 p-2">
                <pre class="text-xs">{{
                  JSON.stringify(feature.config, null, 2)
                }}</pre>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  </Page>
</template>
