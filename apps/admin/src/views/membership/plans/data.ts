import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MembershipPlanApi } from '#/api/membership/plans';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '计划名称',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入计划代码，如：BASIC, PREMIUM',
      },
      fieldName: 'code',
      label: '计划代码',
      rules: [
        'required',
        {
          pattern: /^[A-Z][A-Z0-9_]*$/,
          message: '代码必须以大写字母开头，只能包含大写字母、数字和下划线',
        },
      ],
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '月付', value: 'monthly' },
          { label: '季付', value: 'quarterly' },
          { label: '年付', value: 'yearly' },
          { label: '终身', value: 'lifetime' },
        ],
      },
      fieldName: 'billingCycle',
      label: '计费周期',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        style: { width: '100%' },
        addonAfter: '元',
      },
      fieldName: 'price',
      label: '价格',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        style: { width: '100%' },
        addonAfter: '元',
      },
      fieldName: 'originalPrice',
      label: '原价',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入功能特性（JSON格式）',
        rows: 4,
      },
      fieldName: 'features',
      label: '功能特性',
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: '描述',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        style: { width: '100%' },
      },
      fieldName: 'sortOrder',
      label: '排序顺序',
      rules: 'required',
      defaultValue: 1,
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: true },
          { label: $t('common.disabled'), value: false },
        ],
        optionType: 'button',
      },
      defaultValue: true,
      fieldName: 'isActive',
      label: '状态',
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '计划名称',
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: '计划代码',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '月付', value: 'monthly' },
          { label: '季付', value: 'quarterly' },
          { label: '年付', value: 'yearly' },
          { label: '终身', value: 'lifetime' },
        ],
      },
      fieldName: 'billingCycle',
      label: '计费周期',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: true },
          { label: $t('common.disabled'), value: false },
        ],
      },
      fieldName: 'isActive',
      label: '状态',
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: '创建时间',
    },
  ];
}

export function useColumns<T = MembershipPlanApi.Plan>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: '计划名称',
      width: 200,
    },
    {
      field: 'code',
      title: '计划代码',
      width: 150,
      className: 'font-mono text-xs',
    },
    {
      field: 'billingCycle',
      title: '计费周期',
      width: 120,
      cellRender: {
        name: 'CellTag',
        attrs: {
          type: (row: any) => {
            const typeMap: Record<string, string> = {
              monthly: 'primary',
              quarterly: 'warning',
              yearly: 'success',
              lifetime: 'error',
            };
            return typeMap[row.billingCycle] || 'default';
          },
          formatter: (row: any) => {
            const textMap: Record<string, string> = {
              monthly: '月付',
              quarterly: '季付',
              yearly: '年付',
              lifetime: '终身',
            };
            return textMap[row.billingCycle] || row.billingCycle;
          },
        },
      },
    },
    {
      field: 'price',
      title: '价格',
      width: 120,
      formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
    },
    {
      field: 'sortOrder',
      title: '排序',
      width: 80,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'isActive',
      title: '状态',
      width: 100,
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '会员计划',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}
