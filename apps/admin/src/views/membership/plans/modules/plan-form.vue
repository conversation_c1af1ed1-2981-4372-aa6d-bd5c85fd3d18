<script lang="ts" setup>
import type { MembershipPlanApi } from '#/api/membership/plans';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createMembershipPlan,
  isPlanCodeExists,
  updateMembershipPlan,
} from '#/api/membership/plans';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<MembershipPlanApi.Plan>();

// 自定义验证规则
const validateCode = async (rule: any, value: string) => {
  if (!value) return;

  // 检查格式
  if (!/^[A-Z][A-Z0-9_]*$/.test(value)) {
    throw new Error('代码必须以大写字母开头，只能包含大写字母、数字和下划线');
  }

  // 检查是否已存在（排除当前编辑的计划）
  try {
    const exists = await isPlanCodeExists(value, formData.value?.id);
    if (exists) {
      throw new Error('计划代码已存在');
    }
  } catch {
    throw new Error('检查计划代码失败');
  }
};

const validateFeatures = (rule: any, value: string) => {
  if (!value) return Promise.resolve();
  try {
    JSON.parse(value);
    return Promise.resolve();
  } catch {
    throw new Error('请输入有效的JSON格式');
  }
};

// 获取表单schema并添加自定义验证
const getFormSchema = () => {
  const schema = useFormSchema();
  return schema.map((item) => {
    if (item.fieldName === 'code') {
      return {
        ...item,
        rules: [
          'required',
          {
            pattern: /^[A-Z][A-Z0-9_]*$/,
            message: '代码必须以大写字母开头，只能包含大写字母、数字和下划线',
          },
          {
            validator: validateCode,
          },
        ],
      };
    }
    if (item.fieldName === 'features') {
      return {
        ...item,
        rules: [
          'required',
          {
            validator: validateFeatures,
          },
        ],
      };
    }
    return item;
  });
};

const [Form, formApi] = useVbenForm({
  schema: getFormSchema(),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    // 将features字符串解析为对象
    const formData = {
      ...values,
      features: values.features ? JSON.parse(values.features) : {},
    };

    drawerApi.lock();

    try {
      if (id.value) {
        await updateMembershipPlan(id.value, formData);
        message.success('会员计划更新成功');
      } else {
        await createMembershipPlan(formData);
        message.success('会员计划创建成功');
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存会员计划失败:', error);
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('保存失败');
      }
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<MembershipPlanApi.Plan>();
      formApi.resetForm();
      if (data) {
        formData.value = data;
        id.value = data.id;
        // 将features对象转换为JSON字符串
        const formValues = {
          ...data,
          features: data.features ? JSON.stringify(data.features, null, 2) : '',
        };
        formApi.setValues(formValues);
      } else {
        id.value = undefined;
        formData.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `${$t('common.edit')}会员计划`
    : `${$t('common.create')}会员计划`;
});

// 导出drawerApi供父组件使用
defineExpose({
  drawerApi,
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>

<style scoped>
.plan-form {
  padding: 24px;
}
</style>
