<script lang="ts" setup>
import type { SystemRoleApi, SystemUserApi } from '#/api';

import { computed, nextTick, onMounted, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Select } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getRoleList } from '#/api/system/role';
import { createUser, updateUser } from '#/api/system/user';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemUserApi.SystemUser>();
const roleOptions = ref<{ label: string; value: string }[]>([]);
const id = ref();

// 创建一个计算属性，根据是否为编辑模式动态生成表单配置
const isEditMode = computed(() => !!id.value);

// 动态生成表单配置，编辑模式下过滤掉密码字段
const formSchema = computed(() => {
  const schema = useFormSchema();
  // 如果是编辑模式，过滤掉密码字段
  if (isEditMode.value) {
    return schema.filter((field) => field.fieldName !== 'password');
  }
  return schema;
});

const [Form, formApi] = useVbenForm({
  schema: formSchema,
  showDefaultActions: false,
  commonConfig: {
    colon: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    formItemClass: 'w-full mb-4',
  },
  wrapperClass: 'w-full',
});
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    try {
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }

      const values = await formApi.getValues();
      drawerApi.lock();

      if (id.value) {
        // 编辑用户时，从提交数据中移除密码字段
        const { password: _password, ...updateData } = values;
        await updateUser(id.value, updateData);
      } else {
        await createUser(values);
      }

      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存用户失败:', error);
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 先重置表单，确保清除之前的状态
      formApi.resetForm();

      const data = drawerApi.getData<SystemUserApi.SystemUser>();
      if (data && data.id) {
        // 先设置ID，这样计算属性会过滤掉密码字段
        id.value = data.id;
        formData.value = data;

        // 等待下一个渲染周期，确保表单已经根据编辑模式重新渲染
        nextTick(() => {
          // 设置表单值
          formApi.setValues({
            ...data,
            id: data.id,
            roleIds: data.roles?.map((role) => role.id) || [],
          });
        });
      } else {
        // 先设置ID为undefined，这样计算属性会包含密码字段
        id.value = undefined;
        formData.value = undefined;

        // 等待下一个渲染周期，确保表单已经根据创建模式重新渲染
        nextTick(() => {
          // 重置表单
          formApi.resetForm();
        });
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? $t('ui.actionTitle.edit', [$t('system.user.name')])
    : $t('ui.actionTitle.create', [$t('system.user.name')]);
});

/**
 * 获取角色列表
 */
async function loadRoleOptions() {
  try {
    const result = await getRoleList({});

    // 处理不同的返回结构
    if (Array.isArray(result)) {
      // 直接返回数组的情况
      roleOptions.value = result.map((role: SystemRoleApi.SystemRole) => ({
        label: role.name,
        value: role.id,
      }));
    } else if (result && typeof result === 'object') {
      // 返回分页对象的情况
      const items = result.items || result.list || result.data || [];
      roleOptions.value = items.map((role: SystemRoleApi.SystemRole) => ({
        label: role.name,
        value: role.id,
      }));
    } else {
      console.error('角色列表返回格式不正确');
      roleOptions.value = [];
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    roleOptions.value = [];
  }
}

onMounted(() => {
  loadRoleOptions();
});
</script>
<template>
  <Drawer :title="getDrawerTitle" class="user-form-drawer">
    <Form class="w-full">
      <template #roleIds="slotProps">
        <div class="w-full">
          <Select
            v-bind="slotProps"
            :options="roleOptions"
            :placeholder="$t('system.user.selectRoles')"
            mode="multiple"
            :style="{ width: '100%' }"
            :dropdown-style="{ minWidth: '300px', maxHeight: '400px' }"
            :dropdown-match-select-width="false"
            :list-height="256"
            option-label-prop="label"
            show-search
            :filter-option="
              (input, option) =>
                option.label.toLowerCase().includes(input.toLowerCase())
            "
            class="w-full"
            popup-class-name="role-select-dropdown"
          />
        </div>
      </template>
    </Form>
  </Drawer>
</template>

<style lang="scss" scoped>
.user-form-drawer {
  :deep(.ant-select) {
    width: 100%;
  }

  :deep(.ant-form-item) {
    width: 100%;
  }
}

:global(.role-select-dropdown) {
  min-width: 300px !important;

  .ant-select-item {
    padding: 8px 12px;
    white-space: nowrap;
  }
}
</style>
