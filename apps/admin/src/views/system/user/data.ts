import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemUserApi } from '#/api/system';

import { $t } from '#/locales';

/**
 * 用户表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('system.user.username'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'password',
        placeholder: $t('system.user.passwordPlaceholder'),
      },
      fieldName: 'password',
      label: $t('system.user.password'),
      rules: 'required',
      helpText: $t('system.user.passwordHelp'),
    },
    {
      component: 'Input',
      fieldName: 'realName',
      label: $t('system.user.realName'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'email',
      },
      fieldName: 'email',
      label: $t('system.user.email'),
    },
    {
      component: 'Input',
      fieldName: 'phoneNumber',
      label: $t('system.user.phoneNumber'),
    },
    {
      component: 'Input',
      fieldName: 'idCardNumber',
      label: $t('system.user.idCardNumber'),
    },
    {
      component: 'Input',
      fieldName: 'openid',
      label: $t('system.user.openid'),
    },
    {
      component: 'Input',
      fieldName: 'unionid',
      label: $t('system.user.unionid'),
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 3,
      },
      fieldName: 'adminRemark',
      label: $t('system.user.adminRemark'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('system.user.status'),
    },
    {
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        placeholder: $t('system.user.selectRoles'),
        style: { width: '100%' },
        dropdownStyle: { minWidth: '200px' },
        dropdownMatchSelectWidth: false,
        listHeight: 256,
        optionLabelProp: 'label',
        showSearch: true,
        filterOption: (input, option) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      },
      fieldName: 'roleIds',
      label: $t('system.user.roles'),
      formItemClass: 'w-full',
    },
  ];
}

/**
 * 用户列表查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('system.user.username'),
    },
    {
      component: 'Input',
      fieldName: 'realName',
      label: $t('system.user.realName'),
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: $t('system.user.email'),
    },
    {
      component: 'Input',
      fieldName: 'phoneNumber',
      label: $t('system.user.phoneNumber'),
    },
    {
      component: 'Input',
      fieldName: 'idCardNumber',
      label: $t('system.user.idCardNumber'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('system.user.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('system.user.createTime'),
    },
  ];
}

/**
 * 用户列表表格列配置
 */
export function useColumns<T = SystemUserApi.SystemUser>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'username',
      title: $t('system.user.username'),
      width: 120,
    },
    {
      field: 'realName',
      title: $t('system.user.realName'),
      width: 120,
    },
    {
      field: 'email',
      title: $t('system.user.email'),
      width: 180,
    },
    {
      field: 'phoneNumber',
      title: $t('system.user.phoneNumber'),
      width: 130,
    },
    {
      field: 'idCardNumber',
      title: $t('system.user.idCardNumber'),
      width: 180,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('system.user.status'),
      width: 80,
    },
    {
      field: 'adminRemark',
      title: $t('system.user.adminRemark'),
      width: 150,
    },
    {
      field: 'createTime',
      title: $t('system.user.createTime'),
      width: 160,
    },
    {
      field: 'lastLoginTime',
      title: $t('system.user.lastLoginTime'),
      width: 160,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'username',
          nameTitle: $t('system.user.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.user.operation'),
      width: 130,
    },
  ];
}
