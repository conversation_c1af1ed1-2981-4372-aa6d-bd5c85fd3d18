<script lang="ts" setup>
import type { SystemPermissionApi } from '#/api/system/permission';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createPermission,
  isPermissionCodeExists,
  updatePermission,
} from '#/api/system/permission';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemPermissionApi.Permission>();

// 自定义验证规则
const validateCode = async (rule: any, value: string) => {
  if (!value) return;

  // 检查格式
  if (!/^[A-Z][A-Z0-9_]*$/.test(value)) {
    throw new Error('代码必须以大写字母开头，只能包含大写字母、数字和下划线');
  }

  // 检查是否已存在（排除当前编辑的权限）
  try {
    const exists = await isPermissionCodeExists(value, formData.value?.id);
    if (exists) {
      throw new Error('权限代码已存在');
    }
  } catch {
    throw new Error('检查权限代码失败');
  }
};

// 获取表单schema并添加自定义验证
const getFormSchema = () => {
  const schema = useFormSchema();
  return schema.map((item) => {
    if (item.fieldName === 'code') {
      return {
        ...item,
        rules: [
          'required',
          {
            pattern: /^[A-Z][A-Z0-9_]*$/,
            message: '代码必须以大写字母开头，只能包含大写字母、数字和下划线',
          },
          {
            validator: validateCode,
          },
        ],
      };
    }
    return item;
  });
};

const [Form, formApi] = useVbenForm({
  schema: getFormSchema(),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();
    drawerApi.lock();

    try {
      if (id.value) {
        await updatePermission(id.value, values);
        message.success('权限更新成功');
      } else {
        await createPermission(values);
        message.success('权限创建成功');
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存权限失败:', error);
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('保存失败');
      }
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemPermissionApi.Permission>();
      formApi.resetForm();
      if (data) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues(data);
      } else {
        id.value = undefined;
        formData.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `${$t('common.edit')}权限`
    : `${$t('common.create')}权限`;
});

// 导出drawerApi供父组件使用
defineExpose({
  drawerApi,
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>

<style scoped>
.permission-form {
  padding: 24px;
}
</style>
