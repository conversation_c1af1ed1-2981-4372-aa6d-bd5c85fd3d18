import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemPermissionApi } from '#/api/system/permission';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.permission.fields.name'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入权限代码，如：USER_CREATE',
      },
      fieldName: 'code',
      label: $t('system.permission.fields.code'),
      rules: [
        'required',
        {
          pattern: /^[A-Z][A-Z0-9_]*$/,
          message: '代码必须以大写字母开头，只能包含大写字母、数字和下划线',
        },
      ],
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: $t('system.permission.fields.description'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('system.permission.status'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.permission.fields.name'),
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.permission.fields.code'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('system.permission.status'),
    },
    {
      component: 'Input',
      fieldName: 'description',
      label: $t('system.permission.fields.description'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('system.permission.createTime'),
    },
  ];
}

export function useColumns<T = SystemPermissionApi.Permission>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: $t('system.permission.fields.name'),
      width: 200,
    },
    {
      field: 'code',
      title: $t('system.permission.fields.code'),
      width: 200,
      className: 'font-mono text-xs',
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('system.permission.status'),
      width: 100,
    },
    {
      field: 'description',
      minWidth: 100,
      title: $t('system.permission.fields.description'),
    },
    {
      field: 'createTime',
      title: $t('system.permission.createTime'),
      width: 200,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.permission.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.permission.operation'),
      width: 130,
    },
  ];
}
