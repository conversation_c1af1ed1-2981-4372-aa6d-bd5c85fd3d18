<script setup lang="ts">
import type { SystemMonitoringApi } from '#/api/system/monitoring';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Alert, Card, Col, Row, Statistic, Tag } from 'ant-design-vue';
import {
  Activity,
  Clock,
  Database,
  HardDrive,
  MemoryStick,
  Server,
  TrendingUp,
  Users,
} from 'lucide-vue-next';

import {
  getHealthStatus,
  getMonitoringDashboard,
  getSystemMetrics,
} from '#/api/system/monitoring';

// 响应式数据
const loading = ref(false);
const healthStatus = ref<null | SystemMonitoringApi.HealthStatus>(null);
const dashboardData = ref<null | SystemMonitoringApi.DashboardData>(null);
const systemMetrics = ref<null | SystemMonitoringApi.SystemMetrics>(null);
const refreshInterval = ref<NodeJS.Timeout | null>(null);

// 计算属性
const healthStatusColor = computed(() => {
  if (!healthStatus.value) return 'default';
  switch (healthStatus.value.status) {
    case 'error': {
      return 'error';
    }
    case 'ok': {
      return 'success';
    }
    case 'warning': {
      return 'warning';
    }
    default: {
      return 'default';
    }
  }
});

const healthStatusText = computed(() => {
  if (!healthStatus.value) return '未知';
  switch (healthStatus.value.status) {
    case 'error': {
      return '错误';
    }
    case 'ok': {
      return '正常';
    }
    case 'warning': {
      return '警告';
    }
    default: {
      return '未知';
    }
  }
});

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(1)}%`;
};

// 方法
async function loadData() {
  loading.value = true;
  try {
    const [health, dashboard, metrics] = await Promise.all([
      getHealthStatus(),
      getMonitoringDashboard(),
      getSystemMetrics(),
    ]);

    healthStatus.value = health;
    dashboardData.value = dashboard;
    systemMetrics.value = metrics;
  } catch (error) {
    console.error('加载监控数据失败:', error);
  } finally {
    loading.value = false;
  }
}

function startAutoRefresh() {
  refreshInterval.value = setInterval(() => {
    loadData();
  }, 30_000); // 30秒刷新一次
}

function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
}

// 生命周期
onMounted(() => {
  loadData();
  startAutoRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <Page auto-content-height>
    <div class="monitoring-dashboard">
      <!-- 系统健康状态 -->
      <Card title="系统健康状态" :loading="loading" class="mb-4">
        <div class="flex items-center gap-4">
          <Activity
            class="size-8"
            :class="{
              'text-green-500': healthStatus?.status === 'ok',
              'text-yellow-500': healthStatus?.status === 'warning',
              'text-red-500': healthStatus?.status === 'error',
            }"
          />
          <div>
            <div class="text-lg font-semibold">
              <Tag :color="healthStatusColor">{{ healthStatusText }}</Tag>
            </div>
            <div class="text-sm text-gray-500">
              最后检查: {{ new Date().toLocaleString() }}
            </div>
          </div>
        </div>
      </Card>

      <!-- 概览统计 -->
      <Row :gutter="16" class="mb-4">
        <Col :span="6">
          <Card>
            <Statistic
              title="总用户数"
              :value="dashboardData?.overview.totalUsers || 0"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <Users class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="活跃租户"
              :value="dashboardData?.overview.activeTenants || 0"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Server class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="总页面数"
              :value="dashboardData?.overview.totalPages || 0"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TrendingUp class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="今日访问"
              :value="dashboardData?.overview.todayVisits || 0"
              :value-style="{ color: '#eb2f96' }"
            >
              <template #prefix>
                <Clock class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
      </Row>

      <!-- 系统资源监控 -->
      <Row :gutter="16" class="mb-4">
        <Col :span="12">
          <Card title="CPU使用率" :loading="loading">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Server class="size-5 text-blue-500" />
                <span class="text-2xl font-bold">
                  {{ formatPercentage(systemMetrics?.cpu.usage || 0) }}
                </span>
              </div>
              <div class="text-sm text-gray-500">
                {{ systemMetrics?.cpu.cores || 0 }} 核心
              </div>
            </div>
          </Card>
        </Col>
        <Col :span="12">
          <Card title="内存使用率" :loading="loading">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <MemoryStick class="size-5 text-green-500" />
                <span class="text-2xl font-bold">
                  {{ formatPercentage(systemMetrics?.memory.usage || 0) }}
                </span>
              </div>
              <div class="text-sm text-gray-500">
                {{ formatBytes(systemMetrics?.memory.used || 0) }} /
                {{ formatBytes(systemMetrics?.memory.total || 0) }}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Row :gutter="16" class="mb-4">
        <Col :span="12">
          <Card title="磁盘使用率" :loading="loading">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <HardDrive class="size-5 text-orange-500" />
                <span class="text-2xl font-bold">
                  {{ formatPercentage(systemMetrics?.disk.usage || 0) }}
                </span>
              </div>
              <div class="text-sm text-gray-500">
                {{ formatBytes(systemMetrics?.disk.used || 0) }} /
                {{ formatBytes(systemMetrics?.disk.total || 0) }}
              </div>
            </div>
          </Card>
        </Col>
        <Col :span="12">
          <Card title="数据库连接" :loading="loading">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Database class="size-5 text-purple-500" />
                <span class="text-2xl font-bold">
                  {{ systemMetrics?.database.activeConnections || 0 }}
                </span>
              </div>
              <div class="text-sm text-gray-500">
                最大: {{ systemMetrics?.database.maxConnections || 0 }}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <!-- 性能指标 -->
      <Card title="性能指标" :loading="loading" class="mb-4">
        <Row :gutter="16">
          <Col :span="6">
            <Statistic
              title="响应时间"
              :value="dashboardData?.performance.responseTime || 0"
              suffix="ms"
              :value-style="{ color: '#3f8600' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="吞吐量"
              :value="dashboardData?.performance.throughput || 0"
              suffix="req/s"
              :value-style="{ color: '#1890ff' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="错误率"
              :value="
                formatPercentage(dashboardData?.performance.errorRate || 0)
              "
              :value-style="{ color: '#cf1322' }"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="运行时间"
              :value="dashboardData?.performance.uptime || 0"
              suffix="小时"
              :value-style="{ color: '#722ed1' }"
            />
          </Col>
        </Row>
      </Card>

      <!-- 告警信息 -->
      <Card
        title="系统告警"
        :loading="loading"
        v-if="dashboardData?.alerts?.length"
      >
        <div class="space-y-2">
          <Alert
            v-for="alert in dashboardData.alerts"
            :key="alert.id"
            :type="alert.type"
            :message="alert.message"
            :description="new Date(alert.timestamp).toLocaleString()"
            show-icon
          />
        </div>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.monitoring-dashboard {
  padding: 0;
}

.ant-card {
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
}
</style>
