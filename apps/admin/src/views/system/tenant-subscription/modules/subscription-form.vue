<script setup lang="ts">
import type { TenantSubscriptionApi } from '#/api/system/tenant-subscription';

import { ref, watch } from 'vue';

import { useVbenForm } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { getMembershipPlanList } from '#/api/membership/plans';
import { getTenantList } from '#/api/system/tenant';
import {
  createTenantSubscription,
  updateTenantSubscription,
} from '#/api/system/tenant-subscription';

interface Props {
  subscription?: Partial<TenantSubscriptionApi.Subscription>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  cancel: [];
  success: [];
}>();

// 租户选项
const tenantOptions = ref<Array<{ label: string; value: string }>>([]);
// 计划选项
const planOptions = ref<Array<{ label: string; value: string }>>([]);

const [Form, formApi] = useVbenForm({
  schema: [
    {
      component: 'Select',
      componentProps: {
        options: tenantOptions,
        placeholder: '请选择租户',
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'tenantId',
      label: '租户',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: planOptions,
        placeholder: '请选择订阅计划',
        showSearch: true,
      },
      fieldName: 'planId',
      label: '订阅计划',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '月付', value: 'monthly' },
          { label: '季付', value: 'quarterly' },
          { label: '年付', value: 'yearly' },
          { label: '终身', value: 'lifetime' },
        ],
        placeholder: '请选择计费周期',
      },
      fieldName: 'billingCycle',
      label: '计费周期',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入持续时间（天）',
        style: { width: '100%' },
      },
      fieldName: 'duration',
      label: '持续时间（天）',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择开始日期',
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'startDate',
      label: '开始日期',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择结束日期',
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'endDate',
      label: '结束日期',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      fieldName: 'autoRenew',
      label: '自动续费',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入元数据（JSON格式，可选）',
        rows: 3,
      },
      fieldName: 'metadata',
      label: '元数据',
    },
  ],
});

// 监听计费周期变化，自动计算结束日期
const startDate = ref('');
const billingCycle = ref('');
const duration = ref(0);

watch(
  () => [startDate.value, billingCycle.value, duration.value],
  ([start, cycle, dur]) => {
    if (start && cycle && dur) {
      const startTime = new Date(start);
      const end = new Date(startTime);

      switch (cycle) {
        case 'lifetime': {
          end.setFullYear(end.getFullYear() + 100); // 100年后
          break;
        }
        case 'monthly': {
          end.setMonth(end.getMonth() + Math.ceil(dur / 30));
          break;
        }
        case 'quarterly': {
          end.setMonth(end.getMonth() + Math.ceil(dur / 90) * 3);
          break;
        }
        case 'yearly': {
          end.setFullYear(end.getFullYear() + Math.ceil(dur / 365));
          break;
        }
        default: {
          end.setDate(end.getDate() + dur);
        }
      }

      formApi.setFieldValue('endDate', end.toISOString().split('T')[0]);
    }
  },
  { deep: true },
);

// 加载租户和计划数据
async function loadOptions() {
  try {
    // 加载租户列表
    const tenantsResult = await getTenantList({ pageSize: 1000 });
    tenantOptions.value = tenantsResult.items.map((tenant) => ({
      label: tenant.name,
      value: tenant.id.toString(),
    }));

    // 加载会员计划列表
    const plansResult = await getMembershipPlanList({
      pageSize: 1000,
      status: 1,
    });
    planOptions.value = plansResult.items.map((plan) => ({
      label: `${plan.name} (${plan.code})`,
      value: plan.id,
    }));
  } catch (error) {
    console.error('加载选项失败:', error);
    message.error('加载数据失败');
  }
}

// 组件挂载时加载数据
loadOptions();

// 设置表单初始值
if (props.subscription?.id) {
  formApi.setValues({
    tenantId: props.subscription.tenantId || '',
    planId: props.subscription.planId || '',
    billingCycle: props.subscription.billingCycle || 'monthly',
    duration: props.subscription.duration || 30,
    startDate: props.subscription.startDate
      ? props.subscription.startDate.split('T')[0]
      : '',
    endDate: props.subscription.endDate
      ? props.subscription.endDate.split('T')[0]
      : '',
    autoRenew: props.subscription.autoRenew || false,
    metadata: props.subscription.metadata
      ? JSON.stringify(props.subscription.metadata, null, 2)
      : '',
  });
}

// 表单提交处理
async function handleSubmit(values: any) {
  try {
    // 处理元数据
    let metadata = {};
    if (values.metadata) {
      try {
        metadata = JSON.parse(values.metadata);
      } catch {
        message.error('元数据格式不正确，请输入有效的JSON');
        return;
      }
    }

    const submitData: TenantSubscriptionApi.CreateSubscriptionData = {
      tenantId: values.tenantId,
      planId: values.planId,
      billingCycle: values.billingCycle,
      duration: Number(values.duration),
      startDate: new Date(values.startDate).toISOString(),
      endDate: new Date(values.endDate).toISOString(),
      autoRenew: Boolean(values.autoRenew),
      metadata,
    };

    if (props.subscription?.id) {
      // 更新订阅
      await updateTenantSubscription(props.subscription.id, submitData);
      message.success('订阅更新成功');
    } else {
      // 创建订阅
      await createTenantSubscription(submitData);
      message.success('订阅创建成功');
    }

    emit('success');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  }
}

function handleReset() {
  formApi.resetForm();
}
</script>

<template>
  <div class="p-4">
    <Form @submit="handleSubmit" @reset="handleReset" />
  </div>
</template>
