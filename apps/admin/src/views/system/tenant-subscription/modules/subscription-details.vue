<script setup lang="ts">
import type { TenantSubscriptionApi } from '#/api/system/tenant-subscription';

import { Card, Descriptions, Tag } from 'ant-design-vue';

interface Props {
  subscription?: Partial<TenantSubscriptionApi.Subscription>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  close: [];
}>();

// 获取状态的显示文本和颜色
function getStatusConfig(status: string) {
  const statusMap: Record<string, { color: string; text: string }> = {
    active: { text: '活跃', color: 'success' },
    cancelled: { text: '已取消', color: 'error' },
    expired: { text: '已过期', color: 'warning' },
    pending: { text: '待激活', color: 'processing' },
  };
  return statusMap[status] || { text: status, color: 'default' };
}

// 计费周期映射
function getBillingCycleText(cycle: string) {
  const cycleMap: Record<string, string> = {
    monthly: '月付',
    quarterly: '季付',
    yearly: '年付',
    lifetime: '终身',
  };
  return cycleMap[cycle] || cycle;
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString();
}

// 格式化时间
function formatDateTime(dateString?: string) {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString();
}

// 格式化元数据
function formatMetadata(metadata?: Record<string, any>) {
  if (!metadata || Object.keys(metadata).length === 0) {
    return '无';
  }
  return JSON.stringify(metadata, null, 2);
}
</script>

<template>
  <div class="p-6">
    <div class="mb-6">
      <h3 class="mb-2 text-lg font-semibold">订阅详情</h3>
      <p class="text-gray-600">查看租户订阅的详细信息</p>
    </div>

    <Card title="基本信息" class="mb-4">
      <Descriptions :column="2" bordered>
        <Descriptions.Item label="订阅ID">
          {{ props.subscription?.id || '-' }}
        </Descriptions.Item>
        <Descriptions.Item label="租户名称">
          {{ props.subscription?.tenantName || '-' }}
        </Descriptions.Item>
        <Descriptions.Item label="租户ID">
          {{ props.subscription?.tenantId || '-' }}
        </Descriptions.Item>
        <Descriptions.Item label="计划名称">
          {{ props.subscription?.planName || '-' }}
        </Descriptions.Item>
        <Descriptions.Item label="计划代码">
          {{ props.subscription?.planCode || '-' }}
        </Descriptions.Item>
        <Descriptions.Item label="计划ID">
          {{ props.subscription?.planId || '-' }}
        </Descriptions.Item>
      </Descriptions>
    </Card>

    <Card title="订阅信息" class="mb-4">
      <Descriptions :column="2" bordered>
        <Descriptions.Item label="订阅状态">
          <Tag
            v-if="props.subscription?.status"
            :color="getStatusConfig(props.subscription.status).color"
          >
            {{ getStatusConfig(props.subscription.status).text }}
          </Tag>
          <span v-else>-</span>
        </Descriptions.Item>
        <Descriptions.Item label="计费周期">
          {{
            props.subscription?.billingCycle
              ? getBillingCycleText(props.subscription.billingCycle)
              : '-'
          }}
        </Descriptions.Item>
        <Descriptions.Item label="持续时间">
          {{
            props.subscription?.duration
              ? `${props.subscription.duration} 天`
              : '-'
          }}
        </Descriptions.Item>
        <Descriptions.Item label="自动续费">
          <Tag :color="props.subscription?.autoRenew ? 'success' : 'default'">
            {{ props.subscription?.autoRenew ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="开始日期">
          {{ formatDate(props.subscription?.startDate) }}
        </Descriptions.Item>
        <Descriptions.Item label="结束日期">
          {{ formatDate(props.subscription?.endDate) }}
        </Descriptions.Item>
      </Descriptions>
    </Card>

    <Card title="时间信息" class="mb-4">
      <Descriptions :column="2" bordered>
        <Descriptions.Item label="创建时间">
          {{ formatDateTime(props.subscription?.createTime) }}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间">
          {{ formatDateTime(props.subscription?.updateTime) }}
        </Descriptions.Item>
      </Descriptions>
    </Card>

    <Card title="元数据" class="mb-4">
      <pre class="max-h-40 overflow-auto rounded bg-gray-50 p-4 text-sm">{{
        formatMetadata(props.subscription?.metadata)
      }}</pre>
    </Card>

    <div class="flex justify-end gap-4">
      <a-button @click="emit('close')"> 关闭 </a-button>
    </div>
  </div>
</template>

<style scoped>
.ant-descriptions-item-label {
  font-weight: 500;
}

pre {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>
