<script setup lang="ts">
import type { TenantSubscriptionApi } from '#/api/system/tenant-subscription';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { Button, message, Modal, Tag } from 'ant-design-vue';
import { Calendar, Edit, Eye, RefreshCw, X } from 'lucide-vue-next';

import {
  deleteTenantSubscription,
  getTenantSubscriptionList,
  renewTenantSubscription,
} from '#/api/system/tenant-subscription';

import SubscriptionDetails from './modules/subscription-details.vue';
import SubscriptionForm from './modules/subscription-form.vue';

// 抽屉配置
const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: SubscriptionForm,
  destroyOnClose: true,
  title: $t('system.tenantSubscription.title'),
});

const [DetailsDrawer, detailsDrawerApi] = useVbenDrawer({
  connectedComponent: SubscriptionDetails,
  destroyOnClose: true,
  title: $t('system.tenantSubscription.details'),
});

// 表单数据
const formData = ref<Partial<TenantSubscriptionApi.Subscription>>({});
const detailsData = ref<Partial<TenantSubscriptionApi.Subscription>>({});

// 表格列配置
const columns: any[] = [
  { type: 'checkbox', width: 60 },
  {
    field: 'tenantName',
    title: '租户名称',
    minWidth: 150,
  },
  {
    field: 'planName',
    title: '计划名称',
    minWidth: 120,
  },
  {
    field: 'billingCycle',
    title: '计费周期',
    width: 100,
    formatter: ({ cellValue }: { cellValue: string }) => {
      const cycleMap: Record<string, string> = {
        monthly: '月付',
        quarterly: '季付',
        yearly: '年付',
        lifetime: '终身',
      };
      return cycleMap[cellValue] || cellValue;
    },
  },
  {
    field: 'startDate',
    title: '开始时间',
    width: 120,
    formatter: ({ cellValue }: { cellValue: string }) => {
      return cellValue ? new Date(cellValue).toLocaleDateString() : '-';
    },
  },
  {
    field: 'endDate',
    title: '结束时间',
    width: 120,
    formatter: ({ cellValue }: { cellValue: string }) => {
      return cellValue ? new Date(cellValue).toLocaleDateString() : '-';
    },
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: { default: 'status' },
  },
  {
    field: 'autoRenew',
    title: '自动续费',
    width: 100,
    slots: { default: 'autoRenew' },
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 180,
    formatter: ({ cellValue }: { cellValue: string }) => {
      return cellValue ? new Date(cellValue).toLocaleString() : '-';
    },
  },
  {
    field: 'actions',
    title: '操作',
    width: 200,
    fixed: 'right',
    slots: { default: 'actions' },
  },
];

// 搜索表单配置
const searchFormSchema = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入租户名称',
    },
    fieldName: 'tenantName',
    label: '租户名称',
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      options: [
        { label: '活跃', value: 'active' },
        { label: '已取消', value: 'cancelled' },
        { label: '已过期', value: 'expired' },
        { label: '待激活', value: 'pending' },
      ],
      placeholder: '请选择状态',
    },
    fieldName: 'status',
    label: '订阅状态',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入计划ID',
    },
    fieldName: 'planId',
    label: '计划ID',
  },
];

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: searchFormSchema,
    submitOnChange: true,
  },
  gridOptions: {
    columns,
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          try {
            const result = await getTenantSubscriptionList({
              page: page.currentPage,
              pageSize: page.pageSize,
              ...formValues,
            });
            return result;
          } catch (error) {
            console.error('获取订阅列表失败:', error);
            message.error('获取订阅列表失败');
            return {
              items: [],
              total: 0,
              page: 1,
              pageSize: 10,
            };
          }
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    pagerConfig: {
      enabled: true,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  },
});

/**
 * 确认对话框
 */
function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

// 获取状态的显示文本和颜色
function getStatusConfig(status: string) {
  const statusMap: Record<string, { color: string; text: string }> = {
    active: { text: '活跃', color: 'success' },
    cancelled: { text: '已取消', color: 'error' },
    expired: { text: '已过期', color: 'warning' },
    pending: { text: '待激活', color: 'processing' },
  };
  return statusMap[status] || { text: status, color: 'default' };
}

function onView(row: TenantSubscriptionApi.Subscription) {
  detailsData.value = { ...row };
  detailsDrawerApi.open();
}

function onEdit(row: TenantSubscriptionApi.Subscription) {
  formData.value = { ...row };
  formDrawerApi.open();
}

function onCancel(row: TenantSubscriptionApi.Subscription) {
  confirm(`确定要取消租户 "${row.tenantName}" 的订阅吗？`, '取消订阅')
    .then(async () => {
      const hideLoading = message.loading({
        content: '正在取消订阅...',
        duration: 0,
        key: 'cancel_subscription',
      });

      try {
        await deleteTenantSubscription(row.id);
        message.success({
          content: '订阅取消成功',
          key: 'cancel_subscription',
        });
        onRefresh();
      } catch (error) {
        console.error('取消订阅失败:', error);
        hideLoading();
        message.error('取消订阅失败');
      }
    })
    .catch(() => {
      // 用户取消操作
    });
}

function onRenew(row: TenantSubscriptionApi.Subscription) {
  // 这里可以打开续费对话框，暂时简化处理
  Modal.confirm({
    title: '续费订阅',
    content: `确定要为租户 "${row.tenantName}" 续费一个月吗？`,
    onOk: async () => {
      try {
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1);

        await renewTenantSubscription(row.id, {
          duration: 30,
          endDate: nextMonth.toISOString(),
        });

        message.success('续费成功');
        onRefresh();
      } catch (error) {
        console.error('续费失败:', error);
        message.error('续费失败');
      }
    },
  });
}

function onDelete(row: TenantSubscriptionApi.Subscription) {
  confirm(`确定要删除租户 "${row.tenantName}" 的订阅记录吗？`, '删除订阅')
    .then(async () => {
      const hideLoading = message.loading({
        content: '正在删除...',
        duration: 0,
        key: 'delete_subscription',
      });

      try {
        await deleteTenantSubscription(row.id);
        message.success({
          content: '删除成功',
          key: 'delete_subscription',
        });
        onRefresh();
      } catch (error) {
        console.error('删除失败:', error);
        hideLoading();
        message.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formData.value = {};
  formDrawerApi.open();
}

function onFormSuccess() {
  formDrawerApi.close();
  onRefresh();
}

function onDetailsClose() {
  detailsDrawerApi.close();
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onFormSuccess">
      <SubscriptionForm
        :subscription="formData"
        @success="onFormSuccess"
        @cancel="formDrawerApi.close()"
      />
    </FormDrawer>

    <DetailsDrawer @success="onDetailsClose">
      <SubscriptionDetails
        :subscription="detailsData"
        @close="onDetailsClose"
      />
    </DetailsDrawer>

    <Grid table-title="租户订阅管理">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Calendar class="size-4" />
          创建订阅
        </Button>
        <Button @click="onRefresh">
          <RefreshCw class="size-4" />
          刷新
        </Button>
      </template>

      <template #status="{ row }">
        <Tag :color="getStatusConfig(row.status).color">
          {{ getStatusConfig(row.status).text }}
        </Tag>
      </template>

      <template #autoRenew="{ row }">
        <Tag :color="row.autoRenew ? 'success' : 'default'">
          {{ row.autoRenew ? '是' : '否' }}
        </Tag>
      </template>

      <template #actions="{ row }">
        <div class="flex gap-2">
          <Button size="small" type="link" @click="onView(row)">
            <Eye class="size-4" />
            查看
          </Button>
          <Button size="small" type="link" @click="onEdit(row)">
            <Edit class="size-4" />
            编辑
          </Button>
          <Button
            v-if="row.status === 'active'"
            size="small"
            type="link"
            @click="onRenew(row)"
          >
            <Calendar class="size-4" />
            续费
          </Button>
          <Button
            v-if="row.status === 'active'"
            size="small"
            type="link"
            @click="onCancel(row)"
          >
            <X class="size-4" />
            取消
          </Button>
          <Button size="small" type="link" danger @click="onDelete(row)">
            删除
          </Button>
        </div>
      </template>
    </Grid>
  </Page>
</template>
