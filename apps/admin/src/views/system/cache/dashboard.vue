<script setup lang="ts">
import type { SystemCacheApi } from '#/api/system/cache';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  Button,
  Card,
  Col,
  message,
  Modal,
  Row,
  Statistic,
  Table,
  Tag,
} from 'ant-design-vue';
import {
  Activity,
  Database,
  MemoryStick,
  RefreshCw,
  Trash2,
  TrendingUp,
} from 'lucide-vue-next';

import {
  clearAllCache,
  clearMenuCache,
  deleteCacheKey,
  getCacheDashboard,
  getCacheKeys,
} from '#/api/system/cache';

// 响应式数据
const loading = ref(false);
const cacheData = ref<null | SystemCacheApi.DashboardData>(null);
const cacheKeys = ref<Array<SystemCacheApi.CacheKey>>([]);
const keysLoading = ref(false);
const refreshInterval = ref<NodeJS.Timeout | null>(null);

// 分页参数
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 搜索参数
const searchPattern = ref('');

// 计算属性
const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

const formatTTL = (ttl: number) => {
  if (ttl === -1) return '永不过期';
  if (ttl === 0) return '已过期';
  if (ttl < 60) return `${ttl}秒`;
  if (ttl < 3600) return `${Math.floor(ttl / 60)}分钟`;
  return `${Math.floor(ttl / 3600)}小时`;
};

const hitRateColor = computed(() => {
  if (!cacheData.value) return 'default';
  const rate = cacheData.value.metrics.hitRate;
  if (rate >= 0.9) return 'success';
  if (rate >= 0.7) return 'warning';
  return 'error';
});

// 表格列配置
const columns = [
  {
    title: '缓存键',
    dataIndex: 'key',
    key: 'key',
    ellipsis: true,
    width: 300,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
    width: 120,
  },
  {
    title: 'TTL',
    dataIndex: 'ttl',
    key: 'ttl',
    width: 120,
  },
  {
    title: '过期时间',
    dataIndex: 'expireTime',
    key: 'expireTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
  },
];

// 方法
async function loadDashboardData() {
  loading.value = true;
  try {
    const data = await getCacheDashboard();
    cacheData.value = data;
  } catch (error) {
    console.error('加载缓存数据失败:', error);
    message.error('加载缓存数据失败');
  } finally {
    loading.value = false;
  }
}

async function loadCacheKeys() {
  keysLoading.value = true;
  try {
    const result = await getCacheKeys({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      pattern: searchPattern.value || undefined,
    });

    cacheKeys.value = result.items;
    pagination.value.total = result.total;
  } catch (error) {
    console.error('加载缓存键失败:', error);
    message.error('加载缓存键失败');
  } finally {
    keysLoading.value = false;
  }
}

function handleTableChange(pag: any) {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  loadCacheKeys();
}

function handleSearch() {
  pagination.value.current = 1;
  loadCacheKeys();
}

async function handleClearAll() {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空所有缓存吗？此操作不可恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await clearAllCache();
        message.success('缓存已清空');
        loadDashboardData();
        loadCacheKeys();
      } catch (error) {
        console.error('清空缓存失败:', error);
        message.error('清空缓存失败');
      }
    },
  });
}

async function handleClearMenu() {
  try {
    await clearMenuCache();
    message.success('菜单缓存已清空');
    loadDashboardData();
    loadCacheKeys();
  } catch (error) {
    console.error('清空菜单缓存失败:', error);
    message.error('清空菜单缓存失败');
  }
}

async function handleDeleteKey(key: string) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除缓存键 "${key}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteCacheKey(key);
        message.success('缓存键已删除');
        loadCacheKeys();
        loadDashboardData();
      } catch (error) {
        console.error('删除缓存键失败:', error);
        message.error('删除缓存键失败');
      }
    },
  });
}

function handleRefresh() {
  loadDashboardData();
  loadCacheKeys();
}

function startAutoRefresh() {
  refreshInterval.value = setInterval(() => {
    loadDashboardData();
  }, 30_000); // 30秒刷新一次
}

function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData();
  loadCacheKeys();
  startAutoRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <Page auto-content-height>
    <div class="cache-dashboard">
      <!-- 概览统计 -->
      <Row :gutter="16" class="mb-4">
        <Col :span="6">
          <Card>
            <Statistic
              title="缓存命中率"
              :value="(cacheData?.metrics?.hitRate || 0) * 100"
              suffix="%"
              :value-style="{
                color:
                  hitRateColor === 'success'
                    ? '#3f8600'
                    : hitRateColor === 'warning'
                      ? '#faad14'
                      : '#cf1322',
              }"
            >
              <template #prefix>
                <TrendingUp class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="内存使用"
              :value="cacheData?.info.memoryUsage || 0"
              suffix="MB"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <MemoryStick class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="缓存键数量"
              :value="cacheData?.info.keys || 0"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <Database class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="运行时间"
              :value="cacheData?.info.uptime || 0"
              suffix="秒"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <Activity class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
      </Row>

      <!-- 操作按钮 -->
      <Card class="mb-4">
        <div class="flex items-center justify-between">
          <div class="flex gap-2">
            <Button type="primary" @click="handleRefresh" :loading="loading">
              <RefreshCw class="size-4" />
              刷新数据
            </Button>
            <Button @click="handleClearMenu"> 清空菜单缓存 </Button>
            <Button danger @click="handleClearAll">
              <Trash2 class="size-4" />
              清空所有缓存
            </Button>
          </div>
          <div class="flex items-center gap-2">
            <a-input
              v-model:value="searchPattern"
              placeholder="输入缓存键模式..."
              style="width: 200px"
              @press-enter="handleSearch"
            />
            <Button @click="handleSearch">搜索</Button>
          </div>
        </div>
      </Card>

      <!-- 缓存键列表 -->
      <Card title="缓存键管理">
        <Table
          :columns="columns"
          :data-source="cacheKeys"
          :loading="keysLoading"
          :pagination="pagination"
          @change="handleTableChange"
          row-key="key"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.key === 'type'">
              <Tag color="blue">{{ text }}</Tag>
            </template>
            <template v-else-if="column.key === 'size'">
              {{ formatBytes(text) }}
            </template>
            <template v-else-if="column.key === 'ttl'">
              {{ formatTTL(text) }}
            </template>
            <template v-else-if="column.key === 'expireTime'">
              {{ text ? new Date(text).toLocaleString() : '-' }}
            </template>
            <template v-else-if="column.key === 'action'">
              <Button
                size="small"
                danger
                type="link"
                @click="handleDeleteKey(record.key)"
              >
                <Trash2 class="size-4" />
                删除
              </Button>
            </template>
          </template>
        </Table>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.cache-dashboard {
  padding: 0;
}

.ant-card {
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
}
</style>
