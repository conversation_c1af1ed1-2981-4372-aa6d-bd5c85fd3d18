<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FeatureCodeApi } from '#/api/system/feature-code';

import { onMounted, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteFeatureCode,
  getFeatureCodeList,
} from '#/api/system/feature-code';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import MenuForm from './modules/menu-form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [MenuModal, menuModalApi] = useVbenModal({
  connectedComponent: MenuForm,
  destroyOnClose: true,
});

/**
 * 表格操作按钮点击事件
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<FeatureCodeApi.FeatureCode>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'menus': {
      onManageMenus(row);
      break;
    }
  }
}

/**
 * 确认对话框
 */
function confirm(content: string, title: string) {
  return new Promise<void>((resolve, reject) => {
    Modal.confirm({
      title,
      content,
      onOk: () => resolve(),
      onCancel: () => reject(new Error('用户取消操作')),
    });
  });
}

// 存储所有功能代码数据
const allFeatureCodes = ref<FeatureCodeApi.FeatureCode[]>([]);
// 加载状态
const loading = ref(false);

// 初始加载数据
async function loadData() {
  // 显示加载动画
  gridApi.setLoading(true);
  loading.value = true;
  try {
    const data = await getFeatureCodeList({});
    allFeatureCodes.value = data;
    // 更新表格数据
    gridApi.setGridOptions({ data });
  } catch (error) {
    console.error('获取功能代码列表失败:', error);
    message.error('获取功能代码列表失败');
  } finally {
    // 隐藏加载动画
    gridApi.setLoading(false);
    loading.value = false;
  }
}

// 前端筛选函数
function filterData(formValues: any) {
  if (allFeatureCodes.value.length === 0) return;

  // 显示加载动画，提升用户体验
  gridApi.setLoading(true);

  try {
    // 如果没有筛选条件，显示所有数据
    if (
      !formValues ||
      Object.keys(formValues).every((key) => !formValues[key])
    ) {
      gridApi.setGridOptions({ data: allFeatureCodes.value });
      return;
    }

    // 前端筛选
    const filteredData = allFeatureCodes.value.filter((item) => {
      // 代码筛选
      if (
        formValues.code &&
        !item.code.toLowerCase().includes(formValues.code.toLowerCase())
      ) {
        return false;
      }
      // 名称筛选
      if (
        formValues.name &&
        !item.name.toLowerCase().includes(formValues.name.toLowerCase())
      ) {
        return false;
      }
      // 模块筛选
      if (
        formValues.module &&
        !item.module.toLowerCase().includes(formValues.module.toLowerCase())
      ) {
        return false;
      }
      // 状态筛选
      if (
        formValues.isActive !== undefined &&
        formValues.isActive !== null &&
        item.isActive !== formValues.isActive
      ) {
        return false;
      }
      return true;
    });

    // 更新表格数据
    gridApi.setGridOptions({ data: filteredData });
  } finally {
    // 隐藏加载动画
    gridApi.setLoading(false);
  }
}

// 创建表格组件和API
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    submitOnChange: true, // 表单值变化时自动提交
    handleSubmit: filterData, // 自定义提交处理函数
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false, // 禁用分页
    },
    data: [], // 初始为空数组
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true, // 显示搜索按钮
      zoom: true,
    },
  } as VxeTableGridOptions<FeatureCodeApi.FeatureCode>,
});

function onEdit(row: FeatureCodeApi.FeatureCode) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: FeatureCodeApi.FeatureCode) {
  confirm(
    $t('ui.actionMessage.deleteConfirm', [row.name]),
    $t('ui.actionTitle.delete', [$t('system.featureCode.name')]),
  )
    .then(() => {
      const hideLoading = message.loading({
        content: $t('ui.actionMessage.deleting', [row.name]),
        duration: 0,
        key: 'action_process_msg',
      });
      deleteFeatureCode(row.code)
        .then(() => {
          message.success({
            content: $t('ui.actionMessage.deleteSuccess', [row.name]),
            key: 'action_process_msg',
          });
          onRefresh();
        })
        .catch(() => {
          hideLoading();
        });
    })
    .catch(() => {
      // 用户取消删除
    });
}

function onRefresh() {
  loadData();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}

function onManageMenus(row: FeatureCodeApi.FeatureCode) {
  menuModalApi.setData({ featureCode: row }).open();
}

// 组件挂载时加载数据
onMounted(() => {
  loadData();

  // 重写query方法，使其调用我们的loadData函数
  gridApi.query = () => {
    loadData();
    return Promise.resolve();
  };
});
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <MenuModal @success="onRefresh" />
    <Grid :table-title="$t('system.featureCode.list')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('system.featureCode.name')]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
