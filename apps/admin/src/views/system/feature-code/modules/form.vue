<script lang="ts" setup>
import type { FeatureCodeApi } from '#/api/system/feature-code';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button, Input, message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getFeatureCodeDetail,
  upsertFeatureCode,
} from '#/api/system/feature-code';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<FeatureCodeApi.FeatureCode>();
const isLoading = ref(false);

// 元数据编辑相关
const metadataModalVisible = ref<boolean>(false);
const metadataValue = ref<string>('');

// 格式化后的元数据，用于显示
const formattedMetadata = computed(() => {
  if (!formData.value?.metadata) return '{}';

  try {
    // 如果是对象，直接格式化
    return JSON.stringify(formData.value.metadata, null, 2);
  } catch {
    return '{}';
  }
});

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  commonConfig: {
    colon: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    formItemClass: 'w-full mb-4',
  },
});

// 打开元数据编辑器
async function openMetadataEditor() {
  // 使用 formData 中的元数据对象
  const metadata = formData.value?.metadata || {};

  // 将对象转换为格式化的JSON字符串
  metadataValue.value = JSON.stringify(metadata, null, 2);
  metadataModalVisible.value = true;
}

// 保存元数据
async function saveMetadata() {
  try {
    if (!metadataValue.value.trim()) {
      // 如果为空，设置为空对象
      formData.value!.metadata = {};

      // 更新表单中的元数据字符串字段
      const values = await formApi.getValues();
      values.metadataStr = '{}';
      await formApi.setValues(values);

      metadataModalVisible.value = false;
      return;
    }

    // 解析JSON字符串以验证格式
    const metadata = JSON.parse(metadataValue.value);

    // 更新 formData 中的元数据对象
    formData.value!.metadata = metadata;

    // 更新表单中的元数据字符串字段
    const values = await formApi.getValues();
    values.metadataStr = metadataValue.value;
    await formApi.setValues(values);

    message.success($t('ui.actionMessage.operationSuccess'));
    metadataModalVisible.value = false;
  } catch {
    message.error('JSON格式无效，请检查');
  }
}

const getDrawerTitle = () => {
  return formData.value?.id
    ? $t('ui.actionTitle.edit', [$t('system.featureCode.name')])
    : $t('ui.actionTitle.create', [$t('system.featureCode.name')]);
};

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    drawerApi.lock();
    isLoading.value = true;

    try {
      const values = await formApi.getValues();

      // 使用 formData 中的元数据对象
      if (formData.value && formData.value.metadata) {
        values.metadata = formData.value.metadata;
      } else {
        // 如果 formData 中没有元数据，尝试从表单中的 metadataStr 解析
        try {
          values.metadata = values.metadataStr
            ? JSON.parse(values.metadataStr)
            : {};
        } catch {
          values.metadata = {};
        }
      }

      // 删除临时字段
      delete values.metadataStr;

      await upsertFeatureCode(values);
      message.success($t('ui.actionMessage.operationSuccess'));
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存功能代码失败:', error);
      message.error('保存功能代码失败');
    } finally {
      drawerApi.unlock();
      isLoading.value = false;
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<FeatureCodeApi.FeatureCode>();
      formApi.resetForm();

      if (data?.id) {
        isLoading.value = true;
        try {
          const detail = await getFeatureCodeDetail(data.code);
          // 处理获取到的功能代码详情
          if (detail) {
            // 保存原始数据，包括元数据对象
            formData.value = { ...detail };

            // 为表单准备数据，将元数据对象转换为JSON字符串
            const formValues = { ...detail };
            if (
              formValues.metadata &&
              typeof formValues.metadata === 'object'
            ) {
              formValues.metadataStr = JSON.stringify(formValues.metadata);
              // 删除原始元数据字段，避免类型错误
              delete formValues.metadata;
            }

            await formApi.setValues(formValues);
          }
        } catch (error) {
          console.error('获取功能代码详情失败:', error);
          message.error('获取功能代码详情失败');
        } finally {
          isLoading.value = false;
        }
      } else {
        formData.value = undefined;
      }
    }
  },
  title: getDrawerTitle(),
});
</script>

<template>
  <Drawer :loading="isLoading">
    <Form>
      <!-- 自定义元数据显示和编辑区域 -->
      <div class="mb-4 mt-4">
        <div class="mb-2 flex items-center">
          <span class="w-1/4 pr-2 text-right">
            {{ $t('system.featureCode.metadata') }}:
          </span>
          <Button type="primary" size="small" @click="openMetadataEditor">
            编辑元数据
          </Button>
        </div>
        <div
          class="ml-[25%] max-h-[200px] overflow-auto whitespace-pre rounded border bg-[#1e1e1e] p-2 text-white"
          style="font-family: monospace"
        >
          {{ formattedMetadata }}
        </div>
      </div>
    </Form>
  </Drawer>

  <!-- 元数据编辑模态框 -->
  <Modal
    v-model:open="metadataModalVisible"
    :title="$t('system.featureCode.metadata')"
    @ok="saveMetadata"
  >
    <div>
      <Input.TextArea
        v-model:value="metadataValue"
        placeholder="{}"
        :rows="10"
      />
      <div class="mt-2 text-gray-500">
        请输入JSON格式的元数据，例如：{"key": "value"}
      </div>
    </div>
  </Modal>
</template>
