<script lang="ts" setup>
import type { FeatureCodeApi } from '#/api/system/feature-code';

import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Spin, Tree } from 'ant-design-vue';

import {
  getFeatureCodeMenus,
  setFeatureCodeMenus,
} from '#/api/system/feature-menu';
import { getMenuList } from '#/api/system/menu';
import { $t } from '#/locales';

const emits = defineEmits(['success']);

const menuTreeData = ref<any[]>([]);
const checkedKeys = ref<number[]>([]);
const expandedKeys = ref<number[]>([]);
const isLoading = ref(false);
const menuLoading = ref(false);

// 加载菜单树数据
async function loadMenuTree() {
  menuLoading.value = true;
  try {
    const menuList = await getMenuList();
    menuTreeData.value = convertToTreeData(menuList);
    // 默认展开第一级菜单
    expandedKeys.value = menuTreeData.value.map((item) => item.key);
  } catch (error) {
    console.error('获取菜单列表失败:', error);
    message.error('获取菜单列表失败');
  } finally {
    menuLoading.value = false;
  }
}

// 加载功能代码关联的菜单
async function loadFeatureCodeMenus() {
  if (!currentFeatureCode.value?.code) {
    return;
  }

  isLoading.value = true;
  try {
    const menus = await getFeatureCodeMenus(currentFeatureCode.value.code);
    checkedKeys.value = menus.map((menu) => menu.menuId);
  } catch (error) {
    console.error('获取功能代码关联菜单失败:', error);
    message.error('获取功能代码关联菜单失败');
  } finally {
    isLoading.value = false;
  }
}

// 将菜单列表转换为树形结构
function convertToTreeData(menuList: any[]) {
  // 递归处理菜单树
  function processMenuTree(menus: any[]): any[] {
    return menus.map((menu) => {
      const result = {
        ...menu,
        key: menu.id,
        // 使用meta.title作为显示标题，如果没有则使用name
        title: menu.meta?.title || menu.name,
        // 传递图标信息
        icon: menu.meta?.icon,
      };

      // 如果有子菜单，递归处理
      if (menu.children && menu.children.length > 0) {
        result.children = processMenuTree(menu.children);
      }

      return result;
    });
  }

  // 处理菜单树
  return processMenuTree(menuList);
}

// 保存功能代码关联的菜单
async function saveFeatureCodeMenus() {
  // 检查currentFeatureCode是否存在
  if (!currentFeatureCode.value) {
    console.error('功能代码对象不存在');
    return;
  }

  // 检查currentFeatureCode.code是否存在
  if (!currentFeatureCode.value.code) {
    console.error('功能代码不存在');
    return;
  }

  // 确保checkedKeys是数组
  const menuIds = Array.isArray(checkedKeys.value) ? checkedKeys.value : [];

  isLoading.value = true;
  try {
    // 直接调用API
    await setFeatureCodeMenus(currentFeatureCode.value.code, menuIds);

    message.success('操作成功');
    emits('success');
    modalApi.close();
  } catch (error) {
    console.error('保存功能代码关联菜单失败:', error);
    message.error('保存功能代码关联菜单失败');
  } finally {
    isLoading.value = false;
  }
}

// 处理树选择变化
function handleTreeCheck(keys: any, _info: any) {
  // 当check-strictly为false时，Tree组件会自动处理父子节点的选中状态
  // 我们只需要保存选中的节点ID即可
  if (keys && Array.isArray(keys)) {
    checkedKeys.value = keys;
  } else if (keys && keys.checked && Array.isArray(keys.checked)) {
    checkedKeys.value = keys.checked;
  }
}

// 存储从外部传入的功能代码数据
const currentFeatureCode = ref<FeatureCodeApi.FeatureCode | null>(null);

const [VbenModal, modalApi] = useVbenModal({
  title: computed(
    () =>
      `${$t('system.featureCode.manageMenus')}: ${currentFeatureCode.value?.name || ''}`,
  ),
  width: 700,
  footer: null, // 禁用默认的footer，使用自定义footer
  onOpenChange(visible) {
    if (visible) {
      // 获取外部传入的数据
      const data = modalApi.getData<{
        featureCode: FeatureCodeApi.FeatureCode;
      }>();

      if (data?.featureCode) {
        currentFeatureCode.value = data.featureCode;
      } else {
        console.error('未获取到功能代码数据');
      }

      loadMenuTree();
      loadFeatureCodeMenus();
    }
  },
});

// 组件挂载时加载数据
onMounted(() => {
  loadMenuTree();
  loadFeatureCodeMenus();
});
</script>

<template>
  <VbenModal>
    <Spin :spinning="isLoading || menuLoading">
      <div class="mb-4">
        <div class="mb-2 font-medium">
          {{ $t('system.featureCode.selectMenus') }}
        </div>
        <div class="mb-4 text-sm text-gray-500">
          {{ $t('system.featureCode.selectMenusDesc') }}
        </div>
        <Tree
          v-model:checked-keys="checkedKeys"
          v-model:expanded-keys="expandedKeys"
          :tree-data="menuTreeData"
          checkable
          @check="handleTreeCheck"
        >
          <template #title="{ title, icon }">
            <div class="flex items-center">
              <span v-if="icon" class="mr-1">
                <i :class="icon"></i>
              </span>
              <span>{{ title }}</span>
            </div>
          </template>
        </Tree>
      </div>
    </Spin>

    <div class="mt-4 flex justify-end border-t pt-4">
      <button class="mr-2 rounded border px-4 py-2" @click="modalApi.close()">
        取消
      </button>
      <button
        class="rounded bg-blue-500 px-4 py-2 text-white"
        :disabled="isLoading"
        @click="saveFeatureCodeMenus"
      >
        确认
      </button>
    </div>
  </VbenModal>
</template>
