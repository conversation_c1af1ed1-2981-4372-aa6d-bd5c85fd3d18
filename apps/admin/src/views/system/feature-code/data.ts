import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FeatureCodeApi } from '#/api/system/feature-code';

import { $t } from '#/locales';

/**
 * 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.featureCode.code'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.featureCode.name'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'module',
      label: $t('system.featureCode.module'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: $t('system.featureCode.description'),
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: true },
          { label: $t('common.disabled'), value: false },
        ],
        optionType: 'button',
      },
      defaultValue: true,
      fieldName: 'isActive',
      label: $t('system.featureCode.isActive'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'sortOrder',
      label: $t('system.featureCode.sortOrder'),
    },
    // 元数据字段 - 使用隐藏的 Input 组件，不显示标签
    {
      component: 'Input',
      componentProps: {
        style: { display: 'none' },
      },
      defaultValue: '{}',
      fieldName: 'metadataStr',
      label: '', // 空标签，不显示
    },
  ];
}

/**
 * 表格列配置
 */
export function useColumns<T = FeatureCodeApi.FeatureCode>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'code',
      title: $t('system.featureCode.code'),
      width: 150,
    },
    {
      field: 'name',
      title: $t('system.featureCode.name'),
      width: 150,
    },
    {
      field: 'module',
      title: $t('system.featureCode.module'),
      width: 120,
    },
    {
      field: 'description',
      title: $t('system.featureCode.description'),
      minWidth: 200,
    },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: $t('common.enabled'), value: true },
          { color: 'error', label: $t('common.disabled'), value: false },
        ],
      },
      field: 'isActive',
      title: $t('system.featureCode.isActive'),
      width: 100,
    },
    {
      field: 'sortOrder',
      title: $t('system.featureCode.sortOrder'),
      width: 100,
    },
    {
      field: 'createdAt',
      title: $t('system.featureCode.createdAt'),
      width: 180,
      formatter: ({ row }) => {
        return row.createTime || row.createdAt;
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.featureCode.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          { code: 'edit', text: '编辑' },
          { code: 'menus', text: '关联菜单' },
          { code: 'delete', text: '删除' },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.featureCode.operation'),
      width: 250,
    },
  ];
}

/**
 * 表格查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.featureCode.code'),
      componentProps: {
        placeholder: $t('ui.placeholder.input'),
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.featureCode.name'),
      componentProps: {
        placeholder: $t('ui.placeholder.input'),
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'module',
      label: $t('system.featureCode.module'),
      componentProps: {
        placeholder: $t('ui.placeholder.input'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: $t('ui.placeholder.select'),
        options: [
          { label: $t('common.enabled'), value: true },
          { label: $t('common.disabled'), value: false },
        ],
      },
      fieldName: 'isActive',
      label: $t('system.featureCode.isActive'),
    },
  ];
}
