import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemTenantApi } from '#/api/system/tenant';

import { $t } from '#/locales';

/**
 * 租户表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.tenant.tenantCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.tenant.tenantName'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: 'https://example.com',
      },
      fieldName: 'website',
      label: $t('system.tenant.website'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: 'example.com',
      },
      fieldName: 'domain',
      label: $t('system.tenant.domain'),
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('system.tenant.status'),
    },
    {
      component: 'Input',
      fieldName: 'metadata.industry',
      label: $t('system.tenant.industry'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('common.small'), value: '小型' },
          { label: $t('common.medium'), value: '中型' },
          { label: $t('common.large'), value: '大型' },
        ],
      },
      fieldName: 'metadata.size',
      label: $t('system.tenant.size'),
    },
    {
      component: 'Input',
      fieldName: 'metadata.region',
      label: $t('system.tenant.region'),
    },
    {
      component: 'Input',
      fieldName: 'datasource.name',
      label: $t('system.tenant.datasourceName'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: 'postgresql://user:password@localhost:5432/db_name',
      },
      fieldName: 'datasource.url',
      label: $t('system.tenant.datasourceUrl'),
      rules: 'required',
      helpText: '格式: postgresql://username:password@host:port/database_name',
    },
  ];
}

/**
 * 租户列表查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.tenant.tenantName'),
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.tenant.tenantCode'),
    },
    {
      component: 'Input',
      fieldName: 'domain',
      label: $t('system.tenant.domain'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('system.tenant.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('system.tenant.createTime'),
    },
  ];
}

/**
 * 租户列表表格列配置
 */
export function useColumns<T = SystemTenantApi.SystemTenant>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: $t('system.tenant.tenantName'),
      width: 200,
    },
    {
      field: 'code',
      title: $t('system.tenant.tenantCode'),
      width: 150,
    },
    {
      field: 'id',
      title: $t('system.tenant.id'),
      width: 100,
    },
    {
      field: 'website',
      title: $t('system.tenant.website'),
      width: 200,
    },
    {
      field: 'domain',
      title: $t('system.tenant.domain'),
      width: 200,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('system.tenant.status'),
      width: 100,
    },
    {
      field: 'createTime',
      title: $t('system.tenant.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.tenant.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          'edit',
          'delete',
          { code: 'features', text: $t('system.tenantFeature.manage') },
          { code: 'configs', text: $t('system.tenantConfig.manage') },
          { code: 'subscriptions', text: '订阅管理' },
          { code: 'viewMenus', text: $t('system.tenant.viewMenus') },
          { code: 'syncMenus', text: $t('system.tenant.syncMenus') },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.tenant.operation'),
      width: 500,
    },
  ];
}
