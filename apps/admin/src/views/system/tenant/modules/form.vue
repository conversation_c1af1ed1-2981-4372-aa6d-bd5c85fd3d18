<script lang="ts" setup>
import type { SystemTenantApi } from '#/api/system/tenant';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { CircleCheckBig, CircleX } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createTenant,
  testDatasourceConnection,
  updateTenant,
} from '#/api/system/tenant';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemTenantApi.SystemTenant>();
const testingConnection = ref(false);
const testConnectionResult = ref<null | { message: string; success: boolean }>(
  null,
);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  commonConfig: {
    colon: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    formItemClass: 'w-full mb-4',
  },
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    drawerApi.lock();
    (id.value ? updateTenant(id.value, values) : createTenant(values))
      .then(() => {
        emits('success');
        drawerApi.close();
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemTenantApi.SystemTenant>();
      formApi.resetForm();
      testConnectionResult.value = null;
      if (data) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues(data);
      } else {
        id.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? $t('ui.actionTitle.edit', [$t('system.tenant.name')])
    : $t('ui.actionTitle.create', [$t('system.tenant.name')]);
});

/**
 * 测试数据源连接
 */
async function handleTestConnection() {
  try {
    // 获取表单值
    const values = await formApi.getValues();
    const url = values.datasource.url;

    // 如果URL为空，显示警告
    if (!url) {
      message.warning($t('system.tenant.datasourceUrlRequired'));
      return;
    }

    testingConnection.value = true;
    testConnectionResult.value = null;

    const result = await testDatasourceConnection(url);
    testConnectionResult.value = result;

    if (result.success) {
      message.success($t('system.tenant.connectionSuccess'));
    } else {
      message.error(
        `${$t('system.tenant.connectionFailed')}: ${result.message}`,
      );
    }
  } catch (error) {
    console.error('测试连接失败:', error);
    testConnectionResult.value = {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
    };
    message.error($t('system.tenant.connectionFailed'));
  } finally {
    testingConnection.value = false;
  }
}
</script>
<template>
  <Drawer :title="getDrawerTitle">
    <Form>
      <!-- 测试连接按钮单独占一行 -->
      <div class="test-connection-section">
        <Button
          type="primary"
          :loading="testingConnection"
          @click="handleTestConnection"
          class="test-connection-btn"
        >
          {{ $t('system.tenant.testConnection') }}
        </Button>

        <div v-if="testConnectionResult" class="connection-result">
          <div v-if="testConnectionResult.success" class="text-success">
            <CircleCheckBig class="size-4" />
            {{ $t('system.tenant.connectionSuccess') }}
          </div>
          <div v-else class="text-danger">
            <CircleX class="size-4" />
            {{ $t('system.tenant.connectionFailed') }}:
            {{ testConnectionResult.message }}
          </div>
        </div>
      </div>
    </Form>
  </Drawer>
</template>

<style lang="scss" scoped>
.test-connection-section {
  padding-left: 24%;
}

.test-connection-btn {
  margin-bottom: 8px;
}

.connection-result {
  padding: 8px 12px;
  margin-top: 8px;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;

  .text-success,
  .text-danger {
    display: flex;
    gap: 6px;
    align-items: center;
  }
}

.text-success {
  color: #52c41a;
}

.text-danger {
  color: #ff4d4f;
}
</style>
