<script lang="ts" setup>
import type { FeatureMenuApi } from '#/api/system/feature-menu';
import type { TenantApi } from '#/api/system/tenant';

import { computed, h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Spin, Table, Tag } from 'ant-design-vue';

import { getTenantMenus, syncTenantMenus } from '#/api/system/feature-menu';
import { $t } from '#/locales';

const emits = defineEmits(['success']);

// 存储从外部传入的租户数据
const currentTenant = ref<null | TenantApi.Tenant>(null);

const menuData = ref<FeatureMenuApi.TenantMenuItem[]>([]);
const isLoading = ref(false);
const isSyncing = ref(false);

// 表格列定义
const columns = [
  {
    title: $t('system.menu.name'),
    dataIndex: 'menuName',
    key: 'menuName',
  },
  {
    title: $t('system.menu.path'),
    dataIndex: 'menuPath',
    key: 'menuPath',
  },
  {
    title: $t('system.featureCode.code'),
    dataIndex: 'featureCode',
    key: 'featureCode',
  },
  {
    title: '状态',
    dataIndex: 'enabled',
    key: 'enabled',
    customRender: ({ text }: { text: boolean }) => {
      return text
        ? h(Tag, { color: 'success' }, () => '启用')
        : h(Tag, { color: 'error' }, () => '禁用');
    },
  },
];

// 加载租户可用菜单
async function loadTenantMenus() {
  if (!currentTenant.value?.id) {
    return;
  }

  isLoading.value = true;
  try {
    const menus = await getTenantMenus(currentTenant.value.id);
    menuData.value = menus;
  } catch (error) {
    console.error('获取租户可用菜单失败:', error);
    message.error('获取租户可用菜单失败');
  } finally {
    isLoading.value = false;
  }
}

// 同步租户菜单
async function handleSyncMenus() {
  if (!currentTenant.value?.id) {
    return;
  }

  isSyncing.value = true;
  try {
    await syncTenantMenus(currentTenant.value.id);
    message.success($t('system.tenant.syncMenusSuccess'));
    await loadTenantMenus();
    emits('success');
  } catch (error) {
    console.error('同步租户菜单失败:', error);
    message.error('同步租户菜单失败');
  } finally {
    isSyncing.value = false;
  }
}

const [Modal, modalApi] = useVbenModal({
  title: computed(
    () =>
      `${$t('system.tenant.viewMenus')}: ${currentTenant.value?.name || ''}`,
  ),
  width: 800,
  onOpenChange(visible) {
    if (visible) {
      // 获取外部传入的数据
      const data = modalApi.getData<{ tenant: TenantApi.Tenant }>();

      if (data?.tenant) {
        currentTenant.value = data.tenant;
        loadTenantMenus();
      } else {
        console.error('未获取到租户数据');
      }
    }
  },
  footer: () => {
    return h('div', { class: 'flex justify-between' }, [
      h(
        Button,
        {
          type: 'primary',
          loading: isSyncing.value,
          onClick: handleSyncMenus,
        },
        () => $t('system.tenant.syncMenus'),
      ),
      h(
        Button,
        {
          onClick: () => modalApi.close(),
        },
        () => '关闭',
      ),
    ]);
  },
});

// 不需要在组件挂载时加载数据，因为我们已经在onOpenChange回调中加载数据了
</script>

<template>
  <Modal>
    <Spin :spinning="isLoading">
      <div class="mb-4">
        <div class="mb-2 font-medium">
          {{ $t('system.tenant.availableMenus') }}
        </div>
        <div class="mb-4 text-sm text-gray-500">
          {{ $t('system.tenant.availableMenusDesc') }}
        </div>

        <Table
          :columns="columns"
          :data-source="menuData"
          :pagination="{ pageSize: 10 }"
          :row-key="(record) => record.id"
        />
      </div>
    </Spin>
  </Modal>
</template>
