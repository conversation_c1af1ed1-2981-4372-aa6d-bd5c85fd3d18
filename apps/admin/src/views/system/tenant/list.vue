<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SystemTenantApi } from '#/api/system/tenant';

import { useRouter } from 'vue-router';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { syncTenantMenus } from '#/api/system/feature-menu';
import {
  deleteTenant,
  getTenantList,
  updateTenantStatus,
} from '#/api/system/tenant';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import MenuView from './modules/menu-view.vue';

const router = useRouter();
const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [MenuViewModal, menuViewModalApi] = useVbenModal({
  connectedComponent: MenuView,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          try {
            const result = await getTenantList({
              page: page.currentPage,
              pageSize: page.pageSize,
              ...formValues,
            });

            return result;
          } catch (error) {
            console.error('获取租户列表失败:', error);
            message.error('获取租户列表失败');
            return {
              items: [],
              total: 0,
              page: 1,
              pageSize: 10,
            };
          }
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    pagerConfig: {
      enabled: true,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<SystemTenantApi.SystemTenant>,
});

function onActionClick(e: OnActionClickParams<SystemTenantApi.SystemTenant>) {
  switch (e.code) {
    case 'configs': {
      onManageConfigs(e.row);
      break;
    }
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
    case 'features': {
      onManageFeatures(e.row);
      break;
    }
    case 'subscriptions': {
      onManageSubscriptions(e.row);
      break;
    }
    case 'syncMenus': {
      onSyncMenus(e.row);
      break;
    }
    case 'viewMenus': {
      onViewMenus(e.row);
      break;
    }
  }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

/**
 * 状态开关即将改变
 * @param newStatus 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onStatusChange(
  newStatus: number,
  row: SystemTenantApi.SystemTenant,
) {
  const status: Recordable<string> = {
    0: '禁用',
    1: '启用',
  };
  try {
    await confirm(
      `你要将${row.name}的状态切换为 【${status[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    await updateTenantStatus(row.id, newStatus as 0 | 1);
    return true;
  } catch {
    return false;
  }
}

function onEdit(row: SystemTenantApi.SystemTenant) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: SystemTenantApi.SystemTenant) {
  confirm(
    $t('ui.actionMessage.deleteConfirm', [row.name]),
    $t('ui.actionTitle.delete', [$t('system.tenant.name')]),
  )
    .then(() => {
      const _hideLoading = message.loading({
        content: $t('ui.actionMessage.deleting', [row.name]),
        duration: 0,
        key: 'action_process_msg',
      });
      deleteTenant(row.id)
        .then(() => {
          message.success({
            content: $t('ui.actionMessage.deleteSuccess', [row.name]),
            key: 'action_process_msg',
          });
          onRefresh();
        })
        .catch(() => {
          _hideLoading();
        });
    })
    .catch(() => {
      // 用户取消删除
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}

/**
 * 管理租户功能
 */
function onManageFeatures(row: SystemTenantApi.SystemTenant) {
  // 使用路由导航到租户功能管理页面，并传递租户ID
  router.push({
    path: '/system/tenant/features',
    query: { tenantId: row.id.toString() },
  });
}

/**
 * 管理租户配置
 */
function onManageConfigs(row: SystemTenantApi.SystemTenant) {
  // 使用路由导航到租户配置管理页面，并传递租户ID
  router.push({
    path: '/system/tenant/configs',
    query: { tenantId: row.id.toString() },
  });
}

/**
 * 管理租户订阅
 */
function onManageSubscriptions(row: SystemTenantApi.SystemTenant) {
  // 使用路由导航到租户订阅管理页面，并传递租户ID
  router.push({
    path: '/system/tenant/subscriptions',
    query: { tenantId: row.id.toString() },
  });
}

/**
 * 查看租户可用菜单
 */
function onViewMenus(row: SystemTenantApi.SystemTenant) {
  menuViewModalApi.setData({ tenant: row }).open();
}

/**
 * 同步租户菜单
 */
async function onSyncMenus(row: SystemTenantApi.SystemTenant) {
  try {
    await confirm(
      $t('system.tenant.syncMenusConfirm', [row.name]),
      $t('system.tenant.syncMenus'),
    );

    const hideLoading = message.loading({
      content: $t('system.tenant.syncingMenus', [row.name]),
      duration: 0,
      key: 'sync_menus_msg',
    });

    try {
      await syncTenantMenus(row.id);
      hideLoading();
      message.success({
        content: $t('system.tenant.syncMenusSuccess'),
        key: 'sync_menus_msg',
      });
    } catch (error) {
      console.error('同步租户菜单失败:', error);
      hideLoading();
      message.error({
        content: $t('system.tenant.syncMenusError'),
        key: 'sync_menus_msg',
      });
    }
  } catch {
    // 用户取消操作
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <MenuViewModal @success="onRefresh" />
    <Grid :table-title="$t('system.tenant.list')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('system.tenant.name')]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
