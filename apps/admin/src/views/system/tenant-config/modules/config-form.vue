<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '#/adapter/form';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getTenantConfig,
  testTenantConfig,
  updateTenantConfig,
} from '#/api/system/tenant-config';
import { $t } from '#/locales';

import { useEmailConfigFormSchema, useSmsConfigFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref({
  tenantId: undefined as number | undefined,
  category: '',
  categoryName: '',
});

const configData = ref<Recordable<any>>({});
const isLoading = ref(false);
const isTesting = ref(false);
const testResult = ref<null | { message: string; success: boolean }>(null);

// 根据类别获取表单配置
const formSchema = computed<VbenFormSchema[]>(() => {
  switch (formData.value.category) {
    case 'email': {
      return useEmailConfigFormSchema();
    }
    case 'sms': {
      return useSmsConfigFormSchema();
    }
    default: {
      return [];
    }
  }
});

const [Form, formApi] = useVbenForm({
  schema: formSchema,
  showDefaultActions: false,
});

const getDrawerTitle = computed(() => {
  return `${formData.value.categoryName} - ${$t('system.tenantConfig.name')}`;
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    drawerApi.lock();
    isLoading.value = true;

    try {
      await updateTenantConfig(
        formData.value.category,
        values,
        formData.value.tenantId,
      );

      message.success($t('ui.actionMessage.operationSuccess'));
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      drawerApi.unlock();
      isLoading.value = false;
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<{
        category: string;
        categoryName: string;
        tenantId: number;
      }>();

      if (data) {
        formData.value = data;
        testResult.value = null;

        // 加载配置数据
        await loadConfig();
      }
    }
  },
});

async function loadConfig() {
  isLoading.value = true;
  try {
    const config = await getTenantConfig(
      formData.value.category,
      formData.value.tenantId,
    );

    configData.value = config;
    formApi.setValues(config);
  } catch (error) {
    console.error('获取配置失败:', error);
    message.error('获取配置失败');
  } finally {
    isLoading.value = false;
  }
}

async function handleTestConfig() {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();

  isTesting.value = true;
  testResult.value = null;

  try {
    const result = await testTenantConfig(
      formData.value.category,
      values,
      formData.value.tenantId,
    );

    testResult.value = {
      success: result.success || false,
      message: result.message || '',
    };

    if (result.success) {
      message.success('测试成功');
    } else {
      message.error(`测试失败: ${result.message}`);
    }
  } catch (error) {
    console.error('测试配置失败:', error);
    testResult.value = {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
    };
    message.error('测试配置失败');
  } finally {
    isTesting.value = false;
  }
}
</script>

<template>
  <Drawer :title="getDrawerTitle" :loading="isLoading">
    <Form />

    <div class="mt-4 flex justify-between">
      <Button type="primary" :loading="isTesting" @click="handleTestConfig">
        {{ $t('system.tenantConfig.testConfig') }}
      </Button>
    </div>

    <div
      v-if="testResult"
      class="mt-4 rounded border p-3"
      :class="testResult.success ? 'border-green-500' : 'border-red-500'"
    >
      <div
        class="font-medium"
        :class="testResult.success ? 'text-green-500' : 'text-red-500'"
      >
        {{ testResult.success ? '测试成功' : '测试失败' }}
      </div>
      <div class="mt-1">{{ testResult.message }}</div>
    </div>
  </Drawer>
</template>
