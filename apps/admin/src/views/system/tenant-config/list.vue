<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { TenantConfigApi } from '#/api/system/tenant-config';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { message, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTenantList } from '#/api/system/tenant';
import { getConfigCategories } from '#/api/system/tenant-config';
import { $t } from '#/locales';

import { useColumns } from './data';
import ConfigForm from './modules/config-form.vue';

const [ConfigDrawer, configDrawerApi] = useVbenDrawer({
  connectedComponent: ConfigForm,
  destroyOnClose: true,
});

const route = useRoute();
const selectedTenantId = ref<number>();
const tenantOptions = ref<{ label: string; value: number }[]>([]);

/**
 * 表格操作按钮点击事件
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<TenantConfigApi.ConfigCategory>) {
  switch (code) {
    case 'config': {
      onConfig(row);
      break;
    }
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false, // 禁用分页
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          try {
            return await getConfigCategories();
          } catch (error) {
            console.error('获取配置类别列表失败:', error);
            message.error('获取配置类别列表失败');
            return [];
          }
        },
      },
    },
    rowConfig: {
      keyField: 'code',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
  } as VxeTableGridOptions<TenantConfigApi.ConfigCategory>,
});

function onConfig(row: TenantConfigApi.ConfigCategory) {
  if (!selectedTenantId.value) {
    message.warning('请先选择租户');
    return;
  }

  configDrawerApi
    .setData({
      tenantId: selectedTenantId.value,
      category: row.code,
      categoryName: row.name,
    })
    .open();
}

function onRefresh() {
  gridApi.query();
}

// 监听租户选择变化
watch(selectedTenantId, () => {
  onRefresh();
});

// 加载租户列表
onMounted(async () => {
  try {
    const result = await getTenantList({ pageSize: 100 });
    tenantOptions.value = result.items.map((item) => ({
      label: item.name,
      value: item.id,
    }));

    // 如果URL中有租户ID参数，则自动选择该租户
    const tenantIdFromQuery = route.query.tenantId;
    if (tenantIdFromQuery) {
      const tenantId = Number.parseInt(tenantIdFromQuery as string, 10);
      if (!Number.isNaN(tenantId)) {
        selectedTenantId.value = tenantId;
      }
    }
  } catch (error) {
    console.error('获取租户列表失败:', error);
    message.error('获取租户列表失败');
  }
});
</script>

<template>
  <Page auto-content-height>
    <ConfigDrawer @success="onRefresh" />

    <Grid :table-title="$t('system.tenantConfig.list')">
      <template #toolbar-tools>
        <div class="flex items-center gap-2">
          <span>{{ $t('system.tenantConfig.selectTenant') }}:</span>
          <Select
            v-model:value="selectedTenantId"
            :options="tenantOptions"
            :placeholder="$t('system.tenantConfig.selectTenant')"
            style="width: 200px"
          />
        </div>
      </template>
    </Grid>
  </Page>
</template>
