import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TenantConfigApi } from '#/api/system/tenant-config';

import { $t } from '#/locales';

/**
 * 配置类别表格列配置
 */
export function useColumns<T = TenantConfigApi.ConfigCategory>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: $t('system.tenantConfig.categoryName'),
      width: 200,
    },
    {
      field: 'code',
      title: $t('system.tenantConfig.categoryCode'),
      width: 150,
    },
    {
      field: 'description',
      title: $t('system.tenantConfig.description'),
      width: 300,
    },
    {
      field: 'requiredFeature',
      title: $t('system.tenantConfig.requiredFeature'),
      width: 200,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.tenantConfig.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'config',
            text: $t('system.tenantConfig.name'),
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.tenantConfig.operation'),
      width: 130,
    },
  ];
}

/**
 * 邮件配置表单配置
 */
export function useEmailConfigFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: 'SMTP', value: 'smtp' },
          { label: 'Mailgun', value: 'mailgun' },
          { label: 'SendGrid', value: 'sendgrid' },
        ],
      },
      fieldName: 'provider',
      label: $t('system.tenantConfig.email.provider'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'host',
      label: $t('system.tenantConfig.email.host'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      fieldName: 'port',
      label: $t('system.tenantConfig.email.port'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('system.tenantConfig.email.username'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'password',
      },
      fieldName: 'password',
      label: $t('system.tenantConfig.email.password'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'fromName',
      label: $t('system.tenantConfig.email.fromName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'fromEmail',
      label: $t('system.tenantConfig.email.fromEmail'),
      rules: 'required',
    },
    {
      component: 'Switch',
      fieldName: 'enableSSL',
      label: $t('system.tenantConfig.email.enableSSL'),
    },
  ];
}

/**
 * 短信配置表单配置
 */
export function useSmsConfigFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '阿里云', value: 'aliyun' },
          { label: '腾讯云', value: 'tencent' },
        ],
      },
      fieldName: 'provider',
      label: $t('system.tenantConfig.sms.provider'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'accessKey',
      label: $t('system.tenantConfig.sms.accessKey'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'password',
      },
      fieldName: 'secretKey',
      label: $t('system.tenantConfig.sms.secretKey'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'signName',
      label: $t('system.tenantConfig.sms.signName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'templateCode',
      label: $t('system.tenantConfig.sms.templateCode'),
      rules: 'required',
    },
  ];
}
