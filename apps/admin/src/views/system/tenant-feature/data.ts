import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TenantFeatureApi } from '#/api/system/tenant-feature';

import { $t } from '#/locales';

/**
 * 租户功能表格列配置
 */
export function useColumns<T = TenantFeatureApi.TenantFeature>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'featureCode',
      title: $t('system.tenantFeature.featureCode'),
      width: 200,
    },
    {
      cellRender: { name: 'CellTag' },
      field: 'enabled',
      title: $t('system.tenantFeature.enabled'),
      width: 100,
      formatter: ({ cellValue }) => {
        return cellValue ? 1 : 0;
      },
    },
    {
      field: 'expiresAt',
      title: $t('system.tenantFeature.expiresAt'),
      width: 180,
      formatter: ({ cellValue }) => {
        return cellValue || $t('system.tenantFeature.noExpiration');
      },
    },
    {
      field: 'quota',
      title: $t('system.tenantFeature.quota'),
      width: 120,
      formatter: ({ cellValue }) => {
        return cellValue === null
          ? $t('system.tenantFeature.unlimited')
          : cellValue;
      },
    },
    {
      field: 'usedQuota',
      title: $t('system.tenantFeature.usedQuota'),
      width: 120,
    },
    {
      field: 'remaining',
      title: $t('system.tenantFeature.remaining'),
      width: 120,
      formatter: ({ row }) => {
        if (row.quota === null) return $t('system.tenantFeature.unlimited');
        return row.quota - row.usedQuota;
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'featureCode',
          nameTitle: $t('system.tenantFeature.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'enable',
            text: $t('system.tenantFeature.enable'),
            disabled: (row: TenantFeatureApi.TenantFeature) => row.enabled,
          },
          {
            code: 'disable',
            text: $t('system.tenantFeature.disable'),
            disabled: (row: TenantFeatureApi.TenantFeature) => !row.enabled,
          },
          {
            code: 'config',
            text: $t('system.tenantFeature.config'),
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.tenantFeature.operation'),
      width: 180,
    },
  ];
}

/**
 * 启用功能表单配置
 */
export function useEnableFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'config.expiresAt',
      label: $t('system.tenantFeature.expiresAt'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: $t('system.tenantFeature.unlimited'),
      },
      fieldName: 'config.quota',
      label: $t('system.tenantFeature.quota'),
    },
  ];
}

/**
 * 应用模板表单配置
 */
export function useApplyTemplateFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        placeholder: $t('system.tenantFeature.selectTemplate'),
      },
      fieldName: 'templateCode',
      label: $t('system.tenantFeature.selectTemplate'),
      rules: 'required',
    },
  ];
}
