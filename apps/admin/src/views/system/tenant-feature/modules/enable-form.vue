<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { enableTenantFeature } from '#/api/system/tenant-feature';
import { $t } from '#/locales';

import { useEnableFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref({
  tenantId: undefined as number | undefined,
  featureCode: '',
  config: {
    expiresAt: null as null | string,
    quota: null as null | number,
  },
});

const [Form, formApi] = useVbenForm({
  schema: useEnableFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  title: $t('system.tenantFeature.enable'),
  async onConfirm() {
    const values = await formApi.getValues();

    modalApi.lock();
    try {
      await enableTenantFeature(
        formData.value.tenantId!,
        formData.value.featureCode,
        values.config,
      );

      message.success('功能已启用');
      modalApi.close();
      emits('success');
    } catch (error) {
      console.error('启用功能失败:', error);
      message.error('启用功能失败');
    } finally {
      modalApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{
        config?: {
          expiresAt?: null | string;
          quota?: null | number;
        };
        featureCode: string;
        tenantId: number;
      }>();

      if (data) {
        formData.value = {
          tenantId: data.tenantId,
          featureCode: data.featureCode,
          config: {
            expiresAt: data.config?.expiresAt || null,
            quota: data.config?.quota || null,
          },
        };

        formApi.setValues({
          config: {
            expiresAt: data.config?.expiresAt || null,
            quota: data.config?.quota || null,
          },
        });
      }
    }
  },
});
</script>

<template>
  <Modal>
    <div class="mb-4">
      <div class="mb-2 font-medium">
        {{ $t('system.tenantFeature.featureCode') }}:
      </div>
      <div>{{ formData.featureCode }}</div>
    </div>

    <Form />
  </Modal>
</template>
