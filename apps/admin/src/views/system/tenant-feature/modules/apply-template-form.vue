<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getFeatureTemplateList } from '#/api/system/feature-template';
import { applyFeatureTemplate } from '#/api/system/tenant-feature';
import { $t } from '#/locales';

import { useApplyTemplateFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref({
  tenantId: undefined as number | undefined,
  templateCode: '',
});

const templateOptions = ref<{ label: string; value: string }[]>([]);
const isLoading = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useApplyTemplateFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  title: $t('system.tenantFeature.applyTemplate'),
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    modalApi.lock();
    try {
      await applyFeatureTemplate(formData.value.tenantId!, values.templateCode);

      message.success('模板已应用');
      modalApi.close();
      emits('success');
    } catch (error) {
      console.error('应用模板失败:', error);
      message.error('应用模板失败');
    } finally {
      modalApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{
        tenantId: number;
      }>();

      if (data) {
        formData.value = {
          tenantId: data.tenantId,
          templateCode: '',
        };

        formApi.resetForm();
        loadTemplates();
      }
    }
  },
});

async function loadTemplates() {
  isLoading.value = true;
  try {
    const templates = await getFeatureTemplateList();
    templateOptions.value = templates.items
      .filter((template) => template.isActive)
      .map((template) => ({
        label: template.name,
        value: template.code,
      }));

    // 更新表单组件的选项
    const schema = useApplyTemplateFormSchema();
    schema[0].componentProps = {
      ...schema[0].componentProps,
      options: templateOptions.value,
    };
    formApi.updateSchema(schema);
  } catch (error) {
    console.error('获取功能模板列表失败:', error);
    message.error('获取功能模板列表失败');
  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <Modal :loading="isLoading">
    <Form />
  </Modal>
</template>
