<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { TenantFeatureApi } from '#/api/system/tenant-feature';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTenantList } from '#/api/system/tenant';
import {
  disableTenantFeature,
  getTenantFeatureList,
} from '#/api/system/tenant-feature';
import { $t } from '#/locales';

import { useColumns } from './data';
import ApplyTemplateForm from './modules/apply-template-form.vue';
import EnableFeatureForm from './modules/enable-form.vue';

const [EnableModal, enableModalApi] = useVbenModal({
  connectedComponent: EnableFeatureForm,
  destroyOnClose: true,
});

const [ApplyTemplateModal, applyTemplateModalApi] = useVbenModal({
  connectedComponent: ApplyTemplateForm,
  destroyOnClose: true,
});

const route = useRoute();
const selectedTenantId = ref<number>();
const tenantOptions = ref<{ label: string; value: number }[]>([]);

/**
 * 表格操作按钮点击事件
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<TenantFeatureApi.TenantFeature>) {
  switch (code) {
    case 'config': {
      onConfig(row);
      break;
    }
    case 'disable': {
      onDisable(row);
      break;
    }
    case 'enable': {
      onEnable(row);
      break;
    }
  }
}

/**
 * 确认对话框
 */
function confirm(content: string, title: string) {
  return new Promise<void>((resolve, reject) => {
    Modal.confirm({
      title,
      content,
      onOk: () => resolve(),
      onCancel: () => reject(new Error('用户取消操作')),
    });
  });
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async () => {
          if (!selectedTenantId.value) {
            return [];
          }

          try {
            return await getTenantFeatureList(selectedTenantId.value);
          } catch (error) {
            console.error('获取租户功能列表失败:', error);
            message.error('获取租户功能列表失败');
            return [];
          }
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
  } as VxeTableGridOptions<TenantFeatureApi.TenantFeature>,
});

function onEnable(row: TenantFeatureApi.TenantFeature) {
  enableModalApi
    .setData({
      tenantId: selectedTenantId.value,
      featureCode: row.featureCode,
    })
    .open();
}

function onDisable(row: TenantFeatureApi.TenantFeature) {
  confirm(
    `确定要禁用功能 "${row.featureCode}" 吗？`,
    $t('system.tenantFeature.disable'),
  )
    .then(async () => {
      try {
        await disableTenantFeature(selectedTenantId.value!, row.featureCode);
        message.success('功能已禁用');
        onRefresh();
      } catch (error) {
        console.error('禁用功能失败:', error);
        message.error('禁用功能失败');
      }
    })
    .catch(() => {
      // 用户取消操作
    });
}

function onConfig(row: TenantFeatureApi.TenantFeature) {
  enableModalApi
    .setData({
      tenantId: selectedTenantId.value,
      featureCode: row.featureCode,
      config: {
        expiresAt: row.expiresAt,
        quota: row.quota,
      },
    })
    .open();
}

function onRefresh() {
  gridApi.query();
}

function onApplyTemplate() {
  if (!selectedTenantId.value) {
    message.warning('请先选择租户');
    return;
  }

  applyTemplateModalApi
    .setData({
      tenantId: selectedTenantId.value,
    })
    .open();
}

// 加载租户列表
onMounted(async () => {
  try {
    const result = await getTenantList({ pageSize: 100 });
    tenantOptions.value = result.items.map((item) => ({
      label: item.name,
      value: item.id,
    }));

    // 如果URL中有租户ID参数，则自动选择该租户
    const tenantIdFromQuery = route.query.tenantId;
    if (tenantIdFromQuery) {
      const tenantId = Number.parseInt(tenantIdFromQuery as string, 10);
      if (!Number.isNaN(tenantId)) {
        selectedTenantId.value = tenantId;
      }
    }
  } catch (error) {
    console.error('获取租户列表失败:', error);
    message.error('获取租户列表失败');
  }
});

// 监听租户选择变化
watch(selectedTenantId, () => {
  onRefresh();
});
</script>

<template>
  <Page auto-content-height>
    <EnableModal @success="onRefresh" />
    <ApplyTemplateModal @success="onRefresh" />

    <Grid :table-title="$t('system.tenantFeature.list')">
      <template #toolbar-tools>
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <span>{{ $t('system.tenantFeature.selectTenant') }}:</span>
            <Select
              v-model:value="selectedTenantId"
              :options="tenantOptions"
              :placeholder="$t('system.tenantFeature.selectTenant')"
              style="width: 200px"
            />
          </div>

          <Button
            type="primary"
            :disabled="!selectedTenantId"
            @click="onApplyTemplate"
          >
            <Plus class="size-5" />
            {{ $t('system.tenantFeature.applyTemplate') }}
          </Button>
        </div>
      </template>
    </Grid>
  </Page>
</template>
