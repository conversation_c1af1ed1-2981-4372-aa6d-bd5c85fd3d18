<script lang="ts" setup>
import type { FeatureTemplateApi } from '#/api/system/feature-template';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Spin, Tag } from 'ant-design-vue';

import { getFeatureTemplateMenus } from '#/api/system/feature-menu';
import { $t } from '#/locales';

// 存储从外部传入的功能模板数据
const currentTemplate = ref<FeatureTemplateApi.FeatureTemplate | null>(null);

const menuTreeData = ref<any[]>([]);
const expandedKeys = ref<any[]>([]);
const isLoading = ref(false);

// 原始菜单数据（用于调试）
const rawMenuData = ref<any>(null);

// 解析后的菜单数据（用于调试）
const parsedMenuData = ref<any>(null);

// 控制菜单展开状态
const expandedMenus = ref<number[]>([]);

// 切换菜单展开状态
function toggleMenu(index: number) {
  const position = expandedMenus.value.indexOf(index);
  if (position === -1) {
    expandedMenus.value.push(index);
  } else {
    expandedMenus.value.splice(position, 1);
  }
}

// 获取菜单的中文标题
function getMenuTitle(menu: any): string {
  if (!menu) return '未知菜单';

  // 尝试从meta中获取中文标题
  if (menu.meta) {
    try {
      // meta可能是JSON字符串
      if (typeof menu.meta === 'string') {
        const metaObj = JSON.parse(menu.meta);
        if (metaObj && metaObj.title) {
          return metaObj.title;
        }
      }
      // meta也可能已经是对象
      else if (typeof menu.meta === 'object' && menu.meta.title) {
        return menu.meta.title;
      }
    } catch (error) {
      console.error('解析菜单meta失败:', error);
    }
  }

  // 如果没有meta或解析失败，返回name
  return menu.name || '未命名菜单';
}

// 加载功能模板关联的菜单
async function loadTemplateMenus() {
  if (!currentTemplate.value?.code) {
    console.error('没有功能模板代码，无法加载关联菜单');
    return;
  }

  isLoading.value = true;

  try {
    // 获取菜单数据
    const menus = await getFeatureTemplateMenus(currentTemplate.value.code);

    // 保存原始数据用于显示
    rawMenuData.value = menus;

    // 默认展开所有父菜单
    if (Array.isArray(menus) && menus.length > 0) {
      // 设置所有父菜单为展开状态
      expandedMenus.value = menus.map((_, index) => index);
    }

    // 处理API返回的数据（用于树形结构显示，目前未使用）
    if (Array.isArray(menus) && menus.length > 0) {
      // 如果是非空数组，直接转换为树形结构
      menuTreeData.value = processMenuTree(menus);

      // 设置展开的节点
      expandedKeys.value = getExpandedKeys(menuTreeData.value);
    } else if (typeof menus === 'string') {
      // 如果是字符串，尝试解析
      try {
        const parsedData = JSON.parse(menus);

        // 如果解析成功，记录解析后的数据
        parsedMenuData.value = parsedData;

        if (Array.isArray(parsedData) && parsedData.length > 0) {
          // 设置所有父菜单为展开状态
          expandedMenus.value = parsedData.map((_, index) => index);
          rawMenuData.value = parsedData;
        }
      } catch (error) {
        console.error('解析菜单字符串失败:', error);
      }
    }
  } catch (error) {
    console.error('获取功能模板关联菜单失败:', error);
    message.error('获取功能模板关联菜单失败');
  } finally {
    isLoading.value = false;
  }
}

// 递归获取所有节点的key，用于默认展开
function getExpandedKeys(nodes: any[]) {
  let keys: any[] = [];
  if (!Array.isArray(nodes)) return keys;

  nodes.forEach((node) => {
    if (node.key) {
      keys.push(node.key);
    }
    if (node.children && node.children.length > 0) {
      keys = [...keys, ...getExpandedKeys(node.children)];
    }
  });

  return keys;
}

// 递归处理菜单树
function processMenuTree(menus: any[]): any[] {
  return menus.map((menu) => createMenuNode(menu));
}

// 创建菜单节点
function createMenuNode(menu: any) {
  // 确保menu是对象
  if (!menu || typeof menu !== 'object') {
    return {
      key: 'invalid-menu',
      title: String(menu),
    };
  }

  const result = {
    ...menu,
    key: menu.id || `menu-${Math.random().toString(36).slice(2, 11)}`,
    title: renderMenuTitle(menu),
    // 传递图标信息
    icon: menu.meta?.icon,
  };

  // 如果有子菜单，递归处理
  if (
    menu.children &&
    Array.isArray(menu.children) &&
    menu.children.length > 0
  ) {
    result.children = processMenuTree(menu.children);
  }

  return result;
}

// 渲染菜单标题，包括功能代码标签
function renderMenuTitle(menu: any) {
  if (!menu) return { title: '未知菜单', featureCodes: [] };

  // 处理不同格式的菜单数据
  let title = '';
  let featureCodes: string[] = [];

  // 如果是字符串，直接使用
  if (typeof menu === 'string') {
    title = menu;
  }
  // 如果是对象，尝试获取标题
  else if (typeof menu === 'object') {
    // 尝试从不同属性获取标题
    if (menu.title) {
      title = menu.title;
    } else if (menu.meta?.title) {
      title = menu.meta.title;
    } else if (menu.name) {
      title = menu.name;
    } else if (menu.menuName) {
      title = menu.menuName;
    } else {
      // 如果没有找到标题，使用JSON字符串
      title = JSON.stringify(menu);
    }

    // 尝试获取功能代码
    if (Array.isArray(menu.featureCodes)) {
      featureCodes = menu.featureCodes;
    } else if (typeof menu.featureCode === 'string') {
      featureCodes = [menu.featureCode];
    } else if (menu.featureCodes && typeof menu.featureCodes === 'string') {
      // 如果featureCodes是字符串，尝试解析为数组
      try {
        const parsed = JSON.parse(menu.featureCodes);
        if (Array.isArray(parsed)) {
          featureCodes = parsed;
        } else if (typeof parsed === 'string') {
          featureCodes = [parsed];
        }
      } catch {
        // 如果解析失败，将其作为单个功能代码
        featureCodes = [menu.featureCodes];
      }
    }
  }

  // 创建带有功能代码标签的标题
  return {
    title: title || '未命名菜单',
    featureCodes,
  };
}

const [Modal, modalApi] = useVbenModal({
  title: computed(
    () =>
      `${$t('system.featureTemplate.viewMenus')}: ${currentTemplate.value?.name || ''}`,
  ),
  width: 700,
  onOpenChange(visible) {
    if (visible) {
      // 获取外部传入的数据
      const data = modalApi.getData<{
        template: FeatureTemplateApi.FeatureTemplate;
      }>();

      if (data?.template) {
        currentTemplate.value = data.template;
        loadTemplateMenus();
      } else {
        console.error('未获取到功能模板数据');
      }
    }
  },
  footer: false, // 不显示底部按钮
});

// 不需要在组件挂载时加载数据，因为我们已经在onOpenChange回调中加载数据了
</script>

<template>
  <Modal>
    <Spin :spinning="isLoading">
      <div class="mb-4">
        <div class="mb-2 font-medium">
          {{ $t('system.featureTemplate.associatedMenus') }}
        </div>
        <div class="mb-4 text-sm text-gray-500">
          {{ $t('system.featureTemplate.associatedMenusDesc') }}
        </div>

        <!-- 自定义菜单显示 -->
        <div
          v-if="rawMenuData && rawMenuData.length > 0"
          class="rounded border bg-black p-4"
        >
          <div v-for="(menu, index) in rawMenuData" :key="index" class="mb-3">
            <!-- 父菜单项 -->
            <div class="mb-2 flex items-center">
              <span class="mr-2 cursor-pointer" @click="toggleMenu(index)">
                <i v-if="menu.children && menu.children.length > 0">
                  {{ expandedMenus.includes(index) ? '▼' : '▶' }}
                </i>
              </span>
              <span class="text-lg font-medium">{{ getMenuTitle(menu) }}</span>
              <div
                v-if="menu.featureCodes && menu.featureCodes.length > 0"
                class="ml-2 flex flex-wrap"
              >
                <Tag
                  v-for="code in menu.featureCodes"
                  :key="code"
                  color="blue"
                  class="mb-1 ml-1"
                >
                  {{ code }}
                </Tag>
              </div>
            </div>

            <!-- 子菜单项 -->
            <div
              v-if="
                expandedMenus.includes(index) &&
                menu.children &&
                menu.children.length > 0
              "
              class="ml-6 border-l-2 border-gray-700 pl-4"
            >
              <div
                v-for="(child, childIndex) in menu.children"
                :key="`${index}-${childIndex}`"
                class="mb-2 mt-2"
              >
                <div class="flex items-center">
                  <span class="font-medium">{{ getMenuTitle(child) }}</span>
                  <div
                    v-if="child.featureCodes && child.featureCodes.length > 0"
                    class="ml-2 flex flex-wrap"
                  >
                    <Tag
                      v-for="code in child.featureCodes"
                      :key="code"
                      color="blue"
                      class="mb-1 ml-1"
                    >
                      {{ code }}
                    </Tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 如果没有菜单数据，显示提示信息 -->
        <div v-else class="rounded border p-4 py-4 text-center text-gray-500">
          {{ $t('system.featureTemplate.noAssociatedMenus') }}
        </div>
      </div>
    </Spin>
  </Modal>
</template>
