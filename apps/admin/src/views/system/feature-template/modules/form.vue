<script lang="ts" setup>
import type { FeatureTemplateApi } from '#/api/system/feature-template';

import { computed, onMounted, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { Plus, X } from '@vben/icons';

import {
  Button,
  Divider,
  Input,
  message,
  Modal,
  Select,
  Space,
  Switch,
  Tag,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getFeatureCodeList } from '#/api/system/feature-code';
import {
  getFeatureTemplateDetail,
  upsertFeatureTemplate,
} from '#/api/system/feature-template';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<FeatureTemplateApi.FeatureTemplate>();
const features = ref<Record<string, FeatureTemplateApi.FeatureConfig>>({});
const newFeatureCode = ref('');
const isLoading = ref(false);
const featureCodeOptions = ref<{ label: string; value: string }[]>([]);
const featureCodeLoading = ref(false);

// 元数据编辑相关
const currentEditingCode = ref<string>('');
const metadataModalVisible = ref<boolean>(false);
const metadataValue = ref<string>('');

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  commonConfig: {
    colon: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    formItemClass: 'w-full mb-4',
  },
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    drawerApi.lock();
    isLoading.value = true;

    try {
      const values = await formApi.getValues();
      const templateData = {
        code: values.code,
        name: values.name,
        isActive: values.isActive,
        features: features.value,
      };

      await upsertFeatureTemplate(templateData);
      message.success($t('ui.actionMessage.operationSuccess'));
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('保存功能模板失败:', error);
      message.error('保存功能模板失败');
    } finally {
      drawerApi.unlock();
      isLoading.value = false;
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<FeatureTemplateApi.FeatureTemplate>();
      formApi.resetForm();
      features.value = {};

      // 加载功能代码列表
      loadFeatureCodes();

      if (data?.id) {
        id.value = data.id;
        formData.value = data;
        isLoading.value = true;

        getFeatureTemplateDetail(data.code)
          .then((detail) => {
            formApi.setValues(detail);
            features.value = detail.features || {};
            // 加载完模板详情后重新加载功能代码列表，过滤掉已添加的
            loadFeatureCodes();
          })
          .catch((error) => {
            console.error('获取功能模板详情失败:', error);
            message.error('获取功能模板详情失败');
          })
          .finally(() => {
            isLoading.value = false;
          });
      } else {
        id.value = undefined;
        formData.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? $t('system.featureTemplate.editTemplate')
    : $t('system.featureTemplate.createTemplate');
});

function addFeature() {
  if (!newFeatureCode.value) {
    message.warning('请选择功能代码');
    return;
  }

  if (features.value[newFeatureCode.value]) {
    message.warning('该功能代码已存在');
    return;
  }

  features.value[newFeatureCode.value] = {
    enabled: true,
    quota: null,
  };

  // 添加后重新加载功能代码列表，过滤掉已添加的
  loadFeatureCodes();
  newFeatureCode.value = '';
}

function removeFeature(code: string) {
  delete features.value[code];
  // 移除后重新加载功能代码列表
  loadFeatureCodes();
}

// 打开元数据编辑器
function openMetadataEditor(code: string) {
  currentEditingCode.value = code;
  // 获取当前功能的元数据
  const metadata = features.value[code]?.metadata || {};
  // 将对象转换为格式化的JSON字符串
  metadataValue.value = JSON.stringify(metadata, null, 2);
  metadataModalVisible.value = true;
}

// 保存元数据
function saveMetadata() {
  try {
    if (!metadataValue.value.trim()) {
      // 如果为空，设置为空对象
      if (features.value[currentEditingCode.value]) {
        features.value[currentEditingCode.value].metadata = {};
      }
      metadataModalVisible.value = false;
      return;
    }

    // 解析JSON字符串
    const metadata = JSON.parse(metadataValue.value);

    // 更新功能的元数据
    if (features.value[currentEditingCode.value]) {
      features.value[currentEditingCode.value].metadata = metadata;
    }

    message.success($t('ui.actionMessage.operationSuccess'));
    metadataModalVisible.value = false;
  } catch {
    message.error('JSON格式无效，请检查');
  }
}

function toggleFeatureEnabled(code: string) {
  if (features.value[code]) {
    features.value[code].enabled = !features.value[code].enabled;
  }
}

function updateFeatureQuota(code: string, quota: null | number) {
  if (features.value[code]) {
    features.value[code].quota = quota;
  }
}

// 加载功能代码列表
async function loadFeatureCodes() {
  featureCodeLoading.value = true;
  try {
    const data = await getFeatureCodeList({ includeInactive: false });
    // 过滤掉已经添加的功能代码
    const existingCodes = Object.keys(features.value);
    const filteredData = data.filter(
      (item) => !existingCodes.includes(item.code),
    );

    featureCodeOptions.value = filteredData.map((item) => ({
      label: `${item.name} (${item.code})`,
      value: item.code,
    }));
  } catch (error) {
    console.error('获取功能代码列表失败:', error);
    message.error('获取功能代码列表失败');
  } finally {
    featureCodeLoading.value = false;
  }
}

// 组件挂载时加载功能代码列表
onMounted(() => {
  loadFeatureCodes();
});
</script>

<template>
  <Drawer :title="getDrawerTitle" :loading="isLoading">
    <Form>
      <Divider>{{ $t('system.featureTemplate.featureSettings') }}</Divider>

      <div class="mb-4">
        <Space>
          <Select
            v-model:value="newFeatureCode"
            :loading="featureCodeLoading"
            :options="featureCodeOptions"
            :placeholder="$t('system.featureTemplate.featureCode')"
            show-search
            style="width: 300px"
            @dropdown-visible-change="
              (visible) => visible && loadFeatureCodes()
            "
          />
          <Button type="primary" @click="addFeature">
            <Plus class="size-4" />
            {{ $t('system.featureTemplate.addFeature') }}
          </Button>
        </Space>
      </div>

      <div v-if="Object.keys(features).length === 0" class="mb-4 text-gray-500">
        暂无功能，请添加功能
      </div>

      <div
        v-for="(featureConfig, code) in features"
        :key="code"
        class="mb-4 rounded border p-3"
      >
        <div class="flex items-center justify-between">
          <div class="font-medium">{{ code }}</div>
          <Button danger type="link" @click="removeFeature(code)">
            <X class="size-4" />
            {{ $t('system.featureTemplate.removeFeature') }}
          </Button>
        </div>

        <div class="mt-2 flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span>{{ $t('system.featureTemplate.featureEnabled') }}:</span>
            <Switch
              v-if="featureConfig"
              :checked="featureConfig.enabled"
              @change="() => toggleFeatureEnabled(code)"
            />
            <Tag
              v-if="featureConfig"
              :color="featureConfig.enabled ? 'success' : 'error'"
            >
              {{
                featureConfig.enabled
                  ? $t('common.enabled')
                  : $t('common.disabled')
              }}
            </Tag>
          </div>

          <div class="flex items-center gap-2">
            <span>{{ $t('system.featureTemplate.featureQuota') }}:</span>
            <Input
              v-if="featureConfig"
              :value="
                featureConfig.quota === null ? undefined : featureConfig.quota
              "
              type="number"
              style="width: 100px"
              :placeholder="$t('system.featureTemplate.unlimitedQuota')"
              @change="
                (e) =>
                  updateFeatureQuota(
                    code,
                    e.target.value ? Number(e.target.value) : null,
                  )
              "
            />
          </div>

          <div class="mt-2 w-full">
            <div class="mb-1">{{ $t('system.featureCode.metadata') }}:</div>
            <Button
              v-if="featureConfig"
              size="small"
              @click="openMetadataEditor(code)"
            >
              编辑元数据
            </Button>
          </div>
        </div>
      </div>
    </Form>
  </Drawer>

  <!-- 元数据编辑模态框 -->
  <Modal
    v-model:open="metadataModalVisible"
    :title="$t('system.featureCode.metadata')"
    @ok="saveMetadata"
  >
    <div>
      <Input.TextArea
        v-model:value="metadataValue"
        placeholder="{}"
        :rows="10"
      />
      <div class="mt-2 text-gray-500">
        请输入JSON格式的元数据，例如：{"key": "value"}
      </div>
    </div>
  </Modal>
</template>
