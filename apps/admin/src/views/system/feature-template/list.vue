<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FeatureTemplateApi } from '#/api/system/feature-template';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteFeatureTemplate,
  getFeatureTemplateList,
} from '#/api/system/feature-template';
import { $t } from '#/locales';

import { useColumns } from './data';
import Form from './modules/form.vue';
import MenuView from './modules/menu-view.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [MenuViewModal, menuViewModalApi] = useVbenModal({
  connectedComponent: MenuView,
  destroyOnClose: true,
});

/**
 * 表格操作按钮点击事件
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<FeatureTemplateApi.FeatureTemplate>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'viewMenus': {
      onViewMenus(row);
      break;
    }
  }
}

/**
 * 确认对话框
 */
function confirm(content: string, title: string) {
  return new Promise<void>((resolve, reject) => {
    Modal.confirm({
      title,
      content,
      onOk: () => resolve(),
      onCancel: () => reject(new Error('用户取消操作')),
    });
  });
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async () => {
          try {
            const data = await getFeatureTemplateList();

            // 确保数据是数组
            if (Array.isArray(data)) {
              return { items: data };
            } else if (data && typeof data === 'object') {
              // 如果是对象，尝试获取items属性
              const anyData = data as any;
              if (Array.isArray(anyData.items)) {
                return anyData;
              } else {
                // 如果没有items属性，将对象转换为数组
                const dataArray = Object.values(data).filter(
                  (item) => typeof item === 'object',
                );
                return { items: dataArray };
              }
            }

            // 默认返回空数组
            return { items: [] };
          } catch (error) {
            console.error('获取功能模板列表失败:', error);
            message.error('获取功能模板列表失败');
            return { items: [] };
          }
        },
      },
      props: {
        result: 'items',
        total: 'total',
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
  } as VxeTableGridOptions<FeatureTemplateApi.FeatureTemplate>,
});

function onEdit(row: FeatureTemplateApi.FeatureTemplate) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: FeatureTemplateApi.FeatureTemplate) {
  confirm(
    $t('ui.actionMessage.deleteConfirm', [row.name]),
    $t('ui.actionTitle.delete', [$t('system.featureTemplate.name')]),
  )
    .then(() => {
      const hideLoading = message.loading({
        content: $t('ui.actionMessage.deleting', [row.name]),
        duration: 0,
        key: 'action_process_msg',
      });
      deleteFeatureTemplate(row.code)
        .then(() => {
          message.success({
            content: $t('ui.actionMessage.deleteSuccess', [row.name]),
            key: 'action_process_msg',
          });
          onRefresh();
        })
        .catch(() => {
          hideLoading();
        });
    })
    .catch(() => {
      // 用户取消删除
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}

function onViewMenus(row: FeatureTemplateApi.FeatureTemplate) {
  menuViewModalApi.setData({ template: row }).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <MenuViewModal />
    <Grid :table-title="$t('system.featureTemplate.list')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('system.featureTemplate.name')]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
