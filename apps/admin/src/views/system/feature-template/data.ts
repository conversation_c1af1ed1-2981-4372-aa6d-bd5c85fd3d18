import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FeatureTemplateApi } from '#/api/system/feature-template';

import { $t } from '#/locales';

/**
 * 功能模板表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'code',
      label: $t('system.featureTemplate.templateCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.featureTemplate.templateName'),
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: true },
          { label: $t('common.disabled'), value: false },
        ],
        optionType: 'button',
      },
      defaultValue: true,
      fieldName: 'isActive',
      label: $t('system.featureTemplate.isActive'),
    },
  ];
}

/**
 * 功能模板列表表格列配置
 */
export function useColumns<T = FeatureTemplateApi.FeatureTemplate>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: $t('system.featureTemplate.templateName'),
      width: 200,
    },
    {
      field: 'code',
      title: $t('system.featureTemplate.templateCode'),
      width: 150,
    },
    {
      field: 'features',
      title: $t('system.featureTemplate.features'),
      width: 300,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        return Object.keys(cellValue).join(', ');
      },
    },
    {
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: $t('common.enabled'), value: true },
          { color: 'error', label: $t('common.disabled'), value: false },
        ],
      },
      field: 'isActive',
      title: $t('system.featureTemplate.isActive'),
      width: 100,
    },
    {
      field: 'createdAt',
      title: $t('system.featureTemplate.createdAt'),
      width: 180,
      formatter: ({ row }) => {
        return row.createTime || row.createdAt;
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('system.featureTemplate.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          { code: 'edit', text: '编辑' },
          { code: 'viewMenus', text: '查看关联菜单' },
          { code: 'delete', text: '删除' },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.featureTemplate.operation'),
      width: 250,
    },
  ];
}
