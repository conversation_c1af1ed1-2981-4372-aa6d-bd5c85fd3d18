<script setup lang="ts">
import type { WebsiteSchema } from '../schema/page-schema';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

const emits = defineEmits(['success']);

const formData = ref<WebsiteSchema>();

function useFormSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '网站名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'domain',
      label: '域名',
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: '网站描述',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'color',
        style: { width: '60px', height: '32px' },
      },
      fieldName: 'primaryColor',
      label: '主题色',
      defaultValue: '#1890ff',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
  ];
}

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();

    drawerApi.lock();

    // 模拟API调用
    setTimeout(() => {
      const newWebsite: WebsiteSchema = {
        id: id.value || `website-${Date.now()}`,
        name: values.name,
        domain: values.domain,
        status: values.status,
        createTime: new Date().toLocaleString(),
        globalSettings: {
          theme: { mode: 'light', primaryColor: values.primaryColor },
          fonts: ['Inter', 'PingFang SC'],
          colors: {
            primary: values.primaryColor,
            secondary: '#722ed1',
            success: '#52c41a',
            warning: '#faad14',
            error: '#f5222d',
          },
          spacing: { xs: '4px', sm: '8px', md: '16px', lg: '24px', xl: '32px' },
          breakpoints: { mobile: 768, tablet: 1024, desktop: 1200 },
        },
        navigation: { header: [], footer: [] },
        pages: [],
      };

      emits('success', newWebsite);
      drawerApi.close();
    }, 1000);
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<WebsiteSchema>();
      formApi.resetForm();
      if (data) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues({
          name: data.name,
          domain: data.domain,
          description: '',
          primaryColor: data.globalSettings.colors.primary,
          status: data.status,
        });
      } else {
        id.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id ? '编辑网站' : '新增网站';
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
