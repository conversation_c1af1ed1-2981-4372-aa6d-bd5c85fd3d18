<script setup lang="ts">
import type { ComponentSchema, PageSchema } from '../schema/page-schema';

import { computed, ref } from 'vue';

import { Icon } from '@iconify/vue';

import { WEBSITE_COMPONENTS } from '../../../components/website-components';

// 定义Props
interface Props {
  pageSchema: PageSchema;
}

const props = defineProps<Props>();

// 响应式数据
const previewMode = ref<'desktop' | 'mobile' | 'tablet'>('desktop');
const loading = ref(false);

// 计算属性
const previewStyles = computed(() => {
  const styles: Record<string, string> = {
    desktop: 'width: 100%; max-width: none;',
    tablet: 'width: 768px; max-width: 768px;',
    mobile: 'width: 375px; max-width: 375px;',
  };
  return styles[previewMode.value];
});

const pageTitle = computed(() => props.pageSchema.title || '未命名页面');
const pageDescription = computed(() => props.pageSchema.description || '');

// 获取组件实例
const getComponentInstance = (type: string) => {
  return WEBSITE_COMPONENTS[type as keyof typeof WEBSITE_COMPONENTS];
};

// 递归渲染组件
const renderComponent = (component: ComponentSchema) => {
  const ComponentInstance = getComponentInstance(component.type);
  if (!ComponentInstance) {
    return null;
  }
  return ComponentInstance;
};

// 事件处理
const handleModeChange = (mode: 'desktop' | 'mobile' | 'tablet') => {
  previewMode.value = mode;
};

const handleRefresh = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 获取预览模式的中文名称
const getPreviewModeName = (mode: string) => {
  const modeNames: Record<string, string> = {
    desktop: '桌面端',
    tablet: '平板端',
    mobile: '移动端',
  };
  return modeNames[mode] || mode;
};
</script>

<template>
  <div class="preview-panel">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <h3>{{ pageTitle }}</h3>
        <span v-if="pageDescription" class="page-desc">{{
          pageDescription
        }}</span>
      </div>

      <div class="toolbar-center">
        <!-- 设备切换 -->
        <a-radio-group
          :value="previewMode"
          @change="handleModeChange($event.target.value)"
          button-style="solid"
          size="small"
        >
          <a-radio-button value="desktop">
            <Icon icon="ic:baseline-desktop-windows" />
            桌面
          </a-radio-button>
          <a-radio-button value="tablet">
            <Icon icon="ic:baseline-tablet" />
            平板
          </a-radio-button>
          <a-radio-button value="mobile">
            <Icon icon="ic:baseline-smartphone" />
            手机
          </a-radio-button>
        </a-radio-group>
      </div>

      <div class="toolbar-right">
        <a-tooltip title="刷新预览">
          <a-button size="small" @click="handleRefresh" :loading="loading">
            <Icon icon="ic:baseline-refresh" />
          </a-button>
        </a-tooltip>
        <a-tooltip title="在新窗口打开">
          <a-button size="small">
            <Icon icon="ic:baseline-open-in-new" />
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- 预览区域 -->
    <div class="preview-container">
      <div class="preview-frame" :style="previewStyles">
        <div class="preview-content" v-if="!loading">
          <!-- 页面元信息 -->
          <div class="page-meta" v-if="pageSchema.seo">
            <title>{{ pageSchema.seo.title }}</title>
            <meta
              v-if="pageSchema.seo.description"
              name="description"
              :content="pageSchema.seo.description"
            />
            <meta
              v-if="pageSchema.seo.keywords"
              name="keywords"
              :content="pageSchema.seo.keywords.join(', ')"
            />
          </div>

          <!-- 页面组件渲染 -->
          <div
            class="page-components"
            :class="{
              'desktop-view': previewMode === 'desktop',
              'tablet-view': previewMode === 'tablet',
              'mobile-view': previewMode === 'mobile',
            }"
          >
            <component
              v-for="component in pageSchema.components"
              :key="component.id"
              :is="renderComponent(component)"
              v-bind="component.props"
              :styles="component.styles"
              :id="component.id"
              class="preview-component"
            >
              <!-- 递归渲染子组件 -->
              <template
                v-if="component.children && component.children.length > 0"
              >
                <component
                  v-for="child in component.children"
                  :key="child.id"
                  :is="renderComponent(child)"
                  v-bind="child.props"
                  :styles="child.styles"
                  :id="child.id"
                />
              </template>
            </component>
          </div>

          <!-- 空状态 -->
          <div v-if="pageSchema.components.length === 0" class="empty-preview">
            <Icon icon="ic:baseline-preview" class="empty-icon" />
            <h3>页面预览</h3>
            <p>当前页面没有任何组件，请添加组件后再预览</p>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else class="preview-loading">
          <a-spin size="large" />
          <p>正在生成预览...</p>
        </div>
      </div>
    </div>

    <!-- 预览信息栏 -->
    <div class="preview-info">
      <div class="info-left">
        <span>预览模式: {{ getPreviewModeName(previewMode) }}</span>
        <a-divider type="vertical" />
        <span>组件数量: {{ pageSchema.components.length }}</span>
        <a-divider type="vertical" />
        <span>
          页面状态:
          {{ pageSchema.status === 'published' ? '已发布' : '草稿' }}
        </span>
      </div>
      <div class="info-right">
        <span class="last-updated">
          最后更新: {{ new Date(pageSchema.updatedAt).toLocaleString() }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 12px;
    height: auto;
    padding: 12px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    justify-content: center;
    width: 100%;
  }

  .preview-container {
    padding: 16px 8px;
  }

  .preview-info {
    flex-direction: column;
    gap: 8px;
    height: auto;
    padding: 8px 16px;
  }

  .info-left,
  .info-right {
    justify-content: center;
    width: 100%;
  }
}

.preview-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.preview-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 16px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.page-desc {
  display: block;
  margin-top: 2px;
  font-size: 12px;
  color: #999;
}

.toolbar-center {
  display: flex;
  flex: 1;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.preview-container {
  display: flex;
  flex: 1;
  justify-content: center;
  padding: 24px;
  overflow: auto;
}

.preview-frame {
  min-height: 600px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
}

.preview-content {
  height: 100%;
}

.page-meta {
  display: none;
}

.page-components {
  min-height: 400px;
  padding: 0;
}

.preview-component {
  margin: 0;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
  color: #d9d9d9;
}

.empty-preview h3 {
  margin: 0 0 8px;
  font-size: 18px;
  color: #666;
}

.empty-preview p {
  margin: 0;
  font-size: 14px;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
  text-align: center;
}

.preview-loading p {
  margin-top: 16px;
  font-size: 14px;
}

.preview-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 16px;
  font-size: 12px;
  color: #666;
  background-color: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.info-left {
  display: flex;
  align-items: center;
}

.last-updated {
  font-style: italic;
}

/* 响应式预览样式 */
.desktop-view {
  /* 桌面端样式 */
}

.tablet-view {
  /* 平板端样式 */
  max-width: 768px;
}

.mobile-view {
  /* 移动端样式 */
  max-width: 375px;
}
</style>
