<script setup lang="ts">
import type { ComponentSchema, StyleConfig } from '../schema/page-schema';

import { computed, ref, watch } from 'vue';

import { Icon } from '@iconify/vue';
import { cloneDeep } from 'lodash-es';

// 定义Props
interface Props {
  component?: ComponentSchema | null;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  componentUpdate: [component: ComponentSchema];
}>();

// 响应式数据
const activeTab = ref<'animation' | 'props' | 'styles'>('props');
const localComponent = ref<ComponentSchema | null>(null);

// 监听选中组件变化
watch(
  () => props.component,
  (newComponent) => {
    localComponent.value = newComponent ? cloneDeep(newComponent) : null;
  },
  { immediate: true, deep: true },
);

// 计算属性
const hasComponent = computed(() => !!localComponent.value);

const componentProps = computed({
  get: () => localComponent.value?.props || {},
  set: (value) => {
    if (localComponent.value) {
      localComponent.value.props = value;
      emit('componentUpdate', localComponent.value);
    }
  },
});

const componentStyles = computed({
  get: () => localComponent.value?.styles || {},
  set: (value) => {
    if (localComponent.value) {
      localComponent.value.styles = value;
      emit('componentUpdate', localComponent.value);
    }
  },
});

// 样式编辑方法
const updateStyle = (key: keyof StyleConfig, value: any) => {
  if (localComponent.value) {
    const newStyles = { ...localComponent.value.styles };
    if (value === '' || value === undefined || value === null) {
      delete newStyles[key];
    } else {
      newStyles[key] = value;
    }
    localComponent.value.styles = newStyles;
    emit('componentUpdate', localComponent.value);
  }
};

const updateProp = (key: string, value: any) => {
  if (localComponent.value) {
    const newProps = { ...localComponent.value.props };
    if (value === '' || value === undefined || value === null) {
      delete newProps[key];
    } else {
      newProps[key] = value;
    }
    localComponent.value.props = newProps;
    emit('componentUpdate', localComponent.value);
  }
};

// 常用样式选项
const textAlignOptions = [
  { label: '左对齐', value: 'left' },
  { label: '居中', value: 'center' },
  { label: '右对齐', value: 'right' },
  { label: '两端对齐', value: 'justify' },
];

const displayOptions = [
  { label: 'Block', value: 'block' },
  { label: 'Inline', value: 'inline' },
  { label: 'Flex', value: 'flex' },
  { label: 'Grid', value: 'grid' },
  { label: 'None', value: 'none' },
];

const positionOptions = [
  { label: 'Static', value: 'static' },
  { label: 'Relative', value: 'relative' },
  { label: 'Absolute', value: 'absolute' },
  { label: 'Fixed', value: 'fixed' },
  { label: 'Sticky', value: 'sticky' },
];

// 获取组件类型的中文名称
const getComponentTypeName = (type: string) => {
  const typeNames: Record<string, string> = {
    text: '文本',
    container: '容器',
    image: '图片',
    button: '按钮',
    heading: '标题',
    section: '分段',
  };
  return typeNames[type] || type;
};
</script>

<template>
  <div class="property-panel">
    <!-- 空状态 -->
    <div v-if="!hasComponent" class="empty-state">
      <div class="empty-content">
        <Icon icon="ic:baseline-settings" class="empty-icon" />
        <h3>属性面板</h3>
        <p>选择一个组件来编辑其属性</p>
      </div>
    </div>

    <!-- 组件属性编辑 -->
    <div v-else class="panel-content">
      <!-- 组件信息头部 -->
      <div class="component-header">
        <div class="component-info">
          <Icon icon="ic:baseline-extension" class="component-icon" />
          <div>
            <div class="component-name">
              {{ getComponentTypeName(localComponent?.type || '') }}
            </div>
            <div class="component-id">ID: {{ localComponent?.id }}</div>
          </div>
        </div>
      </div>

      <!-- 标签切换 -->
      <a-tabs v-model:active-key="activeTab" size="small">
        <a-tab-pane key="props" tab="属性">
          <div class="tab-content">
            <!-- 通用属性 -->
            <div class="property-section">
              <h4>基础属性</h4>

              <!-- 文本组件属性 -->
              <template v-if="localComponent?.type === 'text'">
                <div class="form-item">
                  <label>文本内容</label>
                  <a-textarea
                    :value="componentProps.content"
                    placeholder="请输入文本内容"
                    @change="updateProp('content', $event.target.value)"
                    :auto-size="{ minRows: 2, maxRows: 4 }"
                  />
                </div>
                <div class="form-item">
                  <label>HTML标签</label>
                  <a-select
                    :value="componentProps.tag"
                    @change="updateProp('tag', $event)"
                    style="width: 100%"
                  >
                    <a-select-option value="div">Div</a-select-option>
                    <a-select-option value="p">Paragraph</a-select-option>
                    <a-select-option value="span">Span</a-select-option>
                  </a-select>
                </div>
                <div class="form-item">
                  <label>占位符</label>
                  <a-input
                    :value="componentProps.placeholder"
                    placeholder="请输入占位符文本"
                    @change="updateProp('placeholder', $event.target.value)"
                  />
                </div>
              </template>

              <!-- 容器组件属性 -->
              <template v-if="localComponent?.type === 'container'">
                <div class="form-item">
                  <label>流体宽度</label>
                  <a-switch
                    :checked="componentProps.fluid"
                    @change="updateProp('fluid', $event)"
                  />
                </div>
                <div class="form-item">
                  <label>最大宽度</label>
                  <a-input
                    :value="componentProps.maxWidth"
                    placeholder="如: 1200px 或 100%"
                    @change="updateProp('maxWidth', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>内边距</label>
                  <a-input
                    :value="componentProps.padding"
                    placeholder="如: 16px 或 1rem"
                    @change="updateProp('padding', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>背景图片</label>
                  <a-input
                    :value="componentProps.backgroundImage"
                    placeholder="图片URL"
                    @change="updateProp('backgroundImage', $event.target.value)"
                  />
                </div>
              </template>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="styles" tab="样式">
          <div class="tab-content">
            <!-- 尺寸设置 -->
            <div class="property-section">
              <h4>尺寸</h4>
              <div class="form-grid">
                <div class="form-item">
                  <label>宽度</label>
                  <a-input
                    :value="componentStyles.width"
                    placeholder="auto"
                    @change="updateStyle('width', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>高度</label>
                  <a-input
                    :value="componentStyles.height"
                    placeholder="auto"
                    @change="updateStyle('height', $event.target.value)"
                  />
                </div>
              </div>
            </div>

            <!-- 间距设置 -->
            <div class="property-section">
              <h4>间距</h4>
              <div class="form-grid">
                <div class="form-item">
                  <label>外边距</label>
                  <a-input
                    :value="componentStyles.margin"
                    placeholder="如: 16px"
                    @change="updateStyle('margin', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>内边距</label>
                  <a-input
                    :value="componentStyles.padding"
                    placeholder="如: 16px"
                    @change="updateStyle('padding', $event.target.value)"
                  />
                </div>
              </div>
            </div>

            <!-- 文字样式 -->
            <div class="property-section">
              <h4>文字</h4>
              <div class="form-grid">
                <div class="form-item">
                  <label>字体大小</label>
                  <a-input
                    :value="componentStyles.fontSize"
                    placeholder="如: 16px"
                    @change="updateStyle('fontSize', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>字体粗细</label>
                  <a-input
                    :value="componentStyles.fontWeight"
                    placeholder="如: bold"
                    @change="updateStyle('fontWeight', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>文字颜色</label>
                  <a-input
                    :value="componentStyles.color"
                    placeholder="如: #333333"
                    @change="updateStyle('color', $event.target.value)"
                  />
                </div>
                <div class="form-item">
                  <label>文字对齐</label>
                  <a-select
                    :value="componentStyles.textAlign"
                    @change="updateStyle('textAlign', $event)"
                    style="width: 100%"
                    placeholder="选择对齐方式"
                  >
                    <a-select-option
                      v-for="option in textAlignOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </div>
              </div>
            </div>

            <!-- 背景样式 -->
            <div class="property-section">
              <h4>背景</h4>
              <div class="form-grid">
                <div class="form-item">
                  <label>背景颜色</label>
                  <a-input
                    :value="componentStyles.backgroundColor"
                    placeholder="如: #ffffff"
                    @change="
                      updateStyle('backgroundColor', $event.target.value)
                    "
                  />
                </div>
                <div class="form-item">
                  <label>边框圆角</label>
                  <a-input
                    :value="componentStyles.borderRadius"
                    placeholder="如: 6px"
                    @change="updateStyle('borderRadius', $event.target.value)"
                  />
                </div>
              </div>
            </div>

            <!-- 布局样式 -->
            <div class="property-section">
              <h4>布局</h4>
              <div class="form-grid">
                <div class="form-item">
                  <label>显示类型</label>
                  <a-select
                    :value="componentStyles.display"
                    @change="updateStyle('display', $event)"
                    style="width: 100%"
                    placeholder="选择显示类型"
                  >
                    <a-select-option
                      v-for="option in displayOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="form-item">
                  <label>定位方式</label>
                  <a-select
                    :value="componentStyles.position"
                    @change="updateStyle('position', $event)"
                    style="width: 100%"
                    placeholder="选择定位方式"
                  >
                    <a-select-option
                      v-for="option in positionOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="animation" tab="动画">
          <div class="tab-content">
            <div class="property-section">
              <h4>动画效果</h4>
              <p class="section-desc">动画功能即将推出...</p>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 320px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .component-header {
    padding: 12px;
  }

  .tab-content {
    padding: 12px;
  }
}

.property-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.empty-content {
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #d9d9d9;
}

.empty-content h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #666;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.panel-content {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.component-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.component-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.component-icon {
  font-size: 20px;
  color: #1890ff;
}

.component-name {
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.component-id {
  font-size: 12px;
  color: #999;
}

.tab-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.property-section {
  margin-bottom: 24px;
}

.property-section h4 {
  padding-bottom: 8px;
  margin: 0 0 12px;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.section-desc {
  margin: 0;
  font-size: 12px;
  font-style: italic;
  color: #999;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-grid .form-item {
  margin-bottom: 12px;
}
</style>
