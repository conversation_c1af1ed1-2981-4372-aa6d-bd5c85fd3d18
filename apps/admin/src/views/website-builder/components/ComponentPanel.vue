<script setup lang="ts">
import type { ComponentType } from '../schema/component-schema';

import { computed, ref } from 'vue';

import { Icon } from '@iconify/vue';

// 定义Props
interface Props {
  categories: string[];
  components: Array<{
    category: string;
    config: any;
    type: ComponentType;
  }>;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  addComponent: [type: ComponentType];
}>();

// 响应式数据
const selectedCategory = ref<string>('all');
const searchKeyword = ref<string>('');

// 计算属性
const filteredComponents = computed(() => {
  let filtered = props.components;

  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(
      (comp) => comp.category === selectedCategory.value,
    );
  }

  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      (comp) =>
        comp.config.name.toLowerCase().includes(keyword) ||
        comp.config.description.toLowerCase().includes(keyword),
    );
  }

  return filtered;
});

const componentsByCategory = computed(() => {
  const grouped: Record<string, typeof props.components> = {};

  filteredComponents.value.forEach((comp) => {
    if (!grouped[comp.category]) {
      grouped[comp.category] = [];
    }
    grouped[comp.category].push(comp);
  });

  return grouped;
});

// 事件处理
const handleComponentClick = (type: ComponentType) => {
  emit('addComponent', type);
};

const handleCategoryChange = (category: string) => {
  selectedCategory.value = category;
};

// 获取图标名称
const getIconName = (iconName: string) => {
  const iconMap: Record<string, string> = {
    text: 'ic:baseline-text-fields',
    heading: 'ic:baseline-title',
    image: 'ic:baseline-image',
    button: 'ic:baseline-smart-button',
    container: 'ic:baseline-crop-din',
    section: 'ic:baseline-view-agenda',
    row: 'ic:baseline-view-stream',
    column: 'ic:baseline-view-column',
    divider: 'ic:baseline-horizontal-rule',
    spacer: 'ic:baseline-space-bar',
  };

  return iconMap[iconName] || 'ic:baseline-extension';
};

// 获取分类的中文名称
const getCategoryDisplayName = (category: string) => {
  const categoryNames: Record<string, string> = {
    content: '内容组件',
    layout: '布局组件',
    interactive: '交互组件',
    media: '媒体组件',
    navigation: '导航组件',
    composite: '复合组件',
  };

  return categoryNames[category] || category;
};
</script>

<template>
  <div class="component-panel">
    <!-- 头部搜索和筛选 -->
    <div class="panel-header">
      <h3>组件库</h3>

      <!-- 搜索框 -->
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索组件..."
        size="small"
      >
        <template #prefix>
          <Icon icon="ic:baseline-search" />
        </template>
      </a-input>

      <!-- 分类筛选 -->
      <div class="category-tabs">
        <a-button
          size="small"
          :type="selectedCategory === 'all' ? 'primary' : 'default'"
          @click="handleCategoryChange('all')"
        >
          全部
        </a-button>
        <a-button
          v-for="category in categories"
          :key="category"
          size="small"
          :type="selectedCategory === category ? 'primary' : 'default'"
          @click="handleCategoryChange(category)"
        >
          {{ getCategoryDisplayName(category) }}
        </a-button>
      </div>
    </div>

    <!-- 组件列表 -->
    <div class="panel-content">
      <div v-if="selectedCategory === 'all'">
        <!-- 按分类分组显示 -->
        <div
          v-for="(categoryComponents, category) in componentsByCategory"
          :key="category"
          class="component-category"
        >
          <h4 class="category-title">
            {{ getCategoryDisplayName(category) }}
          </h4>
          <div class="component-grid">
            <div
              v-for="component in categoryComponents"
              :key="component.type"
              class="component-item"
              @click="handleComponentClick(component.type)"
            >
              <div class="component-icon">
                <Icon :icon="getIconName(component.config.icon)" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.config.name }}</div>
                <div class="component-desc">
                  {{ component.config.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else>
        <!-- 单分类显示 -->
        <div class="component-grid">
          <div
            v-for="component in filteredComponents"
            :key="component.type"
            class="component-item"
            @click="handleComponentClick(component.type)"
          >
            <div class="component-icon">
              <Icon :icon="getIconName(component.config.icon)" />
            </div>
            <div class="component-info">
              <div class="component-name">{{ component.config.name }}</div>
              <div class="component-desc">
                {{ component.config.description }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredComponents.length === 0" class="empty-state">
        <Icon icon="ic:baseline-search-off" class="empty-icon" />
        <p>没有找到匹配的组件</p>
        <a-button
          size="small"
          @click="
            searchKeyword = '';
            selectedCategory = 'all';
          "
        >
          重置筛选
        </a-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 320px) {
  .component-grid {
    grid-template-columns: 1fr;
  }

  .component-item {
    padding: 8px;
  }

  .component-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

.component-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.component-category {
  margin-bottom: 24px;
}

.category-title {
  padding-bottom: 8px;
  margin: 0 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.component-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.component-item:hover {
  background-color: #f6ffed;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 12px;
  font-size: 16px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.component-item:hover .component-icon {
  color: #1890ff;
  background-color: #e6f7ff;
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-name {
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.component-desc {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #d9d9d9;
}
</style>
