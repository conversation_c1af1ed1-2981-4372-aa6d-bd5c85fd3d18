<script setup lang="ts">
import type { ComponentSchema } from '../schema/page-schema';

import { computed } from 'vue';
import { VueDraggable as VueDraggablePlus } from 'vue-draggable-plus';

import { Icon } from '@iconify/vue';

import { WEBSITE_COMPONENTS } from '../../../components/website-components';

// 定义Props
interface Props {
  components: ComponentSchema[];
  selectedComponent?: ComponentSchema | null;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  componentSelect: [component: ComponentSchema];
  componentUpdate: [component: ComponentSchema];
  'update:components': [components: ComponentSchema[]];
}>();

// 计算属性
const canvasComponents = computed({
  get: () => props.components,
  set: (value) => emit('update:components', value),
});

// 事件处理
const handleComponentClick = (
  component: ComponentSchema,
  event: MouseEvent,
) => {
  event.stopPropagation();
  emit('componentSelect', component);
};

const handleCanvasClick = () => {
  // 点击空白区域取消选择
  emit('componentSelect', null as any);
};

const handleComponentChange = () => {
  // 拖拽排序后触发
  emit('update:components', canvasComponents.value);
};

// 获取组件是否被选中
const isComponentSelected = (component: ComponentSchema) => {
  return props.selectedComponent?.id === component.id;
};

// 获取组件实例
const getComponentInstance = (type: string) => {
  return WEBSITE_COMPONENTS[type as keyof typeof WEBSITE_COMPONENTS];
};

// 渲染组件
const renderComponent = (component: ComponentSchema) => {
  const ComponentInstance = getComponentInstance(component.type);
  if (!ComponentInstance) {
    return null;
  }
  return ComponentInstance;
};
</script>

<template>
  <div class="drag-canvas" @click="handleCanvasClick">
    <!-- 画布头部工具栏 -->
    <div class="canvas-toolbar">
      <div class="toolbar-left">
        <Icon icon="ic:baseline-dashboard" />
        <span>设计画布</span>
      </div>
      <div class="toolbar-right">
        <a-tooltip title="撤销">
          <a-button size="small" :disabled="true">
            <Icon icon="ic:baseline-undo" />
          </a-button>
        </a-tooltip>
        <a-tooltip title="重做">
          <a-button size="small" :disabled="true">
            <Icon icon="ic:baseline-redo" />
          </a-button>
        </a-tooltip>
        <a-tooltip title="清空画布">
          <a-button size="small" danger @click="canvasComponents = []">
            <Icon icon="ic:baseline-clear-all" />
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- 画布内容区域 -->
    <div class="canvas-content">
      <!-- 空状态提示 -->
      <div v-if="canvasComponents.length === 0" class="empty-canvas">
        <div class="empty-content">
          <Icon icon="ic:baseline-design-services" class="empty-icon" />
          <h3>开始构建你的页面</h3>
          <p>从左侧组件库拖拽组件到这里开始设计</p>
        </div>
      </div>

      <!-- 组件列表 -->
      <VueDraggablePlus
        v-else
        v-model="canvasComponents"
        :group="{ name: 'canvas-components' }"
        item-key="id"
        @change="handleComponentChange"
        class="components-container"
      >
        <template #item="{ element: component }">
          <div
            :key="component.id"
            class="canvas-component hover-highlight"
            :class="{
              selected: isComponentSelected(component),
            }"
            @click="handleComponentClick(component, $event)"
          >
            <!-- 组件选中状态指示器 -->
            <div
              v-if="isComponentSelected(component)"
              class="selection-indicator"
            >
              <div class="selection-handle top-left"></div>
              <div class="selection-handle top-right"></div>
              <div class="selection-handle bottom-left"></div>
              <div class="selection-handle bottom-right"></div>
              <div class="selection-toolbar">
                <a-tooltip title="复制组件">
                  <a-button size="small" type="text">
                    <Icon icon="ic:baseline-content-copy" />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="删除组件">
                  <a-button size="small" type="text" danger>
                    <Icon icon="ic:baseline-delete" />
                  </a-button>
                </a-tooltip>
              </div>
            </div>

            <!-- 组件内容 -->
            <component
              :is="renderComponent(component)"
              v-bind="component.props"
              :styles="component.styles"
              :id="component.id"
              :data-component-id="component.id"
              :data-component-type="component.type"
            >
              <!-- 如果组件有子组件，递归渲染 -->
              <template
                v-if="component.children && component.children.length > 0"
              >
                <component
                  v-for="child in component.children"
                  :key="child.id"
                  :is="renderComponent(child)"
                  v-bind="child.props"
                  :styles="child.styles"
                  :id="child.id"
                  :data-component-id="child.id"
                  :data-component-type="child.type"
                />
              </template>
            </component>
          </div>
        </template>
      </VueDraggablePlus>
    </div>

    <!-- 画布尺度指示器 -->
    <div class="canvas-ruler">
      <div class="ruler-info">
        <span>1920 × Auto</span>
        <a-divider type="vertical" />
        <span>桌面视图</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 768px) {
  .canvas-content {
    padding: 16px;
  }

  .canvas-toolbar {
    padding: 0 12px;
  }

  .toolbar-left span {
    display: none;
  }
}

.drag-canvas {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.canvas-content {
  position: relative;
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.empty-content {
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
  color: #d9d9d9;
}

.empty-content h3 {
  margin: 0 0 8px;
  font-size: 18px;
  color: #666;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.components-container {
  min-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.canvas-component {
  position: relative;
  margin: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.canvas-component.hover-highlight:hover {
  box-shadow: 0 0 0 2px rgb(24 144 255 / 30%);
}

.canvas-component.selected {
  box-shadow: 0 0 0 2px #1890ff;
}

.selection-indicator {
  position: absolute;
  inset: -3px;
  z-index: 10;
  pointer-events: none;
}

.selection-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  pointer-events: auto;
  cursor: nw-resize;
  background-color: #1890ff;
  border: 2px solid #fff;
  border-radius: 50%;
}

.selection-handle.top-left {
  top: -4px;
  left: -4px;
}

.selection-handle.top-right {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.selection-handle.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.selection-handle.bottom-right {
  right: -4px;
  bottom: -4px;
  cursor: se-resize;
}

.selection-toolbar {
  position: absolute;
  top: -40px;
  right: 0;
  display: flex;
  gap: 4px;
  padding: 4px;
  pointer-events: auto;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

.canvas-ruler {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 16px;
  background-color: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.ruler-info {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 12px;
  color: #666;
}

/* 拖拽样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-drag {
  transform: rotate(5deg);
}
</style>
