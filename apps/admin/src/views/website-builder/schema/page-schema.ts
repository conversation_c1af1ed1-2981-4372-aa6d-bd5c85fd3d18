/**
 * FlexiHub页面构建器 - 页面Schema定义
 * 定义了拖拽生成页面的数据结构，确保Vue3和Next.js之间的兼容性
 */

// SEO Meta信息
export interface SEOConfig {
  canonical?: string;
  description?: string;
  keywords?: string[];
  openGraph?: {
    description?: string;
    image?: string;
    title?: string;
    type?: 'article' | 'product' | 'website';
    url?: string;
  };
  robots?:
    | 'index,follow'
    | 'index,nofollow'
    | 'noindex,follow'
    | 'noindex,nofollow';
  structuredData?: Record<string, any>[];
  title: string;
  twitter?: {
    card?: 'app' | 'player' | 'summary' | 'summary_large_image';
    creator?: string;
    description?: string;
    image?: string;
    site?: string;
    title?: string;
  };
}

// 国际化配置
export interface I18nConfig {
  content: Record<string, Record<string, string>>; // { 'en': { 'hello': 'Hello' }, 'zh': { 'hello': '你好' } }
  dateFormat?: Record<string, string>;
  defaultLocale: string;
  locales: string[];
  numberFormat?: Record<string, any>;
}

// 响应式配置
export interface ResponsiveConfig {
  breakpoints?: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  desktop?: Record<string, any>; // > 1024px
  mobile?: Record<string, any>; // < 768px
  tablet?: Record<string, any>; // 768px - 1024px
}

// 动画配置
export interface AnimationConfig {
  customKeyframes?: Record<string, any>;
  delay?: number;
  duration: number;
  easing?: string;
  trigger?: 'click' | 'hover' | 'load' | 'scroll';
  type: 'bounce' | 'custom' | 'fade' | 'rotate' | 'scale' | 'slide';
}

// 样式配置
export interface StyleConfig {
  alignContent?: string;
  alignItems?: string;
  alignSelf?: string;
  // 背景
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;

  backgroundSize?: string;
  // 边框
  border?: string;
  borderBottom?: string;
  borderColor?: string;
  borderLeft?: string;
  borderRadius?: string;

  borderRight?: string;
  borderStyle?: string;
  borderTop?: string;
  borderWidth?: string;
  bottom?: string;
  // 阴影
  boxShadow?: string;
  // 文字
  color?: string;
  cursor?: string;
  // 布局
  display?: string;
  // Flexbox
  flex?: string;

  flexBasis?: string;
  flexDirection?: string;
  flexGrow?: number;
  flexShrink?: number;
  flexWrap?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  gridColumn?: string;

  gridGap?: string;
  gridRow?: string;
  // Grid
  gridTemplateColumns?: string;
  gridTemplateRows?: string;
  height?: string;

  justifyContent?: string;
  left?: string;
  letterSpacing?: string;
  lineHeight?: string;
  // 间距
  margin?: string;
  marginBottom?: string;
  marginLeft?: string;
  marginRight?: string;
  marginTop?: string;
  maxHeight?: string;

  maxWidth?: string;
  minHeight?: string;

  minWidth?: string;
  // 其他
  opacity?: number;

  overflow?: string;

  overflowX?: string;
  overflowY?: string;
  padding?: string;
  paddingBottom?: string;
  paddingLeft?: string;
  paddingRight?: string;
  paddingTop?: string;

  pointerEvents?: string;
  position?: string;
  right?: string;
  textAlign?: string;
  textDecoration?: string;
  textShadow?: string;
  textTransform?: string;
  top?: string;
  // 变换
  transform?: string;
  transformOrigin?: string;

  // 过渡
  transition?: string;
  userSelect?: string;
  // 尺寸
  width?: string;
  wordSpacing?: string;
  zIndex?: number;
}

// 渲染条件
export interface RenderCondition {
  field?: string;
  operator:
    | 'contains'
    | 'equals'
    | 'greater_than'
    | 'less_than'
    | 'not_contains'
    | 'not_equals';
  type: 'custom' | 'device' | 'locale' | 'time' | 'user';
  value: any;
}

// 脚本配置
export interface ScriptConfig {
  async?: boolean;
  conditions?: RenderCondition[];
  content?: string;
  defer?: boolean;
  id: string;
  position: 'body_end' | 'body_start' | 'head';
  src?: string;
  type: 'external' | 'inline';
}

// 布局配置
export interface LayoutConfig {
  container?: {
    className?: string;
    maxWidth?: number | string;
    padding?: number | string;
  };
  customClasses?: string[];
  footer?: {
    height?: number | string;
    show: boolean;
  };
  header?: {
    fixed?: boolean;
    height?: number | string;
    show: boolean;
    transparent?: boolean;
  };
  sidebar?: {
    position: 'left' | 'right';
    show: boolean;
    width?: number | string;
  };
  type: 'boxed' | 'custom' | 'default' | 'full-width' | 'sidebar';
}

// 组件Schema
export interface ComponentSchema {
  // 动画配置
  animation?: AnimationConfig;
  // 子组件
  children?: ComponentSchema[];
  // 渲染条件
  conditions?: RenderCondition[];
  // 组件特定配置
  config?: Record<string, any>;

  // 数据绑定
  dataBinding?: {
    field: string;
    source: string;
    transform?: string;
  };

  description?: string;

  // 事件处理
  events?: Record<string, string>;

  // 基础信息
  id: string;

  // 元数据
  meta?: {
    author?: string;
    category: string;
    hidden?: boolean;
    locked?: boolean;
    tags: string[];
    version: string;
  };

  name?: string;

  // 组件属性
  props: Record<string, any>;

  // 响应式配置
  responsive?: ResponsiveConfig;

  // 样式配置
  styles: StyleConfig;

  type: string;
}

// 页面Schema主结构
export interface PageSchema {
  // 访问控制
  access?: {
    allowedRoles?: string[];
    allowedUsers?: string[];
    requireAuth?: boolean;
  };
  // 缓存配置
  cache?: {
    invalidateOn?: string[];
    tags?: string[];
    ttl?: number;
  };
  // 页面组件
  components: ComponentSchema[];
  // 版本控制
  createdAt: string;

  description?: string;
  // 全局样式
  globalStyles?: StyleConfig;
  // 国际化配置
  i18n: I18nConfig;
  // 基础信息
  id: string;
  // 布局配置
  layout: LayoutConfig;

  // 元数据
  meta?: {
    author?: string;
    category?: string;
    tags?: string[];
    template?: string;
    thumbnail?: string;
  };

  publishedAt?: string;

  // 脚本配置
  scripts?: ScriptConfig[];

  // SEO配置
  seo: SEOConfig;

  // 页面设置
  settings?: {
    customBody?: string;
    customHead?: string;
    enableAnalytics?: boolean;
    enableComments?: boolean;
    enableSharing?: boolean;
  };

  slug: string;

  status: 'archived' | 'draft' | 'published';

  title: string;

  updatedAt: string;

  version: string;
}

// 页面集合Schema (网站)
export interface WebsiteSchema {
  createTime: string;
  domain: string;
  globalSettings: {
    breakpoints: {
      desktop: number;
      mobile: number;
      tablet: number;
    };
    colors: {
      error: string;
      primary: string;
      secondary: string;
      success: string;
      warning: string;
    };
    fonts: string[];
    spacing: {
      lg: string;
      md: string;
      sm: string;
      xl: string;
      xs: string;
    };
    theme: {
      mode: 'dark' | 'light';
      primaryColor: string;
    };
  };
  id: string;
  name: string;
  navigation: {
    footer: ComponentSchema[];
    header: ComponentSchema[];
  };
  pages: PageSchema[];
  status: number;
}

// 导出所有类型
export type {
  AnimationConfig,
  ComponentSchema,
  I18nConfig,
  LayoutConfig,
  PageSchema,
  RenderCondition,
  ResponsiveConfig,
  ScriptConfig,
  SEOConfig,
  StyleConfig,
  WebsiteSchema,
};
