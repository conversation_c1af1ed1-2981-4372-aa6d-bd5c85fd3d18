/**
 * Schema validators using Zod for type-safe validation
 */

import { z } from 'zod';

// 基础组件schema验证器
export const ComponentPropsSchema = z.record(z.any());

export const ComponentStyleSchema = z.record(z.string());

export const ComponentEventSchema = z.object({
  type: z.string(),
  handler: z.string(),
  params: z.record(z.any()).optional(),
});

export const ComponentSchema = z.object({
  id: z.string(),
  type: z.string(),
  props: ComponentPropsSchema.optional(),
  styles: ComponentStyleSchema.optional(),
  events: z.array(ComponentEventSchema).optional(),
  children: z.array(z.lazy(() => ComponentSchema)).optional(),
});

// 页面schema验证器
export const PageMetaSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  keywords: z.string().optional(),
  thumbnail: z.string().optional(),
});

export const PageSchema = z.object({
  id: z.string(),
  title: z.string(),
  slug: z.string(),
  description: z.string().optional(),
  meta: PageMetaSchema.optional(),
  components: z.array(ComponentSchema),
  styles: z.record(z.string()).optional(),
  scripts: z.array(z.string()).optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  createdAt: z.string(),
  updatedAt: z.string(),
  version: z.number().default(1),
});

// 网站schema验证器
export const WebsiteConfigSchema = z.object({
  theme: z.string().optional(),
  favicon: z.string().optional(),
  logo: z.string().optional(),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  fonts: z.array(z.string()).optional(),
});

export const WebsiteSchema = z.object({
  id: z.string(),
  name: z.string(),
  domain: z.string().optional(),
  description: z.string().optional(),
  config: WebsiteConfigSchema.optional(),
  pages: z.array(PageSchema),
  status: z
    .enum(['development', 'production', 'maintenance'])
    .default('development'),
  createdAt: z.string(),
  updatedAt: z.string(),
  version: z.number().default(1),
});

// 导出类型
export type ComponentProps = z.infer<typeof ComponentPropsSchema>;
export type ComponentStyle = z.infer<typeof ComponentStyleSchema>;
export type ComponentEvent = z.infer<typeof ComponentEventSchema>;
export type Component = z.infer<typeof ComponentSchema>;
export type PageMeta = z.infer<typeof PageMetaSchema>;
export type Page = z.infer<typeof PageSchema>;
export type WebsiteConfig = z.infer<typeof WebsiteConfigSchema>;
export type Website = z.infer<typeof WebsiteSchema>;
