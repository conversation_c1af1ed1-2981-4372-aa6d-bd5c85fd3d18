/**
 * FlexiHub页面构建器 - 组件Schema定义
 * 定义了可拖拽组件的具体结构和属性
 */

import type { ComponentSchema, StyleConfig } from './page-schema';

// 基础组件类型枚举
export enum ComponentType {
  AUDIO = 'audio',
  BREADCRUMB = 'breadcrumb',
  // 交互组件
  BUTTON = 'button',
  // 复合组件
  CARD = 'card',
  // 媒体组件
  CAROUSEL = 'carousel',

  COLUMN = 'column',
  CONTACT = 'contact',
  // 布局组件
  CONTAINER = 'container',
  DIVIDER = 'divider',
  FAQ = 'faq',
  FORM = 'form',

  GALLERY = 'gallery',
  HEADING = 'heading',
  HERO = 'hero',
  ICON = 'icon',
  IMAGE = 'image',
  INPUT = 'input',

  LINK = 'link',
  // 导航组件
  NAVBAR = 'navbar',
  PAGINATION = 'pagination',

  PRICING = 'pricing',
  ROW = 'row',
  SECTION = 'section',

  SELECT = 'select',
  SPACER = 'spacer',
  TESTIMONIAL = 'testimonial',
  // 内容组件
  TEXT = 'text',
  TEXTAREA = 'textarea',
  VIDEO = 'video',
}

// 组件配置基础接口
export interface BaseComponentConfig {
  category: string;
  description: string;
  icon: string;
  name: string;
  previewImage?: string;
  tags: string[];
}

// 文本组件配置
export interface TextComponentProps {
  content: string;
  editable?: boolean;
  placeholder?: string;
  tag?: 'div' | 'p' | 'span';
}

// 标题组件配置
export interface HeadingComponentProps {
  content: string;
  level: 1 | 2 | 3 | 4 | 5 | 6;
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

// 图片组件配置
export interface ImageComponentProps {
  alt: string;
  height?: number | string;
  lazy?: boolean;
  src: string;
  width?: number | string;
}

// 按钮组件配置
export interface ButtonComponentProps {
  disabled?: boolean;
  href?: string;
  icon?: string;
  loading?: boolean;
  size?: 'large' | 'medium' | 'small';
  text: string;
  type?: 'danger' | 'default' | 'link' | 'primary' | 'success' | 'warning';
  variant?: 'ghost' | 'outline' | 'solid';
}

// 容器组件配置
export interface ContainerComponentProps {
  backgroundImage?: string;
  className?: string;
  fluid?: boolean;
  maxWidth?: number | string;
  padding?: number | string;
}

// 表单组件配置
export interface FormComponentProps {
  action?: string;
  enctype?: string;
  method?: 'GET' | 'POST';
  name?: string;
  target?: '_blank' | '_parent' | '_self' | '_top';
}

// 输入框组件配置
export interface InputComponentProps {
  autoComplete?: boolean;
  disabled?: boolean;
  maxLength?: number;
  name: string;
  placeholder?: string;
  required?: boolean;
  type?: 'email' | 'number' | 'password' | 'tel' | 'text' | 'url';
  value?: string;
}

// 预定义组件配置
export const COMPONENT_CONFIGS: Record<ComponentType, BaseComponentConfig> = {
  // 内容组件
  [ComponentType.TEXT]: {
    category: 'content',
    description: '可编辑的文本内容',
    icon: 'text',
    name: '文本',
    tags: ['text', 'content', 'paragraph'],
  },
  [ComponentType.HEADING]: {
    category: 'content',
    description: '页面标题，支持H1-H6',
    icon: 'heading',
    name: '标题',
    tags: ['heading', 'title', 'h1', 'h2', 'h3'],
  },
  [ComponentType.IMAGE]: {
    category: 'media',
    description: '图片展示组件',
    icon: 'image',
    name: '图片',
    tags: ['image', 'picture', 'media'],
  },
  [ComponentType.VIDEO]: {
    category: 'media',
    description: '视频播放器',
    icon: 'video',
    name: '视频',
    tags: ['video', 'media', 'player'],
  },
  [ComponentType.AUDIO]: {
    category: 'media',
    description: '音频播放器',
    icon: 'audio',
    name: '音频',
    tags: ['audio', 'media', 'sound'],
  },

  // 交互组件
  [ComponentType.BUTTON]: {
    category: 'interactive',
    description: '可点击的按钮',
    icon: 'button',
    name: '按钮',
    tags: ['button', 'click', 'action'],
  },
  [ComponentType.LINK]: {
    category: 'interactive',
    description: '超链接',
    icon: 'link',
    name: '链接',
    tags: ['link', 'url', 'navigation'],
  },
  [ComponentType.FORM]: {
    category: 'interactive',
    description: '表单容器',
    icon: 'form',
    name: '表单',
    tags: ['form', 'input', 'submit'],
  },
  [ComponentType.INPUT]: {
    category: 'interactive',
    description: '输入框',
    icon: 'input',
    name: '输入框',
    tags: ['input', 'field', 'text'],
  },
  [ComponentType.TEXTAREA]: {
    category: 'interactive',
    description: '多行文本输入',
    icon: 'textarea',
    name: '文本域',
    tags: ['textarea', 'multiline', 'text'],
  },
  [ComponentType.SELECT]: {
    category: 'interactive',
    description: '下拉选择框',
    icon: 'select',
    name: '选择框',
    tags: ['select', 'dropdown', 'option'],
  },

  // 布局组件
  [ComponentType.CONTAINER]: {
    category: 'layout',
    description: '容器组件，包含其他组件',
    icon: 'container',
    name: '容器',
    tags: ['container', 'wrapper', 'layout'],
  },
  [ComponentType.SECTION]: {
    category: 'layout',
    description: '页面分段',
    icon: 'section',
    name: '分段',
    tags: ['section', 'segment', 'block'],
  },
  [ComponentType.ROW]: {
    category: 'layout',
    description: '水平布局行',
    icon: 'row',
    name: '行',
    tags: ['row', 'horizontal', 'flex'],
  },
  [ComponentType.COLUMN]: {
    category: 'layout',
    description: '垂直布局列',
    icon: 'column',
    name: '列',
    tags: ['column', 'vertical', 'flex'],
  },
  [ComponentType.DIVIDER]: {
    category: 'layout',
    description: '分割线',
    icon: 'divider',
    name: '分割线',
    tags: ['divider', 'separator', 'hr'],
  },
  [ComponentType.SPACER]: {
    category: 'layout',
    description: '空白间距',
    icon: 'spacer',
    name: '间距',
    tags: ['spacer', 'space', 'gap'],
  },

  // 导航组件
  [ComponentType.NAVBAR]: {
    category: 'navigation',
    description: '导航栏',
    icon: 'navbar',
    name: '导航栏',
    tags: ['navbar', 'menu', 'navigation'],
  },
  [ComponentType.BREADCRUMB]: {
    category: 'navigation',
    description: '面包屑导航',
    icon: 'breadcrumb',
    name: '面包屑',
    tags: ['breadcrumb', 'path', 'navigation'],
  },
  [ComponentType.PAGINATION]: {
    category: 'navigation',
    description: '分页组件',
    icon: 'pagination',
    name: '分页',
    tags: ['pagination', 'pages', 'navigation'],
  },

  // 媒体组件
  [ComponentType.CAROUSEL]: {
    category: 'media',
    description: '轮播图',
    icon: 'carousel',
    name: '轮播图',
    tags: ['carousel', 'slider', 'gallery'],
  },
  [ComponentType.GALLERY]: {
    category: 'media',
    description: '图片画廊',
    icon: 'gallery',
    name: '图片库',
    tags: ['gallery', 'images', 'grid'],
  },
  [ComponentType.ICON]: {
    category: 'media',
    description: '图标',
    icon: 'icon',
    name: '图标',
    tags: ['icon', 'symbol', 'graphic'],
  },

  // 复合组件
  [ComponentType.CARD]: {
    category: 'composite',
    description: '卡片容器',
    icon: 'card',
    name: '卡片',
    tags: ['card', 'container', 'block'],
  },
  [ComponentType.HERO]: {
    category: 'composite',
    description: '英雄区块',
    icon: 'hero',
    name: '英雄区',
    tags: ['hero', 'banner', 'header'],
  },
  [ComponentType.TESTIMONIAL]: {
    category: 'composite',
    description: '用户评价',
    icon: 'testimonial',
    name: '评价',
    tags: ['testimonial', 'review', 'quote'],
  },
  [ComponentType.PRICING]: {
    category: 'composite',
    description: '价格表',
    icon: 'pricing',
    name: '价格表',
    tags: ['pricing', 'plans', 'cost'],
  },
  [ComponentType.FAQ]: {
    category: 'composite',
    description: '常见问题',
    icon: 'faq',
    name: '常见问题',
    tags: ['faq', 'question', 'answer'],
  },
  [ComponentType.CONTACT]: {
    category: 'composite',
    description: '联系表单',
    icon: 'contact',
    name: '联系表单',
    tags: ['contact', 'form', 'message'],
  },
};

// 默认样式配置
export const DEFAULT_STYLES: Record<ComponentType, Partial<StyleConfig>> = {
  [ComponentType.TEXT]: {
    color: '#333333',
    fontSize: '16px',
    lineHeight: '1.5',
    margin: '0 0 16px 0',
  },
  [ComponentType.HEADING]: {
    color: '#222222',
    fontWeight: 'bold',
    lineHeight: '1.2',
    margin: '0 0 24px 0',
  },
  [ComponentType.IMAGE]: {
    height: 'auto',
    maxWidth: '100%',
  },
  [ComponentType.BUTTON]: {
    backgroundColor: '#1890ff',
    border: 'none',
    borderRadius: '6px',
    color: '#ffffff',
    cursor: 'pointer',
    fontSize: '14px',
    padding: '8px 16px',
  },
  [ComponentType.CONTAINER]: {
    padding: '16px',
    width: '100%',
  },
  [ComponentType.SECTION]: {
    margin: '32px 0',
    padding: '32px 0',
  },
  [ComponentType.ROW]: {
    display: 'flex',
    flexDirection: 'row',
    gap: '16px',
  },
  [ComponentType.COLUMN]: {
    display: 'flex',
    flex: '1',
    flexDirection: 'column',
  },
  [ComponentType.DIVIDER]: {
    backgroundColor: '#e8e8e8',
    border: 'none',
    height: '1px',
    margin: '24px 0',
  },
  [ComponentType.SPACER]: {
    height: '32px',
    width: '100%',
  },
  // 其他组件使用空对象作为默认样式
  [ComponentType.VIDEO]: {},
  [ComponentType.AUDIO]: {},
  [ComponentType.LINK]: {},
  [ComponentType.FORM]: {},
  [ComponentType.INPUT]: {},
  [ComponentType.TEXTAREA]: {},
  [ComponentType.SELECT]: {},
  [ComponentType.NAVBAR]: {},
  [ComponentType.BREADCRUMB]: {},
  [ComponentType.PAGINATION]: {},
  [ComponentType.CAROUSEL]: {},
  [ComponentType.GALLERY]: {},
  [ComponentType.ICON]: {},
  [ComponentType.CARD]: {},
  [ComponentType.HERO]: {},
  [ComponentType.TESTIMONIAL]: {},
  [ComponentType.PRICING]: {},
  [ComponentType.FAQ]: {},
  [ComponentType.CONTACT]: {},
};

// 创建组件Schema的工厂函数
export function createComponentSchema(
  type: ComponentType,
  props: Record<string, any> = {},
  styles: Partial<StyleConfig> = {},
  children: ComponentSchema[] = [],
): ComponentSchema {
  const config = COMPONENT_CONFIGS[type];
  const defaultStyles = DEFAULT_STYLES[type];

  return {
    animation: undefined,
    children,
    conditions: [],
    config: {},
    dataBinding: undefined,
    events: {},
    id: `${type}_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`,
    meta: {
      author: 'FlexiHub',
      category: config.category,
      hidden: false,
      locked: false,
      tags: config.tags,
      version: '1.0.0',
    },
    props,
    responsive: {},
    styles: { ...defaultStyles, ...styles },
    type,
  };
}

// 获取组件配置
export function getComponentConfig(type: ComponentType): BaseComponentConfig {
  return COMPONENT_CONFIGS[type];
}

// 获取默认样式
export function getDefaultStyles(type: ComponentType): Partial<StyleConfig> {
  return DEFAULT_STYLES[type];
}

// 按分类获取组件
export function getComponentsByCategory(category: string): ComponentType[] {
  return Object.entries(COMPONENT_CONFIGS)
    .filter(([, config]) => config.category === category)
    .map(([type]) => type as ComponentType);
}

// 获取所有分类
export function getAllCategories(): string[] {
  const categories = new Set(
    Object.values(COMPONENT_CONFIGS).map((config) => config.category),
  );
  return [...categories].sort();
}

// 导出类型
export type {
  BaseComponentConfig,
  ButtonComponentProps,
  ContainerComponentProps,
  FormComponentProps,
  HeadingComponentProps,
  ImageComponentProps,
  InputComponentProps,
  TextComponentProps,
};
