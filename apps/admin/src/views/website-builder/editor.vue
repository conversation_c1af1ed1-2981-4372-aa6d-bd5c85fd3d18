<script setup lang="ts">
// 导入Schema和工具
import type { ComponentSchema, PageSchema } from './schema/page-schema';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { v4 as uuidv4 } from 'uuid';

import {
  getAllCategories,
  getComponentsByCategory,
} from '../../components/website-components';
// 导入组件
import ComponentPanel from './components/ComponentPanel.vue';
import DragCanvas from './components/DragCanvas.vue';
import PreviewPanel from './components/PreviewPanel.vue';
import PropertyPanel from './components/PropertyPanel.vue';
import {
  ComponentType,
  createComponentSchema,
} from './schema/component-schema';

const route = useRoute();
const router = useRouter();

// 响应式数据
const websiteId = ref<string>('');
const pageId = ref<string>('');
const websiteName = ref<string>('');
const pageName = ref<string>('新建页面');
const isEditMode = ref<boolean>(false);

const pageComponents = ref<ComponentSchema[]>([
  // 默认添加一个容器组件作为示例
  createComponentSchema(ComponentType.CONTAINER, {
    fluid: false,
    padding: '32px',
  }),
]);

const selectedComponent = ref<ComponentSchema | null>(null);
const previewVisible = ref(false);

// 初始化
onMounted(() => {
  websiteId.value = route.params.websiteId as string;
  pageId.value = route.params.pageId as string;
  isEditMode.value = !!pageId.value;

  // 模拟根据websiteId获取网站名称
  if (websiteId.value === 'website-1') {
    websiteName.value = 'FlexiHub官网';
  } else if (websiteId.value === 'website-2') {
    websiteName.value = '企业展示站';
  } else {
    websiteName.value = '未知网站';
  }

  // 如果是编辑模式，加载页面数据
  if (isEditMode.value) {
    loadPageData();
  }
});

const loadPageData = () => {
  // TODO: 根据pageId加载页面数据
  // 模拟数据
  if (pageId.value === '1') {
    pageName.value = '首页';
  } else if (pageId.value === '2') {
    pageName.value = '关于我们';
  }
};

// 计算属性
const availableComponents = computed(() => {
  const allComponents: Array<{
    category: string;
    config: any;
    type: ComponentType;
  }> = [];

  // 获取所有分类
  const categories = getAllCategories();

  categories.forEach((category) => {
    const categoryComponents = getComponentsByCategory(category);
    categoryComponents.forEach((type) => {
      allComponents.push({
        category,
        config: {
          description: `${type}组件`,
          icon: type,
          name: type,
          type,
        },
        type,
      });
    });
  });

  return allComponents;
});

const componentCategories = computed(() => {
  return getAllCategories();
});

const currentPageSchema = computed((): PageSchema => {
  return {
    access: undefined,
    cache: undefined,
    components: pageComponents.value,
    createdAt: new Date().toISOString(),
    description: '通过拖拽生成的页面',
    globalStyles: undefined,
    i18n: {
      content: {
        en: {},
        zh: {},
      },
      defaultLocale: 'zh',
      locales: ['zh', 'en'],
    },
    id: uuidv4(),
    layout: {
      type: 'default',
    },
    meta: undefined,
    publishedAt: undefined,
    scripts: undefined,
    seo: {
      title: '新建页面',
    },
    settings: undefined,
    slug: 'new-page',
    status: 'draft',
    title: '新建页面',
    updatedAt: new Date().toISOString(),
    version: '1.0.0',
  };
});

// 事件处理函数
const handleBackToPages = () => {
  router.push(`/website-builder/pages/${websiteId.value}`);
};

const handleBackToWebsites = () => {
  router.push('/website-builder/websites');
};

const handleAddComponent = (componentType: ComponentType) => {
  const newComponent = createComponentSchema(componentType);
  pageComponents.value.push(newComponent);
  selectedComponent.value = newComponent;
  message.success(`已添加${componentType}组件`);
};

const handleComponentSelect = (component: ComponentSchema) => {
  selectedComponent.value = component;
};

const handleComponentUpdate = (updatedComponent: ComponentSchema) => {
  const index = pageComponents.value.findIndex(
    (comp) => comp.id === updatedComponent.id,
  );
  if (index !== -1) {
    pageComponents.value[index] = updatedComponent;
  }

  // 更新选中组件
  if (selectedComponent.value?.id === updatedComponent.id) {
    selectedComponent.value = updatedComponent;
  }
};

const handlePreview = () => {
  previewVisible.value = true;
};

const handleSave = async () => {
  try {
    // TODO: 调用API保存页面Schema
    // await savePageSchema(currentPageSchema.value);
    message.success('页面保存成功');
  } catch (error) {
    message.error('页面保存失败');
    console.error('Save error:', error);
  }
};

const handlePublish = async () => {
  try {
    // TODO: 调用API发布页面
    // await publishPage(currentPageSchema.value);
    message.success('页面发布成功');
  } catch (error) {
    message.error('页面发布失败');
    console.error('Publish error:', error);
  }
};
</script>

<template>
  <div class="website-builder">
    <!-- 头部工具栏 -->
    <div class="builder-header">
      <div class="header-left">
        <h2>{{ isEditMode ? '编辑页面' : '创建页面' }}</h2>
        <a-breadcrumb>
          <a-breadcrumb-item>
            <a @click="handleBackToWebsites">
              <Icon icon="ic:baseline-web" />
              网站管理
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <a @click="handleBackToPages">
              <Icon icon="ic:baseline-pages" />
              {{ websiteName }} - 页面管理
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <Icon icon="ic:baseline-edit" />
            {{ pageName }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="header-right">
        <a-button style="margin-right: 8px" @click="handleBackToPages">
          <Icon icon="ic:baseline-arrow-back" />
          返回页面管理
        </a-button>
        <a-button @click="handlePreview">
          <Icon icon="ic:baseline-visibility" />
          预览
        </a-button>
        <a-button type="primary" @click="handleSave">
          <Icon icon="ic:baseline-save" />
          保存
        </a-button>
        <a-button type="primary" @click="handlePublish">
          <Icon icon="ic:baseline-publish" />
          发布
        </a-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="builder-content">
      <!-- 左侧组件面板 -->
      <div class="components-panel">
        <ComponentPanel
          :categories="componentCategories"
          :components="availableComponents"
          @add-component="handleAddComponent"
        />
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <DragCanvas
          v-model:components="pageComponents"
          :selected-component="selectedComponent"
          @component-select="handleComponentSelect"
          @component-update="handleComponentUpdate"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <PropertyPanel
          :component="selectedComponent"
          @component-update="handleComponentUpdate"
        />
      </div>
    </div>

    <!-- 预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="页面预览"
      width="90%"
      :footer="null"
      centered
    >
      <PreviewPanel v-if="previewVisible" :page-schema="currentPageSchema" />
    </a-modal>
  </div>
</template>

<style scoped>
/* 响应式调整 */
@media (max-width: 1200px) {
  .properties-panel {
    width: 280px;
  }

  .components-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .builder-content {
    flex-direction: column;
  }

  .components-panel,
  .properties-panel {
    width: 100%;
    height: 200px;
  }

  .canvas-area {
    flex: 1;
    min-height: 400px;
  }
}

.website-builder {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.builder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background: rgb(255 255 255 / 95%);
  border-bottom: 1px solid rgb(0 0 0 / 6%);
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  gap: 24px;
  align-items: center;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, #3498db, #8e44ad);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-left a {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  font-weight: 500;
  color: #3498db;
  text-decoration: none;
}

.header-left a:hover {
  color: #2980b9;
}

.header-right {
  display: flex;
  gap: 12px;
}

.builder-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.components-panel {
  width: 280px;
  overflow-y: auto;
  background: rgb(255 255 255 / 95%);
  border-right: 1px solid rgb(0 0 0 / 6%);
  backdrop-filter: blur(10px);
}

.canvas-area {
  position: relative;
  flex: 1;
  overflow: auto;
  background: #fff;
}

.properties-panel {
  width: 320px;
  overflow-y: auto;
  background: rgb(255 255 255 / 95%);
  border-left: 1px solid rgb(0 0 0 / 6%);
  backdrop-filter: blur(10px);
}
</style>
