<script setup lang="ts">
import type { PageSchema } from './schema/page-schema';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');
const selectedRowKeys = ref<string[]>([]);
const websiteId = ref<string>('');
const websiteName = ref<string>('');

// 模拟页面数据
const pages = ref<PageSchema[]>([
  {
    id: '1',
    title: '首页',
    slug: 'home',
    description: '网站首页',
    status: 'published',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: '1.0.0',
    seo: {
      title: '首页 - FlexiHub',
      description: '网站首页，欢迎访问FlexiHub',
      keywords: ['FlexiHub', '网站构建器', '首页'],
    },
    i18n: {
      defaultLocale: 'zh',
      locales: ['zh', 'en'],
      content: {},
    },
    layout: {
      type: 'default',
    },
    components: [],
  },
  {
    id: '2',
    title: '关于我们',
    slug: 'about',
    description: '公司介绍页面',
    status: 'draft',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: '1.0.0',
    seo: {
      title: '关于我们 - FlexiHub',
      description: '了解更多关于FlexiHub的信息',
      keywords: ['关于我们', '公司介绍', 'FlexiHub'],
    },
    i18n: {
      defaultLocale: 'zh',
      locales: ['zh', 'en'],
      content: {},
    },
    layout: {
      type: 'default',
    },
    components: [],
  },
]);

// 初始化
onMounted(() => {
  websiteId.value = route.params.websiteId as string;
  // 模拟根据websiteId获取网站名称
  if (websiteId.value === 'website-1') {
    websiteName.value = 'FlexiHub官网';
  } else if (websiteId.value === 'website-2') {
    websiteName.value = '企业展示站';
  } else {
    websiteName.value = '未知网站';
  }
});

// 计算属性
const filteredPages = computed(() => {
  if (!searchKeyword.value) {
    return pages.value;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return pages.value.filter(
    (page) =>
      page.title.toLowerCase().includes(keyword) ||
      page.description?.toLowerCase().includes(keyword),
  );
});

// 表格列配置
const columns = [
  {
    title: '页面标题',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '路径',
    dataIndex: 'slug',
    key: 'slug',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
];

// 事件处理
const handleCreate = () => {
  // 跳转到页面编辑器创建新页面
  router.push(`/website-builder/editor/${websiteId.value}`);
};

const handleEdit = (page: PageSchema) => {
  // 跳转到页面编辑器编辑页面
  router.push(`/website-builder/editor/${websiteId.value}/${page.id}`);
};

const handleBackToWebsites = () => {
  // 返回网站管理页面
  router.push('/website-builder/websites');
};

const handleDelete = (page: PageSchema) => {
  const index = pages.value.findIndex((p) => p.id === page.id);
  if (index !== -1) {
    pages.value.splice(index, 1);
    message.success('页面删除成功');
  }
};

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的页面');
    return;
  }
  message.success(`批量删除 ${selectedRowKeys.value.length} 个页面`);
  // TODO: 调用批量删除API
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    published: 'success',
    draft: 'default',
    archived: 'warning',
  };
  return statusMap[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档',
  };
  return statusMap[status] || status;
};
</script>

<template>
  <div class="page-management">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <a @click="handleBackToWebsites">
            <Icon icon="ic:baseline-web" />
            网站管理
          </a>
        </a-breadcrumb-item>
        <a-breadcrumb-item>
          <Icon icon="ic:baseline-pages" />
          {{ websiteName }} - 页面管理
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>页面列表</h1>
        </div>
        <div class="header-right">
          <a-button style="margin-right: 8px" @click="handleBackToWebsites">
            <Icon icon="ic:baseline-arrow-back" />
            返回网站管理
          </a-button>
          <a-button type="primary" @click="handleCreate">
            <Icon icon="ic:baseline-add" />
            新建页面
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <a-input
        v-model:value="searchKeyword"
        placeholder="页面标题"
        allow-clear
        style="width: 320px"
      >
        <template #prefix>
          <Icon icon="ic:baseline-search" />
        </template>
      </a-input>
      <a-button
        style="margin-left: 8px"
        danger
        :disabled="selectedRowKeys.length === 0"
        @click="handleBatchDelete"
      >
        <Icon icon="ic:baseline-delete" />
        删除
      </a-button>
    </div>

    <!-- 页面表格 -->
    <a-table
      :columns="columns"
      :data-source="filteredPages"
      :loading="loading"
      :pagination="{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }"
      :row-selection="{
        selectedRowKeys,
        onChange: (keys: string[]) => (selectedRowKeys = keys),
      }"
      row-key="id"
      size="middle"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'title'">
          <div class="page-info">
            <div class="page-name">{{ record.title }}</div>
            <div class="page-desc">{{ record.description }}</div>
          </div>
        </template>

        <template v-else-if="column.key === 'slug'">
          <a-tag>{{ record.slug }}</a-tag>
        </template>

        <template v-else-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <template v-else-if="column.key === 'updatedAt'">
          <span>{{ formatDate(record.updatedAt) }}</span>
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button size="small" type="primary" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button size="small" @click="message.info('预览页面')">
              预览
            </a-button>
            <a-popconfirm
              title="确定要删除这个页面吗？"
              @confirm="handleDelete(record)"
            >
              <a-button size="small" danger> 删除 </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped>
.page-management {
  min-height: 100vh;
  padding: 24px;
  background: #f0f2f5;
}

.breadcrumb-nav {
  padding: 12px 16px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);
}

.breadcrumb-nav a {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  font-weight: 500;
  color: #3498db;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  color: #2980b9;
}

.page-header {
  padding: 16px 24px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left h1 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);
}

.page-info {
  line-height: 1.4;
}

.page-name {
  font-weight: 500;
  color: #000;
}

.page-desc {
  margin-top: 2px;
  font-size: 12px;
  color: #666;
}
</style>
