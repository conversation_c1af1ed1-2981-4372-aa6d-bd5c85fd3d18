<script setup lang="ts">
import { ref } from 'vue';

import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 模拟模板数据
const templates = ref([
  {
    id: '1',
    name: '企业官网模板',
    description: '适合企业官网的简洁模板',
    thumbnail: '/api/placeholder/400/300',
    category: 'business',
    downloads: 1230,
    rating: 4.8,
  },
  {
    id: '2',
    name: '电商首页模板',
    description: '现代化的电商网站首页设计',
    thumbnail: '/api/placeholder/400/300',
    category: 'ecommerce',
    downloads: 856,
    rating: 4.6,
  },
]);

// 事件处理
const handleUse = (template: any) => {
  message.success(`使用模板: ${template.name}`);
  // TODO: 基于模板创建页面
};

const handlePreview = (template: any) => {
  message.info(`预览模板: ${template.name}`);
  // TODO: 打开模板预览
};
</script>

<template>
  <div class="template-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>模板管理</h2>
        <p>选择合适的模板快速创建页面</p>
      </div>
    </div>

    <!-- 模板网格 -->
    <div class="template-grid">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
      >
        <div class="template-thumbnail">
          <img :src="template.thumbnail" :alt="template.name" />
          <div class="template-actions">
            <a-button type="primary" @click="handleUse(template)">
              <Icon icon="ic:baseline-add" />
              使用模板
            </a-button>
            <a-button @click="handlePreview(template)">
              <Icon icon="ic:baseline-visibility" />
              预览
            </a-button>
          </div>
        </div>
        <div class="template-info">
          <h3>{{ template.name }}</h3>
          <p>{{ template.description }}</p>
          <div class="template-meta">
            <span>
              <Icon icon="ic:baseline-download" />
              {{ template.downloads }}
            </span>
            <span>
              <Icon icon="ic:baseline-star" />
              {{ template.rating }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="templates.length === 0" class="empty-state">
      <Icon icon="ic:baseline-layers" class="empty-icon" />
      <h3>暂无模板</h3>
      <p>模板功能正在开发中...</p>
    </div>
  </div>
</template>

<style scoped>
.template-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-left p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.template-card {
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: transform 0.2s ease;
}

.template-card:hover {
  box-shadow: 0 4px 16px rgb(0 0 0 / 15%);
  transform: translateY(-2px);
}

.template-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.template-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-actions {
  position: absolute;
  inset: 0;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  background-color: rgb(0 0 0 / 70%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.template-card:hover .template-actions {
  opacity: 1;
}

.template-info {
  padding: 16px;
}

.template-info h3 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.template-info p {
  margin: 0 0 12px;
  font-size: 14px;
  line-height: 1.4;
  color: #666;
}

.template-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.template-meta span {
  display: flex;
  gap: 4px;
  align-items: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
  color: #d9d9d9;
}

.empty-state h3 {
  margin: 0 0 8px;
  font-size: 18px;
  color: #666;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}
</style>
