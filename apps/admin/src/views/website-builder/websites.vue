<script setup lang="ts">
import type { Recordable } from '@vben/types';

import type { WebsiteSchema } from './schema/page-schema';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import WebsiteForm from './modules/website-form.vue';

const router = useRouter();

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: WebsiteForm,
  destroyOnClose: true,
});

// 模拟网站数据
const websiteData = ref<WebsiteSchema[]>([
  {
    id: 'website-1',
    name: 'FlexiHub官网',
    domain: 'flexihub.com',
    status: 1,
    createTime: '2024-01-01 12:00:00',
    globalSettings: {
      theme: { mode: 'light', primaryColor: '#1890ff' },
      fonts: ['Inter', 'PingFang SC'],
      colors: {
        primary: '#1890ff',
        secondary: '#722ed1',
        success: '#52c41a',
        warning: '#faad14',
        error: '#f5222d',
      },
      spacing: { xs: '4px', sm: '8px', md: '16px', lg: '24px', xl: '32px' },
      breakpoints: { mobile: 768, tablet: 1024, desktop: 1200 },
    },
    navigation: { header: [], footer: [] },
    pages: [
      {
        id: 'page-1',
        title: '首页',
        slug: 'home',
        description: '网站首页',
        status: 'published',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: '1.0.0',
        seo: { title: '首页 - FlexiHub' },
        i18n: { defaultLocale: 'zh', locales: ['zh', 'en'], content: {} },
        layout: { type: 'default' },
        components: [],
      },
    ],
  },
  {
    id: 'website-2',
    name: '企业展示站',
    domain: 'company.com',
    status: 1,
    createTime: '2024-01-02 12:00:00',
    globalSettings: {
      theme: { mode: 'light', primaryColor: '#722ed1' },
      fonts: ['Inter', 'PingFang SC'],
      colors: {
        primary: '#722ed1',
        secondary: '#1890ff',
        success: '#52c41a',
        warning: '#faad14',
        error: '#f5222d',
      },
      spacing: { xs: '4px', sm: '8px', md: '16px', lg: '24px', xl: '32px' },
      breakpoints: { mobile: 768, tablet: 1024, desktop: 1200 },
    },
    navigation: { header: [], footer: [] },
    pages: [],
  },
]);

// 表格列配置
function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'name',
      title: '网站名称',
      width: 200,
    },
    {
      field: 'domain',
      title: '域名',
      width: 200,
    },
    {
      field: 'pages',
      title: '页面数',
      width: 100,
      cellRender: {
        name: 'CellTag',
      },
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: 'CellSwitch',
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: '网站名称',
          onClick: onActionClick,
          options: [{ code: 'manage', text: '管理页面' }, 'edit', 'delete'],
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 200,
    },
  ];
}

// 表单搜索配置
function useGridFormSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '网站名称',
    },
    {
      component: 'Input',
      fieldName: 'domain',
      label: '域名',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
    },
  ];
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(),
    data: websiteData.value,
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async (_, formValues) => {
          // 模拟API调用
          let filteredData = [...websiteData.value];

          if (formValues.name) {
            filteredData = filteredData.filter((item) =>
              item.name.includes(formValues.name),
            );
          }

          if (formValues.domain) {
            filteredData = filteredData.filter((item) =>
              item.domain.includes(formValues.domain),
            );
          }

          if (formValues.status !== undefined) {
            filteredData = filteredData.filter(
              (item) => item.status === formValues.status,
            );
          }

          // 添加页面数统计
          const result = filteredData.map((item) => ({
            ...item,
            pages: item.pages.length,
          }));

          return {
            items: result,
            total: result.length,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<WebsiteSchema>,
});

function onActionClick(e: OnActionClickParams<WebsiteSchema>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
    case 'manage': {
      onManagePages(e.row);
      break;
    }
  }
}

function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

async function onStatusChange(newStatus: number, row: WebsiteSchema) {
  const status: Recordable<string> = {
    0: '禁用',
    1: '启用',
  };
  try {
    await confirm(
      `你要将${row.name}的状态切换为 【${status[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    // 更新本地数据
    const index = websiteData.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      websiteData.value[index].status = newStatus;
    }
    message.success('状态更新成功');
    return true;
  } catch {
    return false;
  }
}

function onEdit(row: WebsiteSchema) {
  formDrawerApi.setData(row).open();
}

function onManagePages(row: WebsiteSchema) {
  router.push(`/website-builder/pages/${row.id}`);
}

function onDelete(row: WebsiteSchema) {
  message.loading({
    content: `正在删除 ${row.name}`,
    duration: 0,
    key: 'action_process_msg',
  });

  setTimeout(() => {
    const index = websiteData.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      websiteData.value.splice(index, 1);
    }
    message.success({
      content: `删除 ${row.name} 成功`,
      key: 'action_process_msg',
    });
    onRefresh();
  }, 1000);
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid table-title="网站列表">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增网站
        </Button>
      </template>
    </Grid>
  </Page>
</template>
