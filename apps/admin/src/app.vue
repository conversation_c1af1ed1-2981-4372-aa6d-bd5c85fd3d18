<script lang="ts" setup>
import { computed } from 'vue';

import { useAntdDesignTokens } from '@vben/hooks';
import { preferences, usePreferences } from '@vben/preferences';

import { StagewiseToolbar } from '@stagewise/toolbar-vue';
import { App, ConfigProvider, theme } from 'ant-design-vue';

import { antdLocale } from '#/locales';

defineOptions({ name: 'App' });

const { isDark } = usePreferences();
const { tokens } = useAntdDesignTokens();

// Stagewise 开发工具配置
const stagewiseConfig = {
  plugins: [],
};

// 检查是否为开发模式
const isDevelopment = import.meta.env.MODE === 'development';

const tokenTheme = computed(() => {
  const algorithm = isDark.value
    ? [theme.darkAlgorithm]
    : [theme.defaultAlgorithm];

  // antd 紧凑模式算法
  if (preferences.app.compact) {
    algorithm.push(theme.compactAlgorithm);
  }

  return {
    algorithm,
    token: tokens,
  };
});
</script>

<template>
  <ConfigProvider :locale="antdLocale" :theme="tokenTheme">
    <App>
      <RouterView />

      <!-- Stagewise 开发工具栏 - 仅在开发模式显示 -->
      <StagewiseToolbar v-if="isDevelopment" :config="stagewiseConfig" />
    </App>
  </ConfigProvider>
</template>
