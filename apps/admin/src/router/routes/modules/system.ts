import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ion:settings-outline',
      order: 9997,
      title: $t('system.title'),
    },
    name: 'System',
    path: '/system',
    children: [
      {
        path: '/system/monitoring',
        name: 'SystemMonitoring',
        meta: {
          icon: 'mdi:monitor-dashboard',
          title: '系统监控',
        },
        component: () => import('#/views/system/monitoring/dashboard.vue'),
      },
      {
        path: '/system/cache',
        name: 'SystemCache',
        meta: {
          icon: 'mdi:cached',
          title: '缓存管理',
        },
        component: () => import('#/views/system/cache/dashboard.vue'),
      },
      {
        path: '/system/permission',
        name: 'SystemPermission',
        meta: {
          icon: 'mdi:shield-key',
          title: '权限管理',
        },
        component: () => import('#/views/system/permission/list.vue'),
      },
      {
        path: '/system/role',
        name: 'SystemR<PERSON>',
        meta: {
          icon: 'ic:baseline-assignment-ind',
          title: $t('system.role.title'),
        },
        component: () => import('#/views/system/role/list.vue'),
      },
      {
        path: '/system/menu',
        name: 'SystemMenu',
        meta: {
          icon: 'mdi:menu',
          title: $t('system.menu.title'),
        },
        component: () => import('#/views/system/menu/list.vue'),
      },
      {
        path: '/system/dept',
        name: 'SystemDept',
        meta: {
          icon: 'charm:organisation',
          title: $t('system.dept.title'),
        },
        component: () => import('#/views/system/dept/list.vue'),
      },
      {
        path: '/system/tenant',
        name: 'SystemTenant',
        meta: {
          icon: 'mdi:office-building',
          title: $t('system.tenant.title'),
        },
        component: () => import('#/views/system/tenant/index.vue'),
        redirect: '/system/tenant/list', // 默认重定向到列表页
        children: [
          {
            path: 'list',
            name: 'SystemTenantList',
            meta: {
              hideInMenu: true, // 在菜单中隐藏子路由
              title: $t('system.tenant.title'),
            },
            component: () => import('#/views/system/tenant/list.vue'),
          },
          {
            path: 'features',
            name: 'SystemTenantFeature',
            meta: {
              icon: 'mdi:feature-search',
              title: $t('system.tenantFeature.title'),
              hideInMenu: true, // 在菜单中隐藏
            },
            component: () => import('#/views/system/tenant-feature/list.vue'),
          },
          {
            path: 'configs',
            name: 'SystemTenantConfig',
            meta: {
              icon: 'mdi:cog',
              title: $t('system.tenantConfig.title'),
              hideInMenu: true, // 在菜单中隐藏
            },
            component: () => import('#/views/system/tenant-config/list.vue'),
          },
          {
            path: 'subscriptions',
            name: 'SystemTenantSubscription',
            meta: {
              icon: 'mdi:credit-card-multiple',
              title: '租户订阅管理',
              hideInMenu: true, // 在菜单中隐藏
            },
            component: () =>
              import('#/views/system/tenant-subscription/list.vue'),
          },
        ],
      },
      {
        path: '/system/user',
        name: 'SystemUser',
        meta: {
          icon: 'mdi:account-multiple',
          title: $t('system.user.title'),
        },
        component: () => import('#/views/system/user/list.vue'),
      },
      {
        path: '/system/feature-templates',
        name: 'SystemFeatureTemplate',
        meta: {
          icon: 'mdi:puzzle',
          title: $t('system.featureTemplate.title'),
        },
        component: () => import('#/views/system/feature-template/list.vue'),
      },
      {
        path: '/system/feature-codes',
        name: 'SystemFeatureCode',
        meta: {
          icon: 'mdi:code-tags',
          title: $t('system.featureCode.title'),
        },
        component: () => import('#/views/system/feature-code/list.vue'),
      },
      {
        path: '/system/membership-plans',
        name: 'SystemMembershipPlans',
        meta: {
          icon: 'mdi:credit-card-multiple',
          title: $t('system.membershipPlan.title'),
        },
        component: () => import('#/views/membership/plans/list.vue'),
      },
    ],
  },
  {
    meta: {
      icon: 'mdi:account-group',
      order: 9998,
      title: '会员管理',
    },
    name: 'Membership',
    path: '/membership',
    children: [
      {
        path: '/membership/plans',
        name: 'MembershipPlans',
        meta: {
          icon: 'mdi:package-variant',
          title: $t('system.membershipPlan.title'),
        },
        component: () => import('#/views/membership/plans/list.vue'),
      },
    ],
  },
];

export default routes;
