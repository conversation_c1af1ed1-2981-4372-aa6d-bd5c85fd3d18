import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:office-building-cog',
      order: 9996,
      title: $t('system.tenant.name'),
    },
    name: 'TenantManagement',
    path: '/tenant',
    children: [
      {
        path: '/tenant/features',
        name: 'TenantFeatures',
        meta: {
          icon: 'mdi:feature-search',
          title: $t('system.tenantFeature.dashboard'),
        },
        component: () => import('#/views/tenant/features/dashboard.vue'),
      },
      {
        path: '/tenant/configs',
        name: 'TenantConfigs',
        meta: {
          icon: 'mdi:cog-outline',
          title: $t('system.tenantConfig.configCenter'),
        },
        component: () => import('#/views/tenant/configs/index.vue'),
      },
    ],
  },
];

export default routes;
