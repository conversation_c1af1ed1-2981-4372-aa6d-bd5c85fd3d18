import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout',
      order: 1000,
      title: $t('page.website-builder.title'),
    },
    name: 'WebsiteBuilder',
    path: '/website-builder',
    redirect: '/website-builder/websites',
    children: [
      {
        name: 'WebsiteBuilderWebsites',
        path: '/website-builder/websites',
        component: () => import('#/views/website-builder/websites.vue'),
        meta: {
          icon: 'lucide:globe',
          title: $t('page.website-builder.websites'),
        },
      },
      {
        name: 'WebsiteBuilderPages',
        path: '/website-builder/pages/:websiteId',
        component: () => import('#/views/website-builder/pages.vue'),
        meta: {
          icon: 'lucide:file-text',
          title: $t('page.website-builder.pages'),
          hideInMenu: true,
        },
      },
      {
        name: 'WebsiteBuilderEditor',
        path: '/website-builder/editor/:websiteId/:pageId?',
        component: () => import('#/views/website-builder/editor.vue'),
        meta: {
          icon: 'lucide:palette',
          title: $t('page.website-builder.editor'),
          hideInMenu: true,
        },
      },
      {
        name: 'WebsiteBuilderTemplates',
        path: '/website-builder/templates',
        component: () => import('#/views/website-builder/templates.vue'),
        meta: {
          icon: 'lucide:layout-template',
          title: $t('page.website-builder.templates'),
        },
      },
    ],
  },
];

export default routes;
