/**
 * Website Builder API
 * 网站构建器相关的API接口
 */

import type {
  ComponentSchema,
  PageSchema,
  WebsiteSchema,
} from '#/views/website-builder/schema/page-schema';

import { requestClient } from '#/api/request';

// 页面管理API
export namespace PageAPI {
  // 获取页面列表
  export function getPageList(params?: {
    keyword?: string;
    page?: number;
    size?: number;
    status?: string;
  }) {
    return requestClient.get<{
      items: PageSchema[];
      total: number;
    }>('/api/website-builder/pages', { params });
  }

  // 获取页面详情
  export function getPage(id: string) {
    return requestClient.get<PageSchema>(`/api/website-builder/pages/${id}`);
  }

  // 创建页面
  export function createPage(data: Partial<PageSchema>) {
    return requestClient.post<PageSchema>('/api/website-builder/pages', data);
  }

  // 更新页面
  export function updatePage(id: string, data: Partial<PageSchema>) {
    return requestClient.put<PageSchema>(
      `/api/website-builder/pages/${id}`,
      data,
    );
  }

  // 删除页面
  export function deletePage(id: string) {
    return requestClient.delete(`/api/website-builder/pages/${id}`);
  }

  // 批量删除页面
  export function batchDeletePages(ids: string[]) {
    return requestClient.delete('/api/website-builder/pages/batch', {
      data: { ids },
    });
  }

  // 发布页面
  export function publishPage(id: string) {
    return requestClient.post(`/api/website-builder/pages/${id}/publish`);
  }

  // 取消发布页面
  export function unpublishPage(id: string) {
    return requestClient.post(`/api/website-builder/pages/${id}/unpublish`);
  }

  // 预览页面
  export function previewPage(id: string) {
    return requestClient.get(`/api/website-builder/pages/${id}/preview`);
  }
}

// 组件管理API
export namespace ComponentAPI {
  // 获取组件列表
  export function getComponentList(params?: {
    category?: string;
    keyword?: string;
  }) {
    return requestClient.get<ComponentSchema[]>(
      '/api/website-builder/components',
      { params },
    );
  }

  // 获取组件配置
  export function getComponentConfig(type: string) {
    return requestClient.get(`/api/website-builder/components/${type}/config`);
  }

  // 验证组件Schema
  export function validateComponent(data: ComponentSchema) {
    return requestClient.post('/api/website-builder/components/validate', data);
  }
}

// 模板管理API
export namespace TemplateAPI {
  // 获取模板列表
  export function getTemplateList(params?: {
    category?: string;
    keyword?: string;
    page?: number;
    size?: number;
  }) {
    return requestClient.get('/api/website-builder/templates', { params });
  }

  // 获取模板详情
  export function getTemplate(id: string) {
    return requestClient.get(`/api/website-builder/templates/${id}`);
  }

  // 基于模板创建页面
  export function createPageFromTemplate(
    templateId: string,
    data: {
      description?: string;
      slug: string;
      title: string;
    },
  ) {
    return requestClient.post<PageSchema>(
      `/api/website-builder/templates/${templateId}/create-page`,
      data,
    );
  }
}

// 网站管理API
export namespace WebsiteAPI {
  // 获取网站配置
  export function getWebsiteConfig() {
    return requestClient.get<WebsiteSchema>('/api/website-builder/website');
  }

  // 更新网站配置
  export function updateWebsiteConfig(data: Partial<WebsiteSchema>) {
    return requestClient.put<WebsiteSchema>(
      '/api/website-builder/website',
      data,
    );
  }

  // 获取网站统计信息
  export function getWebsiteStats() {
    return requestClient.get('/api/website-builder/website/stats');
  }
}

// 文件上传API
export namespace FileAPI {
  // 上传图片
  export function uploadImage(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return requestClient.post<{
      filename: string;
      size: number;
      url: string;
    }>('/api/website-builder/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // 上传媒体文件
  export function uploadMedia(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return requestClient.post<{
      filename: string;
      size: number;
      type: string;
      url: string;
    }>('/api/website-builder/upload/media', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

// 导出/导入API
export namespace ExportAPI {
  // 导出页面Schema
  export function exportPageSchema(id: string) {
    return requestClient.get(`/api/website-builder/export/page/${id}`, {
      responseType: 'blob',
    });
  }

  // 导入页面Schema
  export function importPageSchema(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return requestClient.post<PageSchema>(
      '/api/website-builder/import/page',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
  }

  // 导出整个网站
  export function exportWebsite() {
    return requestClient.get('/api/website-builder/export/website', {
      responseType: 'blob',
    });
  }
}
