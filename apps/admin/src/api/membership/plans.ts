import { requestClient } from '#/api/request';

export namespace MembershipPlanApi {
  export interface Plan {
    billingCycle: 'lifetime' | 'monthly' | 'quarterly' | 'yearly';
    code: string;
    createTime: string;
    description?: string;
    features: Record<string, any>;
    id: string;
    isActive: boolean;
    name: string;
    originalPrice?: number;
    price: number;
    sortOrder: number;
    tenantId: string;
    updateTime: string;
  }

  export interface PlanListParams {
    page?: number;
    pageSize?: number;
    status?: string;
  }

  export interface CreatePlanData {
    billingCycle: 'lifetime' | 'monthly' | 'quarterly' | 'yearly';
    code: string;
    description?: string;
    features: Record<string, any>;
    isActive: boolean;
    name: string;
    originalPrice?: number;
    price: number;
    sortOrder: number;
  }
}

/**
 * 获取会员计划列表
 */
async function getMembershipPlanList(
  params?: MembershipPlanApi.PlanListParams,
) {
  return requestClient.get<Array<MembershipPlanApi.Plan>>(
    '/membership/plans/list',
    { params },
  );
}

/**
 * 获取会员计划详情
 * @param id 计划ID
 */
async function getMembershipPlan(id: string) {
  return requestClient.get<MembershipPlanApi.Plan>(`/membership/plans/${id}`);
}

/**
 * 创建会员计划
 * @param data 计划数据
 */
async function createMembershipPlan(data: MembershipPlanApi.CreatePlanData) {
  return requestClient.post<MembershipPlanApi.Plan>('/membership/plans', data);
}

/**
 * 更新会员计划
 * @param id 计划ID
 * @param data 计划数据
 */
async function updateMembershipPlan(
  id: string,
  data: Partial<MembershipPlanApi.CreatePlanData>,
) {
  return requestClient.put<MembershipPlanApi.Plan>(
    `/membership/plans/${id}`,
    data,
  );
}

/**
 * 删除会员计划
 * @param id 计划ID
 */
async function deleteMembershipPlan(id: string) {
  return requestClient.delete(`/membership/plans/${id}`);
}

/**
 * 检查计划代码是否存在
 * @param code 计划代码
 * @param excludeId 排除的计划ID（编辑时使用）
 */
async function isPlanCodeExists(code: string, excludeId?: string) {
  const response = await requestClient.get<{ exists: boolean }>(
    '/membership/plans/code-exists',
    { params: { code, excludeId } },
  );
  return response.exists;
}

/**
 * 获取所有可用的计划类型
 */
async function getPlanTypes() {
  return [
    { label: '基础版', value: 'basic' },
    { label: '高级版', value: 'premium' },
    { label: '专业版', value: 'pro' },
    { label: '企业版', value: 'enterprise' },
  ];
}

export {
  createMembershipPlan,
  deleteMembershipPlan,
  getMembershipPlan,
  getMembershipPlanList,
  getPlanTypes,
  isPlanCodeExists,
  updateMembershipPlan,
};
