import { requestClient } from '#/api/request';

export namespace SystemPermissionApi {
  export interface Permission {
    code: string;
    createdAt: string;
    description?: string;
    id: string;
    name: string;
    status: 0 | 1;
    updatedAt: string;
  }

  export interface PermissionListParams {
    code?: string;
    name?: string;
    page?: number;
    pageSize?: number;
    status?: 0 | 1;
  }

  export interface RolePermission {
    permissionIds: string[];
    roleId: string;
  }
}

/**
 * 获取权限列表
 */
async function getPermissionList(
  params?: SystemPermissionApi.PermissionListParams,
) {
  return requestClient.get<{
    items: Array<SystemPermissionApi.Permission>;
    page: number;
    pageSize: number;
    total: number;
  }>('/system/permission/list', { params });
}

/**
 * 获取权限详情
 * @param id 权限ID
 */
async function getPermission(id: string) {
  return requestClient.get<SystemPermissionApi.Permission>(
    `/system/permission/${id}`,
  );
}

/**
 * 创建权限
 * @param data 权限数据
 */
async function createPermission(
  data: Omit<SystemPermissionApi.Permission, 'createdAt' | 'id' | 'updatedAt'>,
) {
  return requestClient.post<SystemPermissionApi.Permission>(
    '/system/permission',
    data,
  );
}

/**
 * 更新权限
 * @param id 权限ID
 * @param data 权限数据
 */
async function updatePermission(
  id: string,
  data: Omit<SystemPermissionApi.Permission, 'createdAt' | 'id' | 'updatedAt'>,
) {
  return requestClient.put<SystemPermissionApi.Permission>(
    `/system/permission/${id}`,
    data,
  );
}

/**
 * 删除权限
 * @param id 权限ID
 */
async function deletePermission(id: string) {
  return requestClient.delete(`/system/permission/${id}`);
}

/**
 * 检查权限码是否存在
 * @param code 权限码
 * @param id 排除的权限ID（用于更新时检查）
 */
async function isPermissionCodeExists(code: string, id?: string) {
  const response = await requestClient.get<{ exists: boolean }>(
    '/system/permission/code-exists',
    {
      params: { code, id },
    },
  );
  return response.exists;
}

/**
 * 获取角色的权限列表
 * @param roleId 角色ID
 */
async function getRolePermissions(roleId: string) {
  return requestClient.get<Array<SystemPermissionApi.Permission>>(
    `/system/permission/roles/${roleId}`,
  );
}

/**
 * 分配权限给角色
 * @param roleId 角色ID
 * @param permissionIds 权限ID列表
 */
async function assignPermissionsToRole(
  roleId: string,
  permissionIds: string[],
) {
  return requestClient.post('/system/permission/assign', {
    roleId,
    permissionIds,
  });
}

/**
 * 移除角色的权限
 * @param roleId 角色ID
 * @param permissionId 权限ID
 */
async function removeRolePermission(roleId: string, permissionId: string) {
  return requestClient.delete(
    `/system/permission/roles/${roleId}/permissions/${permissionId}`,
  );
}

export {
  assignPermissionsToRole,
  createPermission,
  deletePermission,
  getPermission,
  getPermissionList,
  getRolePermissions,
  isPermissionCodeExists,
  removeRolePermission,
  updatePermission,
};
