import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace TenantFeatureApi {
  export interface TenantFeature {
    config: Recordable<any>;
    createdAt?: string;
    enabled: boolean;
    expiresAt: null | string;
    featureCode: string;
    id: number;
    quota: null | number;
    tenantId: number;
    updatedAt?: string;
    usedQuota: number;
  }

  export interface TenantFeatureConfig {
    [key: string]: any;
    expiresAt?: null | string;
    quota?: null | number;
  }

  export interface MyFeature {
    code: string;
    config: Recordable<any>;
    enabled: boolean;
    expiresAt: null | string;
    quota: null | number;
    remaining: null | number;
    usedQuota: number;
  }
}

/**
 * 获取租户功能列表
 * @param tenantId 租户ID
 */
async function getTenantFeatureList(tenantId: number) {
  return requestClient.get<Array<TenantFeatureApi.TenantFeature>>(
    '/system/tenant-features/list',
    { params: { tenantId } },
  );
}

/**
 * 启用功能
 * @param tenantId 租户ID
 * @param featureCode 功能代码
 * @param config 功能配置
 */
async function enableTenantFeature(
  tenantId: number,
  featureCode: string,
  config?: TenantFeatureApi.TenantFeatureConfig,
) {
  return requestClient.post('/system/tenant-features/enable', {
    tenantId,
    featureCode,
    config,
  });
}

/**
 * 禁用功能
 * @param tenantId 租户ID
 * @param featureCode 功能代码
 */
async function disableTenantFeature(tenantId: number, featureCode: string) {
  return requestClient.post('/system/tenant-features/disable', {
    tenantId,
    featureCode,
  });
}

/**
 * 应用功能模板
 * @param tenantId 租户ID
 * @param templateCode 模板代码
 */
async function applyFeatureTemplate(tenantId: number, templateCode: string) {
  return requestClient.post('/system/tenant-features/apply-template', {
    tenantId,
    templateCode,
  });
}

/**
 * 获取当前租户功能
 */
async function getMyFeatures() {
  return requestClient.get<Array<TenantFeatureApi.MyFeature>>(
    '/tenant/features/my-features',
  );
}

/**
 * 获取功能配置
 * @param featureCode 功能代码
 */
async function getFeatureConfig(featureCode: string) {
  return requestClient.get<Recordable<any>>(
    `/tenant/features/config/${featureCode}`,
  );
}

export {
  applyFeatureTemplate,
  disableTenantFeature,
  enableTenantFeature,
  getFeatureConfig,
  getMyFeatures,
  getTenantFeatureList,
};
