import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace SystemUserApi {
  export interface Role {
    code: string;
    id: string;
    name: string;
  }

  export interface SystemUser {
    [key: string]: any;
    adminRemark?: string;
    avatar?: string;
    createTime?: string;
    email?: string;
    id: number;
    idCardNumber?: string;
    lastLoginTime?: string;
    openid?: string;
    phoneNumber?: string;
    realName: string;
    roleIds?: string[];
    roles?: Role[];
    status: 0 | 1;
    tenantId?: string;
    unionid?: string;
    updateTime?: string;
    username: string;
  }
}

/**
 * 获取用户列表
 */
async function getUserList(params: Recordable<any>) {
  const result = await requestClient.get<{
    items: Array<SystemUserApi.SystemUser>;
    page: number;
    pageSize: number;
    total: number;
  }>('/users/list', { params });

  return result;
}

/**
 * 获取用户详情
 * @param id 用户ID
 */
async function getUserDetail(id: number) {
  try {
    const result = await requestClient.get<SystemUserApi.SystemUser>(
      `/users/${id}`,
    );
    return result;
  } catch (error) {
    console.error('获取用户详情失败:', error);
    throw error;
  }
}

/**
 * 创建用户
 * @param data 用户数据
 */
async function createUser(data: Omit<SystemUserApi.SystemUser, 'id'>) {
  return requestClient.post<SystemUserApi.SystemUser>('/users', data);
}

/**
 * 更新用户
 * @param id 用户ID
 * @param data 用户数据
 */
async function updateUser(
  id: number,
  data: Partial<Omit<SystemUserApi.SystemUser, 'id'>>,
) {
  try {
    // 移除不需要的字段
    const cleanData = { ...data };
    delete cleanData.id;
    delete cleanData.createTime;
    delete cleanData.updateTime;
    delete cleanData.lastLoginTime;
    delete cleanData.roles;
    // 确保移除密码字段，防止意外提交（除非明确要修改密码）
    if (cleanData.password === undefined || cleanData.password === '') {
      delete cleanData.password;
    }

    // 使用 put 方法替代 patch，因为 RequestClient 类型上不存在 patch 方法
    const result = await requestClient.put<SystemUserApi.SystemUser>(
      `/users/${id}`,
      cleanData,
    );

    return result;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
}

/**
 * 删除用户
 * @param id 用户ID
 */
async function deleteUser(id: number) {
  return requestClient.delete<{ success: boolean }>(`/users/${id}`);
}

/**
 * 获取当前用户信息
 */
async function getCurrentUserInfo() {
  return requestClient.get<SystemUserApi.SystemUser>('/users/info');
}

export {
  createUser,
  deleteUser,
  getCurrentUserInfo,
  getUserDetail,
  getUserList,
  updateUser,
};
