import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace TenantConfigApi {
  export interface ConfigCategory {
    code: string;
    description: string;
    name: string;
    requiredFeature: string;
  }
}

/**
 * 获取所有配置类别
 */
async function getConfigCategories() {
  return requestClient.get<Array<TenantConfigApi.ConfigCategory>>(
    '/system/tenant-configs/categories',
  );
}

/**
 * 获取租户配置
 * @param category 配置类别
 * @param tenantId 租户ID（系统管理员必填，租户管理员忽略）
 */
async function getTenantConfig(category: string, tenantId?: number) {
  const params: Recordable<any> = {};
  if (tenantId) {
    params.tenantId = tenantId;
  }
  return requestClient.get<Recordable<any>>(
    `/system/tenant-configs/${category}`,
    { params },
  );
}

/**
 * 更新租户配置
 * @param category 配置类别
 * @param data 配置数据
 * @param tenantId 租户ID（系统管理员必填，租户管理员忽略）
 */
async function updateTenantConfig(
  category: string,
  data: Recordable<any>,
  tenantId?: number,
) {
  const params: Recordable<any> = {};
  if (tenantId) {
    params.tenantId = tenantId;
  }
  return requestClient.post(`/system/tenant-configs/${category}`, data, {
    params,
  });
}

/**
 * 测试租户配置
 * @param category 配置类别
 * @param data 配置数据
 * @param tenantId 租户ID（系统管理员必填，租户管理员忽略）
 */
async function testTenantConfig(
  category: string,
  data: Recordable<any>,
  tenantId?: number,
) {
  const params: Recordable<any> = {};
  if (tenantId) {
    params.tenantId = tenantId;
  }
  return requestClient.post(`/system/tenant-configs/${category}/test`, data, {
    params,
  });
}

export {
  getConfigCategories,
  getTenantConfig,
  testTenantConfig,
  updateTenantConfig,
};
