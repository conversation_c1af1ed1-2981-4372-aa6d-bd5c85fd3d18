import { requestClient } from '#/api/request';

export namespace SystemCacheApi {
  export interface CacheMetrics {
    hit: number;
    hitRate: number;
    miss: number;
    total: number;
  }

  export interface CacheInfo {
    connectionsActive: number;
    connectionsTotal: number;
    keys: number;
    memoryUsage: number;
    uptime: number;
  }

  export interface CacheKey {
    expireTime?: string;
    key: string;
    size: number;
    ttl: number;
    type: string;
  }

  export interface DashboardData {
    charts: {
      hitRate: Array<{ time: string; value: number }>;
      memoryUsage: Array<{ time: string; value: number }>;
      operations: Array<{ time: string; value: number }>;
    };
    info: CacheInfo;
    metrics: CacheMetrics;
    recentKeys: CacheKey[];
  }
}

/**
 * 获取缓存指标
 */
async function getCacheMetrics() {
  return requestClient.get<SystemCacheApi.CacheMetrics>(
    '/system/cache/metrics',
  );
}

/**
 * 获取缓存监控面板数据
 */
async function getCacheDashboard() {
  return requestClient.get<SystemCacheApi.DashboardData>(
    '/system/cache/dashboard',
  );
}

/**
 * 清空所有缓存
 */
async function clearAllCache() {
  return requestClient.delete('/system/cache/clear');
}

/**
 * 删除指定键的缓存
 * @param key 缓存键
 */
async function deleteCacheKey(key: string) {
  return requestClient.delete(`/system/cache/key/${encodeURIComponent(key)}`);
}

/**
 * 使用模式删除多个缓存
 * @param pattern 匹配模式
 */
async function deleteCacheByPattern(pattern: string) {
  return requestClient.delete('/system/cache/pattern', {
    data: { pattern },
  });
}

/**
 * 清除所有菜单缓存
 */
async function clearMenuCache() {
  return requestClient.delete('/system/cache/menu');
}

/**
 * 获取缓存键列表
 * @param params - 查询参数
 * @param params.pattern - 匹配模式
 * @param params.page - 页码
 * @param params.pageSize - 页面大小
 */
async function getCacheKeys(params?: {
  page?: number;
  pageSize?: number;
  pattern?: string;
}) {
  return requestClient.get<{
    items: Array<SystemCacheApi.CacheKey>;
    page: number;
    pageSize: number;
    total: number;
  }>('/system/cache/keys', { params });
}

export {
  clearAllCache,
  clearMenuCache,
  deleteCacheByPattern,
  deleteCacheKey,
  getCacheDashboard,
  getCacheKeys,
  getCacheMetrics,
};
