import { requestClient } from '#/api/request';

export namespace FeatureMenuApi {
  /**
   * 功能代码关联的菜单项
   */
  export interface FeatureMenu {
    /**
     * 创建时间
     */
    createdAt: string;
    /**
     * 功能代码
     */
    featureCode: string;
    /**
     * 主键ID
     */
    id: number;
    /**
     * 菜单ID
     */
    menuId: number;
    /**
     * 菜单名称
     */
    menuName: string;
    /**
     * 菜单路径
     */
    menuPath: string;
    /**
     * 更新时间
     */
    updatedAt: string;
  }

  /**
   * 菜单项
   */
  export interface MenuItem {
    /**
     * 子菜单
     */
    children?: MenuItem[];
    /**
     * 组件
     */
    component?: string;
    /**
     * 关联的功能代码
     */
    featureCodes?: string[];
    /**
     * 菜单ID
     */
    id: number;
    /**
     * 元数据
     */
    meta?: {
      [key: string]: any;
      /**
       * 图标
       */
      icon?: string;
      /**
       * 标题
       */
      title: string;
    };
    /**
     * 菜单名称
     */
    name: string;
    /**
     * 菜单路径
     */
    path: string;
  }

  /**
   * 租户菜单项
   */
  export interface TenantMenuItem {
    /**
     * 是否启用
     */
    enabled: boolean;
    /**
     * 功能代码
     */
    featureCode: string;
    /**
     * 主键ID
     */
    id: number;
    /**
     * 菜单ID
     */
    menuId: number;
    /**
     * 菜单名称
     */
    menuName: string;
    /**
     * 菜单路径
     */
    menuPath: string;
  }
}

/**
 * 获取功能代码关联的菜单
 * @param code 功能代码
 */
export async function getFeatureCodeMenus(code: string) {
  return requestClient.get<FeatureMenuApi.FeatureMenu[]>(
    `/system/feature-codes/${code}/menus`,
  );
}

/**
 * 设置功能代码关联的菜单
 * @param code 功能代码
 * @param menuIds 菜单ID列表
 */
export async function setFeatureCodeMenus(code: string, menuIds: number[]) {
  return requestClient.post(`/system/feature-codes/${code}/menus`, {
    menuIds,
  });
}

/**
 * 获取功能模板关联的菜单
 * @param code 功能模板代码
 */
export async function getFeatureTemplateMenus(code: string) {
  return requestClient.get<FeatureMenuApi.MenuItem[]>(
    `/system/feature-templates/${code}/menus`,
  );
}

/**
 * 获取租户可用菜单
 * @param tenantId 租户ID
 */
export async function getTenantMenus(tenantId: number) {
  return requestClient.get<FeatureMenuApi.TenantMenuItem[]>(
    `/system/tenant-features/${tenantId}/menus`,
  );
}

/**
 * 同步租户菜单
 * @param tenantId 租户ID
 */
export async function syncTenantMenus(tenantId: number) {
  return requestClient.post(`/system/tenant-features/${tenantId}/sync-menus`);
}
