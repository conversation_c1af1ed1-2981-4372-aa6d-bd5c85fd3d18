import { requestClient } from '#/api/request';

export namespace FeatureTemplateApi {
  export interface FeatureConfig {
    [key: string]: any;
    enabled: boolean;
    quota?: null | number;
  }

  export interface FeatureTemplate {
    code: string;
    createdAt?: string;
    features: Record<string, FeatureConfig>;
    id: number;
    isActive: boolean;
    name: string;
    updatedAt?: string;
  }
}

/**
 * 获取所有功能模板
 */
async function getFeatureTemplateList() {
  try {
    const result = await requestClient.get<
      Array<FeatureTemplateApi.FeatureTemplate>
    >('/system/feature-templates/list');

    return result;
  } catch (error) {
    console.error('获取功能模板列表失败:', error);
    return [];
  }
}

/**
 * 获取功能模板详情
 * @param code 模板代码
 */
async function getFeatureTemplateDetail(code: string) {
  return requestClient.get<FeatureTemplateApi.FeatureTemplate>(
    `/system/feature-templates/${code}`,
  );
}

/**
 * 创建或更新功能模板
 * @param data 模板数据
 */
async function upsertFeatureTemplate(
  data: Omit<
    FeatureTemplateApi.FeatureTemplate,
    'createdAt' | 'id' | 'updatedAt'
  >,
) {
  return requestClient.post<FeatureTemplateApi.FeatureTemplate>(
    '/system/feature-templates/upsert',
    data,
  );
}

/**
 * 删除功能模板
 * @param code 模板代码
 */
async function deleteFeatureTemplate(code: string) {
  return requestClient.delete(`/system/feature-templates/${code}`);
}

export {
  deleteFeatureTemplate,
  getFeatureTemplateDetail,
  getFeatureTemplateList,
  upsertFeatureTemplate,
};
