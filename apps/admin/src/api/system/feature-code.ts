import { requestClient } from '#/api/request';

export namespace FeatureCodeApi {
  /**
   * 功能代码
   */
  export interface FeatureCode {
    /**
     * 功能代码，唯一标识
     */
    code: string;
    /**
     * 创建时间
     */
    createdAt: string;
    /**
     * 功能描述
     */
    description: string;
    /**
     * 主键ID
     */
    id: number;
    /**
     * 是否激活
     */
    isActive: boolean;
    /**
     * 额外元数据
     */
    metadata: Record<string, any>;
    /**
     * 所属模块
     */
    module: string;
    /**
     * 功能名称
     */
    name: string;
    /**
     * 排序顺序
     */
    sortOrder: number;
    /**
     * 更新时间
     */
    updatedAt: string;
  }

  /**
   * 创建或更新功能代码的请求参数
   */
  export interface UpsertFeatureCodeRequest {
    /**
     * 功能代码，唯一标识
     */
    code: string;
    /**
     * 功能描述
     */
    description: string;
    /**
     * 是否激活
     */
    isActive?: boolean;
    /**
     * 额外元数据
     */
    metadata?: Record<string, any>;
    /**
     * 所属模块
     */
    module: string;
    /**
     * 功能名称
     */
    name: string;
    /**
     * 排序顺序
     */
    sortOrder?: number;
  }
}

/**
 * 获取功能代码列表
 * @param params 查询参数
 * @param params.forceRefresh 是否强制刷新缓存
 * @param params.includeInactive 是否包含未激活的功能代码
 * @returns 功能代码列表
 */
export async function getFeatureCodeList(params?: {
  forceRefresh?: boolean;
  includeInactive?: boolean;
}): Promise<FeatureCodeApi.FeatureCode[]> {
  try {
    const result = await requestClient.get<FeatureCodeApi.FeatureCode[]>(
      '/system/feature-codes/list',
      { params },
    );
    return result;
  } catch (error) {
    console.error('获取功能代码列表失败:', error);
    return [];
  }
}

/**
 * 获取功能代码详情
 * @param code 功能代码
 * @returns 功能代码详情
 */
export async function getFeatureCodeDetail(
  code: string,
): Promise<FeatureCodeApi.FeatureCode | null> {
  try {
    const result = await requestClient.get<FeatureCodeApi.FeatureCode>(
      `/system/feature-codes/${code}`,
    );
    return result;
  } catch (error) {
    console.error('获取功能代码详情失败:', error);
    return null;
  }
}

/**
 * 创建或更新功能代码
 * @param data 功能代码数据
 * @returns 创建或更新后的功能代码
 */
export async function upsertFeatureCode(
  data: FeatureCodeApi.UpsertFeatureCodeRequest,
): Promise<FeatureCodeApi.FeatureCode> {
  return await requestClient.post<FeatureCodeApi.FeatureCode>(
    '/system/feature-codes/upsert',
    data,
  );
}

/**
 * 删除功能代码
 * @param code 功能代码
 */
export async function deleteFeatureCode(code: string): Promise<void> {
  await requestClient.delete(`/system/feature-codes/${code}`);
}
