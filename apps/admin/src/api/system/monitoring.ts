import { requestClient } from '#/api/request';

export namespace SystemMonitoringApi {
  export interface HealthStatus {
    details: Record<string, any>;
    error: Record<string, any>;
    info: Record<string, any>;
    status: 'error' | 'ok' | 'warning';
  }

  export interface SystemMetrics {
    cpu: {
      cores: number;
      loadAverage: number[];
      usage: number;
    };
    database: {
      activeConnections: number;
      connections: number;
      maxConnections: number;
    };
    disk: {
      free: number;
      total: number;
      usage: number;
      used: number;
    };
    memory: {
      free: number;
      total: number;
      usage: number;
      used: number;
    };
    network: {
      bytesIn: number;
      bytesOut: number;
      packetsIn: number;
      packetsOut: number;
    };
    redis: {
      connections: number;
      keyspaceHits: number;
      keyspaceMisses: number;
      memoryUsage: number;
    };
  }

  export interface DashboardData {
    alerts: Array<{
      id: string;
      message: string;
      timestamp: string;
      type: 'error' | 'info' | 'warning';
    }>;
    charts: {
      cpuUsage: Array<{ time: string; value: number }>;
      errorCount: Array<{ time: string; value: number }>;
      memoryUsage: Array<{ time: string; value: number }>;
      requestCount: Array<{ time: string; value: number }>;
    };
    overview: {
      activeTenants: number;
      todayVisits: number;
      totalPages: number;
      totalUsers: number;
    };
    performance: {
      errorRate: number;
      responseTime: number;
      throughput: number;
      uptime: number;
    };
  }
}

/**
 * 获取系统健康状态
 */
async function getHealthStatus() {
  return requestClient.get<SystemMonitoringApi.HealthStatus>('/health');
}

/**
 * Ping检查
 */
async function pingCheck() {
  return requestClient.get<{ status: string; timestamp: string }>(
    '/health/ping',
  );
}

/**
 * 获取系统指标
 */
async function getSystemMetrics() {
  return requestClient.get<SystemMonitoringApi.SystemMetrics>(
    '/monitoring/metrics',
  );
}

/**
 * 获取监控仪表板数据
 */
async function getMonitoringDashboard() {
  return requestClient.get<SystemMonitoringApi.DashboardData>(
    '/monitoring/dashboard',
  );
}

export { getHealthStatus, getMonitoringDashboard, getSystemMetrics, pingCheck };
