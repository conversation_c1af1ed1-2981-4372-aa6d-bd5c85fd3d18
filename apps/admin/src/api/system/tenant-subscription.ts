import { requestClient } from '#/api/request';

export namespace TenantSubscriptionApi {
  export interface Subscription {
    autoRenew: boolean;
    billingCycle: string;
    createTime: string;
    duration: number;
    endDate: string;
    id: string;
    metadata: Record<string, any>;
    planCode: string;
    planId: string;
    planName: string;
    startDate: string;
    status: string;
    tenantId: string;
    tenantName: string;
    updateTime: string;
  }

  export interface CreateSubscriptionData {
    autoRenew: boolean;
    billingCycle: string;
    duration: number;
    endDate: string;
    metadata: Record<string, any>;
    planId: string;
    startDate: string;
    tenantId: string;
  }

  export interface SubscriptionListParams {
    page?: number;
    pageSize?: number;
    planId?: string;
    status?: string;
    tenantId?: string;
  }

  export interface SubscriptionPlan {
    code: string;
    description: string;
    features: string[];
    id: string;
    isActive: boolean;
    limitations: Record<string, any>;
    name: string;
    price: number;
    type: string;
  }
}

/**
 * 获取租户订阅列表
 */
async function getTenantSubscriptionList(
  params?: TenantSubscriptionApi.SubscriptionListParams,
) {
  return requestClient.get<Array<TenantSubscriptionApi.Subscription>>(
    '/api/system/tenant-subscription/subscriptions',
    { params },
  );
}

/**
 * 获取租户订阅详情
 */
async function getTenantSubscription(id: string) {
  return requestClient.get<TenantSubscriptionApi.Subscription>(
    `/api/system/tenant-subscription/subscriptions/${id}`,
  );
}

/**
 * 创建租户订阅
 */
async function createTenantSubscription(
  data: TenantSubscriptionApi.CreateSubscriptionData,
) {
  return requestClient.post<TenantSubscriptionApi.Subscription>(
    '/api/system/tenant-subscription/subscriptions',
    data,
  );
}

/**
 * 更新租户订阅
 */
async function updateTenantSubscription(
  id: string,
  data: Partial<TenantSubscriptionApi.CreateSubscriptionData>,
) {
  return requestClient.put<TenantSubscriptionApi.Subscription>(
    `/api/system/tenant-subscription/subscriptions/${id}`,
    data,
  );
}

/**
 * 删除租户订阅
 */
async function deleteTenantSubscription(id: string) {
  return requestClient.delete(
    `/api/system/tenant-subscription/subscriptions/${id}`,
  );
}

/**
 * 获取订阅统计信息
 */
async function getSubscriptionStats() {
  return requestClient.get<{
    active: number;
    cancelled: number;
    expired: number;
    total: number;
  }>('/api/system/tenant-subscription/stats');
}

/**
 * 获取租户订阅历史
 */
async function getTenantSubscriptionHistory(tenantId: string) {
  return requestClient.get<Array<TenantSubscriptionApi.Subscription>>(
    `/api/system/tenant-subscription/tenant/${tenantId}/history`,
  );
}

/**
 * 获取租户当前活跃订阅
 */
async function getTenantActiveSubscription(tenantId: string) {
  return requestClient.get<TenantSubscriptionApi.Subscription>(
    `/api/system/tenant-subscription/tenant/${tenantId}/active`,
  );
}

/**
 * 续费租户订阅
 */
async function renewTenantSubscription(
  id: string,
  data: {
    duration: number;
    endDate: string;
  },
) {
  return requestClient.post<TenantSubscriptionApi.Subscription>(
    `/api/system/tenant-subscription/subscriptions/${id}/renew`,
    data,
  );
}

export {
  createTenantSubscription,
  deleteTenantSubscription,
  getSubscriptionStats,
  getTenantActiveSubscription,
  getTenantSubscription,
  getTenantSubscriptionHistory,
  getTenantSubscriptionList,
  renewTenantSubscription,
  updateTenantSubscription,
};
