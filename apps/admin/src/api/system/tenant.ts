import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace SystemTenantApi {
  export interface Datasource {
    createTime?: string;
    id: number;
    metadata?: Recordable<any>;
    name: string;
    updateTime?: string;
    url: string;
  }

  export interface SystemTenant {
    [key: string]: any;
    code: string;
    createTime?: string;
    datasource?: Datasource;
    domain?: string;
    id: number;
    metadata?: Recordable<any>;
    name: string;
    status: 0 | 1;
    updateTime?: string;
    website?: string;
  }
}

/**
 * 获取租户列表数据
 */
async function getTenantList(params: Recordable<any>) {
  try {
    const result = await requestClient.get<{
      items: Array<SystemTenantApi.SystemTenant>;
      page: number;
      pageSize: number;
      total: number;
    }>('/tenant/list', { params });
    return result;
  } catch (error) {
    console.error('租户列表请求失败:', error);
    throw error;
  }
}

/**
 * 获取租户详情
 * @param id 租户ID
 */
async function getTenantDetail(id: number) {
  return requestClient.get<SystemTenantApi.SystemTenant>(`/tenant/${id}`);
}

/**
 * 创建租户
 * @param data 租户数据
 */
async function createTenant(data: Omit<SystemTenantApi.SystemTenant, 'id'>) {
  return requestClient.post('/tenant', data);
}

/**
 * 更新租户
 * @param id 租户ID
 * @param data 租户数据
 */
async function updateTenant(
  id: number,
  data: Partial<Omit<SystemTenantApi.SystemTenant, 'id'>>,
) {
  return requestClient.put(`/tenant/${id}`, data);
}

/**
 * 删除租户
 * @param id 租户ID
 */
async function deleteTenant(id: number) {
  return requestClient.delete(`/tenant/${id}`);
}

/**
 * 更新租户状态
 * @param id 租户ID
 * @param status 状态值
 */
async function updateTenantStatus(id: number, status: 0 | 1) {
  return requestClient.put(`/tenant/${id}/status`, { status });
}

/**
 * 检查租户代码是否存在
 * @param code 租户代码
 * @param id 排除的租户ID
 */
async function isTenantCodeExists(code: string, id?: number) {
  return requestClient.get<boolean>('/tenant/code-exists', {
    params: { code, id },
  });
}

/**
 * 获取数据源列表
 */
async function getDatasourceList(params: Recordable<any>) {
  return requestClient.get<{
    items: Array<SystemTenantApi.Datasource>;
    page: number;
    pageSize: number;
    total: number;
  }>('/datasource/list', { params });
}

/**
 * 创建数据源
 * @param data 数据源数据
 */
async function createDatasource(data: Omit<SystemTenantApi.Datasource, 'id'>) {
  return requestClient.post('/datasource', data);
}

/**
 * 更新数据源
 * @param id 数据源ID
 * @param data 数据源数据
 */
async function updateDatasource(
  id: number,
  data: Partial<Omit<SystemTenantApi.Datasource, 'id'>>,
) {
  return requestClient.put(`/datasource/${id}`, data);
}

/**
 * 删除数据源
 * @param id 数据源ID
 */
async function deleteDatasource(id: number) {
  return requestClient.delete(`/datasource/${id}`);
}

/**
 * 测试数据源连接
 * @param url 数据源URL
 */
async function testDatasourceConnection(url: string) {
  try {
    const result = await requestClient.post<{
      message: string;
      success: boolean;
    }>('/datasource/test-connection', { url });

    // 根据后端返回的数据结构进行处理
    // 如果后端直接返回 { success: boolean, message: string } 则直接返回
    // 如果后端返回嵌套结构，则提取内部数据
    if (typeof result === 'object' && result !== null) {
      if ('success' in result && 'message' in result) {
        return result as { message: string; success: boolean };
      } else if (result.data && typeof result.data === 'object') {
        return result.data as { message: string; success: boolean };
      }
    }

    // 默认返回成功
    return { success: true, message: '连接成功' };
  } catch (error) {
    console.error('测试数据源连接失败:', error);
    // 返回错误信息
    return {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
    };
  }
}

export {
  createDatasource,
  createTenant,
  deleteDatasource,
  deleteTenant,
  getDatasourceList,
  getTenantDetail,
  getTenantList,
  isTenantCodeExists,
  testDatasourceConnection,
  updateDatasource,
  updateTenant,
  updateTenantStatus,
};
