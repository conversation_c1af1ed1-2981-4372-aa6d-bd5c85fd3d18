<script setup lang="ts">
import type { StyleConfig } from '../../../views/website-builder/schema/page-schema';

import { computed, useSlots } from 'vue';

// 定义组件Props
interface Props {
  backgroundImage?: string;
  className?: string;

  // 容器属性
  fluid?: boolean;
  // 基础属性
  id?: string;
  maxWidth?: number | string;
  // 事件属性
  onClick?: () => void;

  padding?: number | string;
  // 响应式属性
  responsive?: Record<string, any>;

  // 样式属性
  styles?: StyleConfig;

  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  backgroundImage: undefined,
  className: undefined,
  fluid: false,
  id: undefined,
  maxWidth: undefined,
  onClick: undefined,
  padding: undefined,
  responsive: undefined,
  styles: undefined,
  type: 'container',
});

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

// 获取slots
const slots = useSlots();

// 计算属性
const componentId = computed(() => props.id || `container-${Date.now()}`);

const hasChildren = computed(() => {
  return slots.default && slots.default().length > 0;
});

const computedClasses = computed(() => {
  const classes: string[] = ['website-container-component'];

  if (props.className) {
    classes.push(props.className);
  }

  if (props.fluid) {
    classes.push('fluid');
  } else {
    classes.push('constrained');
  }

  if (!hasChildren.value) {
    classes.push('empty');
  }

  return classes;
});

const computedStyles = computed(() => {
  const styles: Record<string, any> = {};

  // 应用props中的样式
  if (props.maxWidth && !props.fluid) {
    styles.maxWidth =
      typeof props.maxWidth === 'number'
        ? `${props.maxWidth}px`
        : props.maxWidth;
  }

  if (props.padding) {
    styles.padding =
      typeof props.padding === 'number' ? `${props.padding}px` : props.padding;
  }

  if (props.backgroundImage) {
    styles.backgroundImage = `url(${props.backgroundImage})`;
    styles.backgroundSize = 'cover';
    styles.backgroundPosition = 'center';
  }

  // 应用StyleConfig中的样式
  if (props.styles) {
    Object.entries(props.styles).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        const cssKey = key.replaceAll(
          /[A-Z]/g,
          (match) => `-${match.toLowerCase()}`,
        );
        styles[cssKey] = value;
      }
    });
  }

  return styles;
});

// 事件处理
const handleClick = (event: MouseEvent) => {
  emit('click', event);
  if (props.onClick) {
    props.onClick();
  }
};
</script>

<script lang="ts">
// 导出组件配置用于Schema
export const ContainerComponentConfig = {
  category: 'layout',
  defaultProps: {
    fluid: false,
    padding: '16px',
  },
  description: '容器组件，用于包含其他组件',
  icon: 'container',
  name: '容器',
  type: 'container',
};
</script>

<template>
  <div
    :id="componentId"
    :class="computedClasses"
    :style="computedStyles"
    :data-component-type="type"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot>
      <div v-if="!hasChildren" class="container-placeholder">
        拖拽组件到这里
      </div>
    </slot>
  </div>
</template>

<style scoped>
/* 响应式样式 */
@media (max-width: 768px) {
  .website-container-component.constrained {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .website-container-component.constrained {
    max-width: 768px;
  }
}

.website-container-component {
  position: relative;
  min-height: 60px;
  transition: all 0.2s ease;
}

.website-container-component.constrained {
  max-width: 1200px;
  margin: 0 auto;
}

.website-container-component.fluid {
  width: 100%;
}

.website-container-component.empty {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
}

.website-container-component.empty:hover {
  background-color: rgb(24 144 255 / 5%);
  border-color: #1890ff;
}

.container-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  font-size: 14px;
  color: #999;
  user-select: none;
}
</style>
