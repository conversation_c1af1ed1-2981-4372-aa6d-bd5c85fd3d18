/**
 * Website Components Registry
 * 注册所有可用于网站构建器的组件
 */

// 内容组件
import TextComponent, {
  TextComponentConfig,
} from './content/TextComponent.vue';
// 布局组件
import ContainerComponent, {
  ContainerComponentConfig,
} from './layout/ContainerComponent.vue';

// 组件类型映射
export const WEBSITE_COMPONENTS = {
  container: ContainerComponent,
  text: TextComponent,
} as const;

// 组件配置映射
export const COMPONENT_CONFIGS = {
  container: ContainerComponentConfig,
  text: TextComponentConfig,
} as const;

// 组件类型
export type ComponentType = keyof typeof WEBSITE_COMPONENTS;

// 获取组件
export function getComponent(type: ComponentType) {
  return WEBSITE_COMPONENTS[type];
}

// 获取组件配置
export function getComponentConfig(type: ComponentType) {
  return COMPONENT_CONFIGS[type];
}

// 获取所有组件类型
export function getAllComponentTypes(): ComponentType[] {
  return Object.keys(WEBSITE_COMPONENTS) as ComponentType[];
}

// 获取所有分类
export function getAllCategories(): string[] {
  const categories = new Set<string>();
  getAllComponentTypes().forEach((type) => {
    const config = COMPONENT_CONFIGS[type];
    categories.add(config.category);
  });
  return [...categories];
}

// 按分类获取组件
export function getComponentsByCategory(category: string): ComponentType[] {
  return getAllComponentTypes().filter((type) => {
    const config = COMPONENT_CONFIGS[type];
    return config.category === category;
  });
}

// 导出组件
export { ContainerComponent, TextComponent };

// 导出配置
export { ContainerComponentConfig, TextComponentConfig };

// 默认导出
export default WEBSITE_COMPONENTS;
