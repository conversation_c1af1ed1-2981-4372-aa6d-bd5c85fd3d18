<script setup lang="ts">
import { computed } from 'vue';

// 定义组件Props
interface Props {
  className?: string;
  // 文本内容属性
  content?: string;

  editable?: boolean;
  // 基础属性
  id?: string;
  // 事件属性
  onClick?: () => void;
  placeholder?: string;

  // 响应式属性
  responsive?: Record<string, any>;
  // 样式属性
  styles?: Record<string, string>;

  tag?: 'div' | 'p' | 'span';

  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  className: undefined,
  content: '',
  editable: false,
  id: undefined,
  onClick: undefined,
  placeholder: undefined,
  responsive: undefined,
  styles: undefined,
  tag: 'div',
  type: 'text',
});

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent];
  update: [content: string];
}>();

// 计算属性
const componentId = computed(() => props.id || `text-${Date.now()}`);

const displayContent = computed(() => {
  if (!props.content && props.placeholder) {
    return props.placeholder;
  }
  return props.content || '请输入文本内容';
});

const computedClasses = computed(() => {
  const classes: string[] = ['website-text-component'];

  if (props.className) {
    classes.push(props.className);
  }

  if (props.editable) {
    classes.push('editable');
  }

  if (!props.content && props.placeholder) {
    classes.push('placeholder');
  }

  return classes;
});

const computedStyles = computed(() => {
  if (!props.styles) return {};

  // 将StyleConfig转换为CSS样式对象
  const styles: Record<string, any> = {};

  Object.entries(props.styles).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      // 转换驼峰命名为短横线命名
      const cssKey = key.replaceAll(
        /[A-Z]/g,
        (match) => `-${match.toLowerCase()}`,
      );
      styles[cssKey] = value;
    }
  });

  return styles;
});

// 事件处理
const handleClick = (event: MouseEvent) => {
  emit('click', event);
  if (props.onClick) {
    props.onClick();
  }
};
</script>

<script lang="ts">
// 导出组件配置用于Schema
export const TextComponentConfig = {
  category: 'content',
  defaultProps: {
    content: '文本内容',
    editable: true,
    tag: 'div',
  },
  description: '可编辑的文本组件',
  icon: 'text',
  name: '文本',
  type: 'text',
};
</script>

<template>
  <component
    :is="tag"
    :id="componentId"
    :class="computedClasses"
    :style="computedStyles"
    :data-component-type="type"
    :data-editable="editable"
    v-bind="$attrs"
    @click="handleClick"
  >
    {{ displayContent }}
  </component>
</template>

<style scoped>
/* 响应式样式 */
@media (max-width: 768px) {
  .website-text-component {
    font-size: 14px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .website-text-component {
    font-size: 16px;
  }
}

@media (min-width: 1025px) {
  .website-text-component {
    font-size: 18px;
  }
}

.website-text-component {
  outline: none;
  transition: all 0.2s ease;
}

.website-text-component.editable {
  min-height: 20px;
  cursor: pointer;
}

.website-text-component.editable:hover {
  background-color: rgb(24 144 255 / 10%);
  border: 1px dashed #1890ff;
}

.website-text-component.placeholder {
  font-style: italic;
  color: #999;
}
</style>
