{"title": "System Management", "dept": {"name": "Department", "title": "Department Management", "deptName": "Department Name", "status": "Status", "createTime": "Create Time", "remark": "Remark", "operation": "Operation", "orderNo": "Order No", "parentDept": "Parent Department"}, "menu": {"title": "Menu Management", "parent": "<PERSON><PERSON>", "menuTitle": "Title", "menuName": "<PERSON>u Name", "name": "<PERSON><PERSON>", "type": "Type", "typeCatalog": "Catalog", "typeMenu": "<PERSON><PERSON>", "typeButton": "<PERSON><PERSON>", "typeLink": "Link", "typeEmbedded": "Embedded", "icon": "Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "path": "Route Path", "component": "Component", "status": "Status", "authCode": "Auth Code", "badge": "Badge", "operation": "Operation", "linkSrc": "Link Address", "affixTab": "Affix In Tabs", "keepAlive": "Keep Alive", "hideInMenu": "Hide In Menu", "hideInTab": "Hide In Tabbar", "hideChildrenInMenu": "Hide Children In Menu", "hideInBreadcrumb": "Hide In Breadcrumb", "advancedSettings": "Other Settings", "activePathMustExist": "The path could not find a valid menu", "activePathHelp": "When jumping to the current route, \nthe menu path that needs to be activated must be specified when it does not display in the navigation menu.", "badgeType": {"title": "Badge Type", "dot": "Dot", "normal": "Text", "none": "None"}, "badgeVariants": "Badge Style"}, "role": {"title": "Role Management", "list": "Role List", "name": "Role", "roleName": "Role Name", "id": "Role ID", "status": "Status", "remark": "Remark", "createTime": "Creation Time", "operation": "Operation", "permissions": "Permissions", "setPermissions": "Permissions"}, "permission": {"title": "Permission Management", "list": "Permission List", "name": "Permission", "status": "Status", "createTime": "Creation Time", "operation": "Operation", "fields": {"name": "Permission Name", "code": "Permission Code", "description": "Description"}}, "tenant": {"title": "Tenant Management", "list": "Tenant List", "name": "Tenant", "tenantName": "Tenant Name", "tenantCode": "Tenant Code", "id": "Tenant ID", "website": "Website", "domain": "Domain", "status": "Status", "metadata": "<PERSON><PERSON><PERSON>", "industry": "Industry", "size": "Size", "region": "Region", "createTime": "Creation Time", "updateTime": "Update Time", "operation": "Operation", "datasource": "Data Source", "datasourceName": "Data Source Name", "datasourceUrl": "Data Source URL", "datasourceUrlRequired": "Please enter data source URL", "testConnection": "Test Connection", "connectionSuccess": "Connection Successful", "connectionFailed": "Connection Failed", "viewMenus": "View Available Menus", "syncMenus": "Sync Menus", "syncMenusConfirm": "Are you sure you want to sync menus for tenant {0}?", "syncingMenus": "Syncing menus for tenant {0}...", "syncMenusSuccess": "Menu synchronization successful", "syncMenusError": "Menu synchronization failed", "availableMenus": "Available Menus", "availableMenusDesc": "All menus available to this tenant"}, "user": {"title": "User Management", "list": "User List", "name": "User", "username": "Username", "realName": "Real Name", "email": "Email", "password": "Password", "passwordPlaceholder": "Please enter password", "passwordHelp": "Password is not required when editing user", "confirmPassword": "Confirm Password", "avatar": "Avatar", "status": "Status", "createTime": "Creation Time", "updateTime": "Update Time", "lastLoginTime": "Last Login Time", "operation": "Operation", "roles": "Roles", "selectRoles": "Select Roles", "phoneNumber": "Phone Number", "idCardNumber": "ID Card Number", "openid": "WeChat OpenID", "unionid": "WeChat UnionID", "adminRemark": "Admin Remark", "tenantId": "Tenant ID"}, "featureTemplate": {"title": "Feature Template Management", "list": "Feature Template List", "name": "Feature Template", "templateName": "Template Name", "templateCode": "Template Code", "features": "Included Features", "isActive": "Active", "createdAt": "Created At", "updatedAt": "Updated At", "operation": "Operation", "editTemplate": "Edit Template", "createTemplate": "Create Template", "featureSettings": "Feature Settings", "featureEnabled": "Enabled", "featureQuota": "<PERSON><PERSON><PERSON>", "unlimitedQuota": "Unlimited", "addFeature": "Add Feature", "removeFeature": "Remove Feature", "featureCode": "Feature Code", "featureConfig": "Feature Configuration", "viewMenus": "View Associated Menus", "associatedMenus": "Associated Menus", "associatedMenusDesc": "All menus associated with this feature template", "noAssociatedMenus": "No associated menus"}, "tenantFeature": {"title": "Tenant Feature Management", "list": "Tenant Feature List", "name": "Tenant Feature", "selectTenant": "Select Tenant", "featureCode": "Feature Code", "enabled": "Enabled", "expiresAt": "Expires At", "quota": "<PERSON><PERSON><PERSON>", "usedQuota": "Used Quota", "remaining": "Remaining", "unlimited": "Unlimited", "operation": "Operation", "enable": "Enable", "disable": "Disable", "applyTemplate": "Apply Template", "selectTemplate": "Select Template", "config": "Configuration", "noExpiration": "No Expiration", "setExpiration": "Set Expiration", "setQuota": "<PERSON> Quota", "dashboard": "Feature Dashboard", "manage": "Manage Features"}, "tenantConfig": {"title": "Tenant Configuration Management", "list": "Configuration List", "name": "Tenant Configuration", "selectTenant": "Select Tenant", "selectCategory": "Select Category", "category": "Category", "categoryName": "Category Name", "categoryCode": "Category Code", "description": "Description", "requiredFeature": "Required Feature", "operation": "Operation", "saveConfig": "Save Configuration", "testConfig": "Test Configuration", "configCenter": "Configuration Center", "manage": "Manage Configs", "email": {"title": "Email Service", "provider": "Provider", "host": "SMTP Server", "port": "Port", "username": "Username", "password": "Password", "fromName": "From Name", "fromEmail": "From Email", "enableSSL": "Enable SSL"}, "sms": {"title": "SMS Service", "provider": "Provider", "accessKey": "Access Key", "secretKey": "Secret Key", "signName": "Sign Name", "templateCode": "Template Code"}, "oss": {"title": "Object Storage", "provider": "Provider", "endpoint": "Endpoint", "accessKey": "Access Key", "secretKey": "Secret Key", "bucket": "Bucket", "region": "Region", "domain": "Custom Domain"}, "payment": {"title": "Payment Service", "provider": "Provider", "appId": "App ID", "merchantId": "Merchant ID", "privateKey": "Private Key", "publicKey": "Public Key", "notifyUrl": "Notify URL"}}, "featureCode": {"title": "Feature Code Management", "list": "Feature Code List", "name": "Feature Code", "code": "Code", "module": "<PERSON><PERSON><PERSON>", "description": "Description", "isActive": "Active", "sortOrder": "Sort Order", "metadata": "<PERSON><PERSON><PERSON>", "createdAt": "Created At", "updatedAt": "Updated At", "operation": "Operation", "manageMenus": "Manage Menus", "selectMenus": "Select Menus", "selectMenusDesc": "Please select menus to associate with this feature code"}}